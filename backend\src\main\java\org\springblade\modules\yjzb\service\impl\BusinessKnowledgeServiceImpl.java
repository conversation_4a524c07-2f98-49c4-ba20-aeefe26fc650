/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.service.impl;

import org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.vo.BusinessKnowledgeVO;
import org.springblade.modules.yjzb.excel.BusinessKnowledgeExcel;
import org.springblade.modules.yjzb.mapper.BusinessKnowledgeMapper;
import org.springblade.modules.yjzb.service.IBusinessKnowledgeService;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;
import org.springblade.modules.yjzb.service.cache.BusinessKnowledgeRuleCache;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;
import jakarta.annotation.PostConstruct;

/**
 * 业务知识 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@Service
public class BusinessKnowledgeServiceImpl extends BaseServiceImpl<BusinessKnowledgeMapper, BusinessKnowledgeEntity>
        implements IBusinessKnowledgeService {

    private final BusinessKnowledgeRuleCache businessKnowledgeRuleCache;

    public BusinessKnowledgeServiceImpl(@Lazy BusinessKnowledgeRuleCache businessKnowledgeRuleCache) {
        this.businessKnowledgeRuleCache = businessKnowledgeRuleCache;
    }

    @PostConstruct
    public void initCache() {
        try {
            businessKnowledgeRuleCache.rebuildWith(this.list());
        } catch (Exception ignore) {
        }
    }

    @Override
    public IPage<BusinessKnowledgeVO> selectBusinessKnowledgePage(IPage<BusinessKnowledgeVO> page,
            BusinessKnowledgeVO businessKnowledge) {
        return page.setRecords(baseMapper.selectBusinessKnowledgePage(page, businessKnowledge));
    }

    @Override
    public boolean save(BusinessKnowledgeEntity entity) {
        boolean r = super.save(entity);
        if (r) {
            BusinessKnowledgeEntity fresh = this.getById(entity.getId());
            businessKnowledgeRuleCache.upsertEntity(fresh);
        }
        return r;
    }

    @Override
    public boolean updateById(BusinessKnowledgeEntity entity) {
        boolean r = super.updateById(entity);
        if (r) {
            BusinessKnowledgeEntity fresh = this.getById(entity.getId());
            businessKnowledgeRuleCache.upsertEntity(fresh);
        }
        return r;
    }

    @Override
    public boolean saveOrUpdate(BusinessKnowledgeEntity entity) {
        boolean r = super.saveOrUpdate(entity);
        if (r) {
            BusinessKnowledgeEntity fresh = this.getById(entity.getId());
            businessKnowledgeRuleCache.upsertEntity(fresh);
        }
        return r;
    }

    @Override
    public boolean deleteLogic(List<Long> ids) {
        boolean r = super.deleteLogic(ids);
        if (r) {
            for (Long id : ids) {
                businessKnowledgeRuleCache.removeEntity(id);
            }
        }
        return r;
    }

    @Override
    public List<BusinessKnowledgeExcel> exportBusinessKnowledge(Wrapper<BusinessKnowledgeEntity> queryWrapper) {
        List<BusinessKnowledgeExcel> businessKnowledgeList = baseMapper.exportBusinessKnowledge(queryWrapper);
        // businessKnowledgeList.forEach(businessKnowledge -> {
        // businessKnowledge.setTypeName(DictCache.getValue(DictEnum.YES_NO,
        // BusinessKnowledge.getType()));
        // });
        return businessKnowledgeList;
    }

}
