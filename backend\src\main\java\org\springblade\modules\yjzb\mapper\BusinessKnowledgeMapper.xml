<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.BusinessKnowledgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessKnowledgeResultMap" type="org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity">
        <result column="id" property="id"/>
        <result column="knowledge_title" property="knowledgeTitle"/>
        <result column="applicable_indicators" property="applicableIndicators"/>
        <result column="knowledge_type" property="knowledgeType"/>
        <result column="knowledge_content" property="knowledgeContent"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectBusinessKnowledgePage" resultMap="businessKnowledgeResultMap">
        select * from yjzb_business_knowledge where is_deleted = 0
    </select>


    <select id="exportBusinessKnowledge" resultType="org.springblade.modules.yjzb.excel.BusinessKnowledgeExcel">
        SELECT * FROM yjzb_business_knowledge ${ew.customSqlSegment}
    </select>

</mapper>
