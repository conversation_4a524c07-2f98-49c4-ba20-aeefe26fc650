/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesDetailEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO;
import org.springblade.modules.yjzb.pojo.dto.IndicatorValuesDetailDTO;
import java.util.List;
import java.math.BigDecimal;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

/**
 * 指标数据明细 服务类
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface IIndicatorValuesDetailService extends IService<IndicatorValuesDetailEntity> {

    /**
     * 分页查询指标数据明细
     *
     * @param page                  分页参数
     * @param indicatorValuesDetail 查询条件
     * @return 分页结果
     */
    IPage<IndicatorValuesDetailVO> selectIndicatorValuesDetailPage(IPage<IndicatorValuesDetailVO> page,
            IndicatorValuesDetailEntity indicatorValuesDetail);

    /**
     * 根据指标ID和期间查询明细列表
     *
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 明细列表
     */
    List<IndicatorValuesDetailVO> selectByIndicatorIdAndPeriod(Long indicatorId, String period);

    /**
     * 根据指标ID和期间区间查询明细列表
     *
     * @param indicatorId 指标ID
     * @param startPeriod 开始期间
     * @param endPeriod   结束期间
     * @return 明细列表
     */
    List<IndicatorValuesDetailVO> selectByIndicatorIdAndPeriodRange(Long indicatorId, String startPeriod,
            String endPeriod);

    /**
     * 根据指标ID和期间计算总金额
     *
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 总金额
     */
    BigDecimal sumAmountByIndicatorIdAndPeriod(Long indicatorId, String period);

    /**
     * 根据指标ID和期间区间计算总金额
     *
     * @param indicatorId 指标ID
     * @param startPeriod 开始期间
     * @param endPeriod   结束期间
     * @return 总金额
     */
    BigDecimal sumAmountByIndicatorIdAndPeriodRange(Long indicatorId, String startPeriod, String endPeriod);

    /**
     * 根据分类统计金额
     *
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 分类统计结果
     */
    List<IndicatorValuesDetailVO> sumAmountByCategory(Long indicatorId, String period);

    /**
     * 按部门统计金额
     *
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 部门聚合结果
     */
    List<IndicatorValuesDetailVO> sumAmountByDept(Long indicatorId, String period);

    /**
     * 批量插入指标数据明细
     *
     * @param list 明细列表
     * @return 是否成功
     */
    boolean batchInsert(List<IndicatorValuesDetailEntity> list);

    /**
     * 根据凭证号查询明细
     *
     * @param voucherNo 凭证号
     * @return 明细列表
     */
    List<IndicatorValuesDetailVO> selectByVoucherNo(String voucherNo);

    /**
     * 保存指标数据明细
     *
     * @param indicatorValuesDetailDTO 明细DTO
     * @return 是否成功
     */
    boolean saveIndicatorValuesDetail(IndicatorValuesDetailDTO indicatorValuesDetailDTO);

    /**
     * 更新指标数据明细
     *
     * @param indicatorValuesDetailDTO 明细DTO
     * @return 是否成功
     */
    boolean updateIndicatorValuesDetail(IndicatorValuesDetailDTO indicatorValuesDetailDTO);

    /**
     * 删除指标数据明细
     *
     * @param id 明细ID
     * @return 是否成功
     */
    boolean deleteIndicatorValuesDetail(Long id);

    /**
     * 批量删除指标数据明细
     *
     * @param ids 明细ID列表
     * @return 是否成功
     */
    boolean deleteIndicatorValuesDetailBatch(List<Long> ids);

    /**
     * 预览费用明细数据文件
     *
     * @param file Excel文件
     * @return 预览数据列表
     */
    List<Map<String, Object>> previewFeeDetailFile(MultipartFile file);

    /**
     * 导入费用明细数据文件
     *
     * @param indicatorId 指标ID
     * @param file        Excel文件
     * @return 导入结果
     */
    Map<String, Object> importFeeDetailFile(Long indicatorId, MultipartFile file);

    /**
     * 根据指标ID、分类和期间查询金额
     *
     * @param indicatorId 指标ID
     * @param category    分类
     * @param period      期间
     * @return 金额
     */
    BigDecimal sumAmountByIndicatorIdAndCategoryAndPeriod(Long indicatorId, String category, String period);

    /**
     * 根据指标ID和分类查询指定期间范围内的金额
     *
     * @param indicatorId 指标ID
     * @param category    分类
     * @param startPeriod 开始期间
     * @param endPeriod   结束期间
     * @return 金额
     */
    BigDecimal sumAmountByIndicatorIdAndCategoryAndPeriodRange(Long indicatorId, String category, String startPeriod,
            String endPeriod);

    /**
     * 根据指标ID和分类查询指定年份的所有月份数据
     *
     * @param indicatorId 指标ID
     * @param category    分类
     * @param year        年份
     * @return 月份数据列表
     */
    List<Map<String, Object>> selectMonthlyDataByIndicatorIdAndCategoryAndYear(Long indicatorId, String category,
            Integer year);

}
