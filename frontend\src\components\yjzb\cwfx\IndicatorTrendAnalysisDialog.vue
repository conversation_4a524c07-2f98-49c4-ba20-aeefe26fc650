<template>
  <!-- 指标趋势分析弹框组件 -->
  <el-dialog
    title="AI分析"
    v-model="dialogVisible"
    width="900px"
    destroy-on-close
    @open="onDialogOpen"
    @close="onDialogClose"
    @update:model-value="handleDialogVisibleChange"
  >
    <div v-if="expenseData">
      <div class="analysis-content">
        <!-- 费用基本信息 -->
        <div class="expense-info">
          <h4>{{ expenseData.indicatorName }} - {{ expenseData.period }}</h4>
          <p class="expense-amount">金额：¥{{ formatNumber(expenseData.value) }}</p>
          <p class="expense-desc">说明：{{ expenseData.dataSource }}</p>
        </div>

        <!-- 费用趋势分析图 -->
        <div class="trend-chart-section">
          <h4>费用趋势分析</h4>
          <div id="trendChart" style="width: 100%; height: 320px;"></div>
        </div>

        <!-- AI智能解读 -->
        <div class="ai-analysis-section">
          <div class="ai-header">
            <h4 class="ai-title">🤖 AI智能解读</h4>
            <div class="ai-status-right">
              <template v-if="aiAnalysisContent">
                <el-tag :type="getStatusTagType(aiAnalysisContent.executeStatus)" size="small">
                  {{ getStatusText(aiAnalysisContent.executeStatus) }}
                </el-tag>
                <span v-if="aiAnalysisContent.executeStatus==='RUNNING'" class="ai-status-hint">分析进行中，请稍后查看结果...</span>
              </template>
            </div>
          </div>
          <div class="ai-content">
            <div v-if="aiAnalysisLoading">正在加载AI解读...</div>
            <div v-else-if="!aiAnalysisContent">暂无AI解读，点击"重新分析"开始。</div>
            <template v-else>
              <!-- JSON 展示 -->
              <div v-if="isJsonAnswer" class="json-analysis">
                <div v-for="(item, idx) in aiAnswerItems" :key="idx">
                  <div v-if="item.context" class="context-card">
                    <div class="context-title">总体解读</div>
                    <div class="context-text">{{ item.context }}</div>
                  </div>
                  <div v-else class="forecast-card">
                    <div class="forecast-header">
                      <span class="month-badge">{{ item['预测月份'] || item.month || item.period }}</span>
                      <span class="amount">¥{{ formatNumber(item['预测金额'] || item.amount || 0) }}</span>
                    </div>
                    <div class="reason">{{ item['核心理由'] || item.reason || item.explain }}</div>
                  </div>
                </div>
              </div>

              <!-- Markdown 回退展示 -->
              <div v-else-if="aiAnswerText || aiThinkText" class="analysis-item">
                <div class="analysis-text">
                  <div v-if="aiThinkText" class="think-box">
                    <div class="think-header" @click="aiThinkCollapsed = !aiThinkCollapsed">
                      <span>{{ aiThinkCollapsed ? '展开' : '收起' }}思考过程</span>
                      <i :class="aiThinkCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
                    </div>
                    <div v-show="!aiThinkCollapsed" class="think-content">
                      <pre style="white-space:pre-wrap;word-break:break-word;">{{ aiThinkText }}</pre>
                    </div>
                  </div>
                  <div v-if="aiAnswerText" class="ai-answer" v-html="aiAnswerHtml()"></div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="closeDialog">关闭</el-button>
      <el-button @click="openPromptEditor" :loading="chartLoading">
        <i class="el-icon-edit"></i> 重新分析（可编辑提示词）
      </el-button>
      <!-- <el-button type="primary" @click="exportAnalysis" disabled>导出分析报告</el-button> -->
    </template>
  </el-dialog>

  <!-- 提示词编辑对话框 -->
  <el-dialog v-model="promptDlg.visible" title="AI分析提示词与业务知识" width="80%" append-to-body>
    <div class="prompt-editor" v-loading="promptDlg.loading">
      <div class="left-view">
        <div class="section-title">当前业务规则部分提示词</div>
        <div class="prompt-preview" v-html="promptDlg.previewHtml"></div>
      </div>
      <div class="right-view">
        <div class="section-title">
          当前指标关联的业务知识
          <div class="title-actions">
            <el-button type="primary" size="small" @click="addRootCategory" style="margin-left: 10px;">
              <i class="el-icon-folder-add"></i> 新增根分类
            </el-button>
            <!-- 业务知识新增改为在知识节点上提供“基于此新建”，移除此顶部按钮 -->
          </div>
        </div>
        <div class="business-knowledge-manager">
          <div v-if="promptDlg.treeData.length === 0" class="empty-knowledge">
            暂无业务知识，点击"新增分类"开始添加
          </div>
          <div v-else>
            <!-- 树形结构展示 -->
            <el-tree
              ref="knowledgeTree"
              :data="promptDlg.treeData"
              :props="treeProps"
              :default-expanded-keys="promptDlg.defaultExpandedKeys"
              node-key="id"
              :allow-drop="allowDrop"
              :allow-drag="allowDrag"
              draggable
              @node-click="onTreeNodeClick"
              @node-drop="handleNodeDrop"
              @node-drag-start="handleDragStart"
              @node-drag-enter="handleDragEnter"
              @node-drag-leave="handleDragLeave"
              @node-drag-over="handleDragOver"
              @node-drag-end="handleDragEnd"
              class="knowledge-tree"
            >
              <template #default="{ node, data }">
                <div
                  class="tree-node-content"
                  :class="{ 'applied-knowledge': data.type==='knowledge' && isKnowledgeApplied(data.knowledge) }"
                  @contextmenu.prevent.stop="onNodeContextMenu($event, data)"
                >
                  <div class="node-info">
                    <span class="node-label">{{ data.label }}</span>
                    <span v-if="data.type === 'category'" class="knowledge-count">({{ data.knowledgeCount }}条)</span>
                  </div>
                  <div class="node-actions" v-if="!promptDlg.isDragging">
                    <template v-if="data.type === 'category'">
                      <el-button
                        type="text"
                        size="small"
                        @click.stop="addKnowledgeToCategory(data)"
                        class="add-knowledge-btn"
                        title="新增业务知识"
                      >
                        <i class="el-icon-document-add"></i>
                      </el-button>
                      <el-button
                        type="text"
                        size="small"
                        @click.stop="addSubCategory(data)"
                        class="add-category-btn"
                        title="添加子分类"
                      >
                        <i class="el-icon-folder-add"></i>
                      </el-button>
                      <el-button
                        type="text"
                        size="small"
                        @click.stop="editCategory(data)"
                        class="edit-category-btn"
                        title="编辑分类"
                      >
                        <i class="el-icon-edit"></i>
                      </el-button>
                      <el-button
                        type="text"
                        size="small"
                        @click.stop="deleteCategory(data)"
                        class="delete-category-btn"
                        title="删除分类"
                      >
                        <i class="el-icon-delete"></i>
                      </el-button>
                    </template>
                    <template v-else>
                      <el-button type="text" size="small" @click.stop="createFromKnowledge(data.knowledge)" title="基于此新建">
                        <i class="el-icon-document-add"></i>
                      </el-button>
                      <el-button type="text" size="small" @click.stop="editKnowledge(data.knowledge)">
                        <i class="el-icon-edit"></i>
                      </el-button>
                      <el-button type="text" size="small" @click.stop="deleteKnowledge(data.knowledge)" class="delete-btn">
                        <i class="el-icon-delete"></i>
                      </el-button>
                    </template>
                  </div>
                </div>
              </template>
            </el-tree>
            <!-- 右键菜单 -->
            <div v-if="promptDlg.contextMenu.visible" class="context-menu" :style="{ left: promptDlg.contextMenu.x + 'px', top: promptDlg.contextMenu.y + 'px' }">
              <template v-if="promptDlg.contextMenu.target && promptDlg.contextMenu.target.type === 'category'">
                <div class="cm-item" @click.stop="cmAddKnowledge(promptDlg.contextMenu.target)">新增业务知识</div>
                <div class="cm-item" @click.stop="cmAddSubCategory(promptDlg.contextMenu.target)">新增子分类</div>
                <div class="cm-item" @click.stop="cmEditCategory(promptDlg.contextMenu.target)">编辑分类</div>
                <div class="cm-item danger" @click.stop="cmDeleteCategory(promptDlg.contextMenu.target)">删除分类</div>
              </template>
              <template v-else-if="promptDlg.contextMenu.target && promptDlg.contextMenu.target.type === 'knowledge'">
                <div class="cm-item" @click.stop="cmEditKnowledge(promptDlg.contextMenu.target)">编辑业务知识</div>
                <div class="cm-item" @click.stop="cmCloneKnowledge(promptDlg.contextMenu.target)">基于此新建</div>
                <div class="cm-item danger" @click.stop="cmDeleteKnowledge(promptDlg.contextMenu.target)">删除业务知识</div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="promptDlg.visible=false">关闭</el-button>
      <el-button type="primary" :loading="chartLoading" @click="confirmPromptAndRun">使用当前提示词重新分析</el-button>
    </template>
  </el-dialog>

  <!-- 新增/编辑业务知识对话框 -->
  <el-dialog
    v-model="promptDlg.showAddKnowledgeDialog"
    :title="promptDlg.editingKnowledge ? '编辑业务知识' : '新增业务知识'"
    width="60%"
    append-to-body
  >
    <el-form :model="promptDlg.editingKnowledge || promptDlg.newKnowledge" label-width="100px">
      <el-form-item label="知识标题" required>
        <el-input
          v-model="(promptDlg.editingKnowledge || promptDlg.newKnowledge).knowledgeTitle"
          placeholder="请输入知识标题"
        />
      </el-form-item>
      <el-form-item label="知识分类" required>
        <el-select
          v-model="(promptDlg.editingKnowledge || promptDlg.newKnowledge).knowledgeCategory"
          placeholder="请选择知识分类"
          filterable
          clearable
        >
          <el-option
            v-for="category in promptDlg.financeCategories"
            :key="category.id"
            :label="category.categoryName"
            :value="category.id"
          >
            <span>{{ category.fullPath || category.categoryName }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务知识类型">
        <el-select
          v-model="(promptDlg.editingKnowledge || promptDlg.newKnowledge).knowledgeCategory"
          placeholder="请选择或输入类型（可选）"
          allow-create
          filterable
          clearable
        >
          <el-option
            v-for="category in promptDlg.categories"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="知识内容" required>
        <el-input
          v-model="(promptDlg.editingKnowledge || promptDlg.newKnowledge).knowledgeContent"
          type="textarea"
          :rows="6"
          placeholder="请输入知识内容"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="promptDlg.showAddKnowledgeDialog = false">取消</el-button>
      <el-button type="primary" @click="saveKnowledge">保存</el-button>
    </template>
  </el-dialog>

  <!-- 新增分类对话框 -->
  <el-dialog
    v-model="promptDlg.showAddCategoryDialog"
    title="新增分类"
    width="400px"
    append-to-body
  >
    <el-form label-width="80px">
      <el-form-item label="分类名称" required>
        <el-input
          v-model="promptDlg.newCategoryName"
          placeholder="请输入分类名称"
          @keyup.enter="addSimpleCategory"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="promptDlg.showAddCategoryDialog = false">取消</el-button>
      <el-button type="primary" @click="addSimpleCategory">确定</el-button>
    </template>
  </el-dialog>

  <!-- 分类管理对话框 -->
  <el-dialog
    v-model="promptDlg.showCategoryDialog"
    :title="promptDlg.editingCategory ? '编辑分类' : '新增分类'"
    width="50%"
    append-to-body
  >
    <el-form :model="promptDlg.editingCategory || promptDlg.newCategory" label-width="100px">
      <el-form-item label="分类名称" required>
        <el-input
          v-model="(promptDlg.editingCategory || promptDlg.newCategory).categoryName"
          placeholder="请输入分类名称"
        />
      </el-form-item>
      <el-form-item label="分类编码" required>
        <el-input
          v-model="(promptDlg.editingCategory || promptDlg.newCategory).categoryCode"
          placeholder="请输入分类编码"
        />
      </el-form-item>
      <el-form-item label="父分类" v-if="!promptDlg.editingCategory || promptDlg.editingCategory.parentId !== 0">
        <el-select
          v-model="(promptDlg.editingCategory || promptDlg.newCategory).parentId"
          placeholder="请选择父分类"
          filterable
          clearable
        >
          <el-option
            :key="0"
            label="根分类"
            :value="0"
          />
          <el-option
            v-for="category in promptDlg.financeCategories"
            :key="category.id"
            :label="category.fullPath || category.categoryName"
            :value="category.id"
            :disabled="promptDlg.editingCategory && category.id === promptDlg.editingCategory.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="排序">
        <el-input-number
          v-model="(promptDlg.editingCategory || promptDlg.newCategory).sort"
          :min="1"
          :max="999"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="(promptDlg.editingCategory || promptDlg.newCategory).status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="(promptDlg.editingCategory || promptDlg.newCategory).remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="promptDlg.showCategoryDialog = false">取消</el-button>
      <el-button type="primary" @click="saveCategory" :loading="promptDlg.loading">保存</el-button>
    </template>
  </el-dialog>
</template>


<script>
import { ref, reactive, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getListByIndicator,
  predictAndAnalyzeByIndicator,
  getLatestAiByIndicator,
  predictAndAnalyzeByIndicatorWithPrompt,
  getIndicatorPromptPreview,
  getIndicatorBusinessRules,
  getBusinessKnowledgeByCategory,
  saveBusinessKnowledge,
  deleteBusinessKnowledge,
  getBusinessKnowledgeCategories,
  getFinanceCategories,
  updateBusinessKnowledgeCategory
} from "@/api/yjzb/indicatorValues"
import { addCategory as apiAddCategory, updateCategory as apiUpdateCategory, removeCategory as apiRemoveCategory } from "@/api/finance/knowledge"
import { getByIndicatorAndYear } from "@/api/yjzb/indicatorAnnualBudget"

export default {
  name: 'IndicatorTrendAnalysisDialog',
  props: {
    // 控制弹框显示/隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 费用数据
    expenseData: {
      type: Object,
      default: null
    },
    // 筛选表单（用于获取过滤空值等设置）
    filterForm: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'close'],
  setup(props, { emit }) {
    // 响应式数据
    const dialogVisible = ref(false)
    const chartInstance = ref(null)
    const chartLoading = ref(false)

    // AI分析相关数据
    const aiAnalysisLoading = ref(false)
    const aiAnalysisContent = ref(null)
    const aiThinkCollapsed = ref(true)
    const aiThinkText = ref('')
    const aiAnswerText = ref('')
    const isJsonAnswer = ref(false)
    const aiAnswerItems = ref([])

    // 折叠面板激活的分类
    const activeCategories = ref([])

    // 树形组件配置
    const treeProps = {
      children: 'children',
      label: 'label'
    }

    // 提示词编辑对话框状态
    const promptDlg = reactive({
      visible: false,
      preview: '',
      previewHtml: '',
      businessRules: [], // 业务知识列表
      loading: false,
      businessKnowledgeByCategory: {},
      categories: [],
      financeCategories: [], // 财务分类列表
      treeData: [], // 树形数据
      defaultExpandedKeys: [], // 默认展开的节点
      isDragging: false, // 是否正在拖拽
      editingKnowledge: null,
      showAddKnowledgeDialog: false,
      showAddCategoryDialog: false,
      newCategoryName: '',
      // 分类管理相关
      showCategoryDialog: false,
      editingCategory: null,
      newCategory: {
        categoryName: '',
        categoryCode: '',
        parentId: 0,
        sort: 1,
        status: 1,
        remark: ''
      },
      newKnowledge: {
        knowledgeTitle: '',
        knowledgeContent: '',
        knowledgeCategory: null,
        applicableIndicators: '',
        knowledgeType: 1
      },
      contextMenu: {
        visible: false,
        x: 0,
        y: 0,
        target: null
      }
    })

    // 打开提示词编辑器：拉取预览和业务知识列表
    const openPromptEditor = async () => {
      if (!props.expenseData) return
      try {
        promptDlg.loading = true
        const indicatorId = props.expenseData.indicatorId
        const period = props.expenseData.period || props.filterForm.selectedMonth

        // 并行获取提示词预览、业务知识列表、分类数据和财务分类
        const [promptResp, rulesResp, categoryResp, categoriesResp, financeCategoriesResp] = await Promise.all([
          getIndicatorPromptPreview(indicatorId, period),
          getIndicatorBusinessRules(indicatorId),
          getBusinessKnowledgeByCategory(indicatorId),
          getBusinessKnowledgeCategories(indicatorId),
          getFinanceCategories()
        ])

        const text = promptResp?.data?.data || ''
        const rules = rulesResp?.data?.data || []
        const categoryData = categoryResp?.data?.data || {}
        const categories = categoriesResp?.data?.data || []
        const financeCategories = financeCategoriesResp?.data?.data || []

        promptDlg.preview = text
        promptDlg.previewHtml = buildPreviewHtml(text, categoryData, financeCategories)
        promptDlg.businessRules = rules
        promptDlg.businessKnowledgeByCategory = categoryData
        promptDlg.categories = categories
        promptDlg.financeCategories = financeCategories

        // 构建树形数据
        buildTreeData(financeCategories, categoryData)

        promptDlg.visible = true
      } catch (e) {
        console.error('获取提示词预览失败:', e)
        promptDlg.preview = ''
        promptDlg.previewHtml = ''
        promptDlg.businessRules = ['获取业务知识失败']
        promptDlg.businessKnowledgeByCategory = {}
        promptDlg.categories = []
        promptDlg.financeCategories = []
        promptDlg.treeData = []
        promptDlg.visible = true
      } finally {
        promptDlg.loading = false
      }
    }

    // 规则高亮：将“业务规则段落（含替换后的列表）”以绿色边框/浅绿背景强调
    const buildPreviewHtml = (txt, knowledgeByCategory, financeCategories) => {
      if (!txt) return ''
      // 简单识别：在“以下为与当前指标相关的归因类业务规则”与“推荐输出结构”之间的文本标记
      const startKey = '## 关键业务特点与规律'
      const endKey = '## 预测步骤'
      const sIdx = txt.indexOf(startKey)
      const eIdx = txt.indexOf(endKey)
      if (sIdx >= 0 && eIdx > sIdx) {
        const head = txt.substring(0, sIdx)
        // 中段替换为“按创建时间的业务知识清单：- **标题**\n内容”
        const formatted = buildKnowledgeListHtml(knowledgeByCategory, financeCategories)
        const mid = `${startKey}\n\n${formatted}`
        const tail = txt.substring(eIdx)
        return [head,
          `<div style="border:1px solid #67C23A;background:rgba(103,194,58,0.08);padding:10px;border-radius:6px;white-space:pre-wrap;">${escapeHtml(mid)}</div>`,
          `<div style="white-space:pre-wrap;">${escapeHtml(tail)}</div>`
        ].join('')
      }
      // 未找到锚点，直接在结尾追加格式化业务知识
      const formatted = buildKnowledgeListHtml(knowledgeByCategory, financeCategories)
      const merged = `${txt}\n\n${formatted}`
      return `<div style="white-space:pre-wrap;">${escapeHtml(merged)}</div>`
    }

    const escapeHtml = (s) => {
      return String(s)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
    }

    // 构建“按创建时间”的业务知识清单，格式：- **标题**\n内容
    const buildKnowledgeListHtml = (knowledgeByCategory, financeCategories) => {
      try {
        const list = []
        const pushUnique = (k) => {
          if (!k || !k.id) return
          if (list.find(x => x.id === k.id)) return
          list.push(k)
        }
        if (knowledgeByCategory && typeof knowledgeByCategory === 'object') {
          Object.values(knowledgeByCategory).forEach(arr => {
            if (Array.isArray(arr)) arr.forEach(pushUnique)
          })
        }
        // 若通过分类更稳妥：
        if (Array.isArray(financeCategories) && financeCategories.length > 0) {
          financeCategories.forEach(cat => {
            const name = cat.fullPath || cat.categoryName
            const byName = knowledgeByCategory && knowledgeByCategory[name]
            if (Array.isArray(byName)) byName.forEach(pushUnique)
          })
        }

        const toTs = (k) => {
          const v = k.createTime || k.createdAt || k.createdTime || k.gmtCreate || k.addTime
          return v ? new Date(v).getTime() : 0
        }
        list.sort((a,b) => toTs(a) - toTs(b))

        const lines = list.map(k => {
          const title = k.knowledgeTitle || ''
          const content = k.knowledgeContent || ''
          return `- **${title}**\n${content}`
        })

        return lines.join('\n\n') || ''
      } catch (e) {
        return ''
      }
    }

    // 确认并使用当前提示词触发分析
    const confirmPromptAndRun = async () => {
      if (!props.expenseData) return
      try {
        chartLoading.value = true
        const indicatorId = props.expenseData.indicatorId
        const period = props.expenseData.period || props.filterForm.selectedMonth
        // 使用原有的分析方法，不传自定义提示词
        const resp = await predictAndAnalyzeByIndicator(indicatorId, period)
        if (resp?.data?.success) {
          // 即时拉取一次 + 开启轮询
          await loadLatestAiAnalysisByIndicator()
          startMinutePolling()
          promptDlg.visible = false
        }
      } catch (e) {
        console.error('触发分析失败:', e)
      } finally {
        chartLoading.value = false
        await renderTrendChart()
      }
    }

    // 业务知识管理方法
    const editKnowledge = (knowledge) => {
      promptDlg.editingKnowledge = { ...knowledge }
      promptDlg.showAddKnowledgeDialog = true
    }

    // 基于现有业务知识创建新业务知识
    const createFromKnowledge = (base) => {
      if (!base) return
      promptDlg.newKnowledge = {
        knowledgeTitle: `${base.knowledgeTitle}（副本）`,
        knowledgeContent: base.knowledgeContent || '',
        knowledgeCategory: base.knowledgeCategory ?? null,
        applicableIndicators: base.applicableIndicators || (props.expenseData?.indicatorId?.toString() || ''),
        knowledgeType: base.knowledgeType ?? 1
      }
      promptDlg.editingKnowledge = null
      promptDlg.showAddKnowledgeDialog = true
    }

    const deleteKnowledge = async (knowledge) => {
      try {
        await deleteBusinessKnowledge(knowledge.id)
        // 重新加载数据
        await refreshBusinessKnowledge()
        // 重新构建树形数据
        buildTreeData(promptDlg.financeCategories, promptDlg.businessKnowledgeByCategory)
        // 重新生成提示词预览
        await refreshPromptPreview()
        ElMessage.success('删除成功')
      } catch (e) {
        console.error('删除业务知识失败:', e)
        ElMessage.error('删除失败')
      }
    }

    const addKnowledgeToCategory = (categoryData) => {
      // 适配树形结构数据
      const categoryId = categoryData.categoryId || categoryData.id
      const categoryName = categoryData.categoryName || categoryData.label || categoryData

      promptDlg.newKnowledge = {
        knowledgeTitle: '',
        knowledgeContent: '',
        knowledgeCategory: categoryId,
        applicableIndicators: props.expenseData?.indicatorId?.toString() || '',
        knowledgeType: 1
      }
      promptDlg.editingKnowledge = null
      promptDlg.showAddKnowledgeDialog = true
    }

    const showAddCategoryDialog = () => {
      promptDlg.newCategoryName = ''
      promptDlg.showAddCategoryDialog = true
    }

    // 分类管理方法
    const addRootCategory = () => {
      promptDlg.newCategory = {
        categoryName: '',
        categoryCode: '',
        parentId: 0,
        sort: 1,
        status: 1,
        remark: ''
      }
      promptDlg.editingCategory = null
      promptDlg.showCategoryDialog = true
    }

    const addSubCategory = (parentCategoryData) => {
      promptDlg.newCategory = {
        categoryName: '',
        categoryCode: '',
        parentId: parentCategoryData.categoryId || 0,
        sort: 1,
        status: 1,
        remark: ''
      }
      promptDlg.editingCategory = null
      promptDlg.showCategoryDialog = true
    }

    const editCategory = (categoryData) => {
      // 根据categoryId获取完整的分类信息
      const category = promptDlg.financeCategories.find(c => c.id === categoryData.categoryId)
      if (category) {
        promptDlg.editingCategory = {
          id: category.id,
          categoryName: category.categoryName,
          categoryCode: category.categoryCode,
          parentId: category.parentId || 0,
          sort: category.sort || 1,
          status: category.status || 1,
          remark: category.remark || ''
        }
        promptDlg.showCategoryDialog = true
      }
    }

    const deleteCategory = async (categoryData) => {
      try {
        // 递归查找树节点
        const findNodeById = (nodes, id) => {
          for (const n of nodes) {
            if (n.id === id) return n
            if (n.children && n.children.length) {
              const found = findNodeById(n.children, id)
              if (found) return found
            }
          }
          return null
        }
        const targetId = `category_${categoryData.categoryId}`
        const targetNode = findNodeById(promptDlg.treeData, targetId)
        const hasChildren = !!(targetNode && targetNode.children && targetNode.children.length > 0)

        if (hasChildren) {
          ElMessage.warning('该分类下还有子分类或业务知识，无法删除')
          return
        }

        await ElMessageBox.confirm(
          `确定要删除分类"${categoryData.label}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        promptDlg.loading = true
        const response = await apiRemoveCategory(categoryData.categoryId.toString())

        if (response.data.success) {
          ElMessage.success('分类删除成功')
          await refreshFinanceCategories()
          await refreshPromptPreview()
        } else {
          ElMessage.error(response.data.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除分类失败:', error)
          ElMessage.error('删除分类失败')
        }
      } finally {
        promptDlg.loading = false
      }
    }

    const saveCategory = async () => {
      try {
        const categoryData = promptDlg.editingCategory || promptDlg.newCategory

        if (!categoryData.categoryName || !categoryData.categoryCode) {
          ElMessage.warning('请填写分类名称和编码')
          return
        }

        promptDlg.loading = true

        let response
        if (promptDlg.editingCategory) {
          response = await apiUpdateCategory(categoryData)
        } else {
          response = await apiAddCategory(categoryData)
        }

        if (response.data.success) {
          ElMessage.success(promptDlg.editingCategory ? '分类更新成功' : '分类创建成功')
          promptDlg.showCategoryDialog = false
          await refreshFinanceCategories()
          await refreshPromptPreview()
        } else {
          ElMessage.error(response.data.msg || '保存失败')
        }
      } catch (error) {
        console.error('保存分类失败:', error)
        ElMessage.error('保存分类失败')
      } finally {
        promptDlg.loading = false
      }
    }

    const refreshFinanceCategories = async () => {
      try {
        const response = await getFinanceCategories()
        const financeCategories = response?.data?.data || []
        promptDlg.financeCategories = financeCategories

        // 重新构建树形数据
        buildTreeData(financeCategories, promptDlg.businessKnowledgeByCategory)
      } catch (error) {
        console.error('刷新财务分类失败:', error)
      }
    }

    const saveKnowledge = async () => {
      try {
        const knowledge = promptDlg.editingKnowledge || promptDlg.newKnowledge
        await saveBusinessKnowledge(knowledge)

        promptDlg.showAddKnowledgeDialog = false
        promptDlg.editingKnowledge = null

        // 重新加载数据
        await refreshBusinessKnowledge()
        // 重新构建树形数据
        buildTreeData(promptDlg.financeCategories, promptDlg.businessKnowledgeByCategory)
        // 重新生成提示词预览
        await refreshPromptPreview()

        ElMessage.success('保存成功')
      } catch (e) {
        console.error('保存业务知识失败:', e)
        ElMessage.error('保存失败')
      }
    }

    const addSimpleCategory = async () => {
      if (!promptDlg.newCategoryName.trim()) {
        ElMessage.warning('请输入分类名称')
        return
      }

      // 添加新分类到列表
      if (!promptDlg.categories.includes(promptDlg.newCategoryName)) {
        promptDlg.categories.push(promptDlg.newCategoryName)
        promptDlg.businessKnowledgeByCategory[promptDlg.newCategoryName] = []
      }

      promptDlg.showAddCategoryDialog = false
      ElMessage.success('分类添加成功')
    }

    const refreshBusinessKnowledge = async () => {
      if (!props.expenseData) return

      try {
        const indicatorId = props.expenseData.indicatorId
        const [categoryResp, categoriesResp] = await Promise.all([
          getBusinessKnowledgeByCategory(indicatorId),
          getBusinessKnowledgeCategories(indicatorId)
        ])

        promptDlg.businessKnowledgeByCategory = categoryResp?.data?.data || {}
        promptDlg.categories = categoriesResp?.data?.data || []
        // 同步重建树形数据，确保节点与数量即时刷新
        buildTreeData(promptDlg.financeCategories, promptDlg.businessKnowledgeByCategory)
      } catch (e) {
        console.error('刷新业务知识失败:', e)
      }
    }

    const refreshPromptPreview = async () => {
      if (!props.expenseData) return

      try {
        const indicatorId = props.expenseData.indicatorId
        const period = props.expenseData.period || props.filterForm.selectedMonth
        const promptResp = await getIndicatorPromptPreview(indicatorId, period)

        const text = promptResp?.data?.data || ''
        promptDlg.preview = text
        promptDlg.previewHtml = buildPreviewHtml(text, promptDlg.businessKnowledgeByCategory, promptDlg.financeCategories)
      } catch (e) {
        console.error('刷新提示词预览失败:', e)
      }
    }

    // 轮询定时器（每分钟查询一次）
    const pollTimer = ref(null)

    // 监听visible属性变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal
    }, { immediate: true })

    // 处理弹框显示状态变化
    const handleDialogVisibleChange = (visible) => {
      emit('update:visible', visible)
    }

    // 弹框打开事件
    const onDialogOpen = async () => {
      if (props.expenseData) {
        await renderTrendChart()
        await loadLatestAiAnalysisByIndicator()
      }
    }

    // 弹框关闭事件
    const onDialogClose = () => {
      if (chartInstance.value) {
        chartInstance.value.dispose()
        chartInstance.value = null
      }
      // 关闭时停止轮询，避免内存泄漏
      stopMinutePolling()
      emit('close')
    }

    // 关闭弹框
    const closeDialog = () => {
      dialogVisible.value = false
    }

    // 格式化数字
    const formatNumber = (value) => {
      if (!value) return '0'
      return parseFloat(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const statusMap = {
        'COMPLETED': 'success',
        'PROCESSING': 'warning',
        'PENDING': 'info',
        'FAILED': 'danger'
      }
      return statusMap[status] || 'info'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        'COMPLETED': '已完成',
        'PROCESSING': '计算中',
        'PENDING': '待计算',
        'FAILED': '失败'
      }
      return statusMap[status] || status
    }

    // 加载最新AI分析结果
    const loadLatestAiAnalysisByIndicator = async () => {
      if (!props.expenseData) return false

      try {
        aiAnalysisLoading.value = true
        const indicatorId = props.expenseData.indicatorId
        const period = props.expenseData.period || props.filterForm.selectedMonth
        const res = await getLatestAiByIndicator(indicatorId, period)
        const entity = res?.data?.data

        if (entity) {
          aiAnalysisContent.value = entity
          parseAiResultText(entity.result || '')
          await nextTick()
          await renderTrendChart()
          return true
        }
        return false
      } catch (e) {
        console.error('加载AI分析失败:', e)
        return false
      } finally {
        aiAnalysisLoading.value = false
      }
    }

    // 解析AI结果文本
    const parseAiResultText = (text) => {
      try {
        const pattern = /<think>([\s\S]*?)<\/think>/i
        const match = typeof text === 'string' ? text.match(pattern) : null
        const body = match && match[1] != null ? text.replace(pattern, '').trim() : (text || '').trim()
        aiThinkText.value = match && match[1] != null ? match[1].trim() : ''

        // 强制要求 JSON：支持 数组 或 对象{context, predict: []}
        isJsonAnswer.value = false
        aiAnswerItems.value = []

        if (body) {
          try {
            const parsed = JSON.parse(body)
            if (Array.isArray(parsed)) {
              isJsonAnswer.value = true
              aiAnswerItems.value = parsed
              aiAnswerText.value = ''
              return
            } else if (parsed && typeof parsed === 'object') {
              const items = []
              if (parsed.context) items.push({ context: parsed.context })
              const pred = Array.isArray(parsed.predict) ? parsed.predict : []
              isJsonAnswer.value = true
              aiAnswerItems.value = items.concat(pred)
              aiAnswerText.value = ''
              return
            }
          } catch (e) {
            // ignore, 统一走报错
          }
        }

        // 非期望JSON：报错并打印返回
        console.error('[AI解析] 返回不符合预期的JSON结构：', body)
        aiAnswerText.value = ''
      } catch (e) {
        aiThinkText.value = ''
        aiAnswerText.value = ''
        isJsonAnswer.value = false
        aiAnswerItems.value = []
        console.error('[AI解析] 解析异常：', e, '原始内容：', text)
      }
    }

    // 渲染AI答案为HTML
    const aiAnswerHtml = () => {
      const text = aiAnswerText.value || ''
      const raw = marked.parse(text, { breaks: true, gfm: true })
      const safe = DOMPurify.sanitize(raw)
      return safe
    }

    // 判断某条知识是否被当前提示词实际引用（简单包含匹配）
    const isKnowledgeApplied = (knowledge) => {
      try {
        if (!knowledge) return false
        const preview = promptDlg.preview || ''
        const content = (knowledge.knowledgeContent || '') + ' ' + (knowledge.knowledgeTitle || '')
        if (!preview || !content) return false
        // 粗略匹配：标题或内容中的10个及以上连续字符在预览中出现
        const text = String(content).replace(/\s+/g, ' ').trim()
        if (text.length < 10) return false
        const sample = text.slice(0, Math.min(30, text.length))
        return preview.includes(sample) || preview.includes(knowledge.knowledgeTitle || '')
      } catch (e) {
        return false
      }
    }

    // 渲染趋势图表
    const renderTrendChart = async () => {
      try {
        chartLoading.value = true
        if (!props.expenseData) return

        const chartDom = document.getElementById('trendChart')
        if (!chartDom) return

        if (chartInstance.value) {
          chartInstance.value.dispose()
        }
        chartInstance.value = echarts.init(chartDom)
        chartInstance.value.showLoading()

        // 解析年份与截止月份
        const period = props.expenseData.period || props.filterForm.selectedMonth
        const [yearStr, monthStr] = (period || '').split('-')
        const year = parseInt(yearStr || String(new Date().getFullYear()), 10)
        const endMonthSelected = Math.min(12, Math.max(1, parseInt(monthStr || '12', 10)))

        // 获取该指标的年度预算
        const indicatorId = props.expenseData.indicatorId
        let annual = 0
        let hasBudgetData = false

        try {
          const budgetRes = await getByIndicatorAndYear(indicatorId, year)
          const bd = budgetRes?.data?.data || {}

          // 检查是否有有效的预算数据
          const mid = bd.midyearBudget != null ? parseFloat(bd.midyearBudget) : 0
          const initB = bd.initialBudget != null ? parseFloat(bd.initialBudget) : 0
          annual = (mid && !isNaN(mid) && mid !== 0) ? mid : (initB && !isNaN(initB) ? initB : 0)

          // 只有当预算值大于0时才认为有预算数据
          hasBudgetData = annual > 0
        } catch (e) {
          console.warn('获取预算数据失败:', e.message)
          // 忽略错误，annual 保持为 0，hasBudgetData 为 false
        }

        const annualCents = Math.round((annual || 0) * 100)

        // 获取该费用指标的当年每月实际值
        const allMonths = Array.from({ length: 12 }, (_, i) => `${year}-${String(i + 1).padStart(2, '0')}`)
        const pageSize = 200
        const params = { period_like: `${year}-` }

        if (props.filterForm.filterEmpty) {
          params.filterEmpty = true
        }

        const resp = await getListByIndicator(1, pageSize, indicatorId, params)
        const records = resp?.data?.data?.records || []
        const monthToValueCents = {}

        records.forEach(r => {
          if (r.period && r.value != null) {
            const v = parseFloat(r.value)
            monthToValueCents[r.period] = Math.round((isNaN(v) ? 0 : v) * 100)
          }
        })

        // 逐月累计（分）
        const monthlyActualsCents = allMonths.map(m => monthToValueCents[m] || 0)

        // 只有当有预算数据时才生成预算系列
        const budgetSeries = []
        if (hasBudgetData) {
          for (let i = 0; i < 12; i++) {
            const accBudgetCents = Math.floor(annualCents * (i + 1) / 12)
            budgetSeries.push(Number((accBudgetCents / 100).toFixed(2)))
          }
        }

        // 实际累计：仅到选中月份，其余置为null
        const actualSeries = new Array(12).fill(null)
        let accActualCentsAtSelected = 0
        for (let i = 0; i < endMonthSelected; i++) {
          accActualCentsAtSelected += monthlyActualsCents[i]
          actualSeries[i] = Number((accActualCentsAtSelected / 100).toFixed(2))
        }

        // 预测累计：从AI解读结果中提取预测数据
        const forecastSeries = new Array(12).fill(null)

        if (isJsonAnswer.value && aiAnswerItems.value && aiAnswerItems.value.length > 0) {
          const monthPredictions = {}
          aiAnswerItems.value.forEach(item => {
            if (item.context) return

            const monthStr = item['预测月份'] || item.month || item.period
            const amountStr = item['预测金额'] || item.amount || '0'

            if (monthStr) {
              let monthNum = 0
              if (monthStr.includes('年') && monthStr.includes('月')) {
                const matches = monthStr.match(/(\d{4})年(\d{1,2})月/)
                if (matches && matches[1] == year) {
                  monthNum = parseInt(matches[2], 10)
                }
              } else if (monthStr.includes('-')) {
                const parts = monthStr.split('-')
                if (parts.length === 2 && parts[0] == year) {
                  monthNum = parseInt(parts[1], 10)
                }
              } else if (monthStr.includes('月')) {
                const matches = monthStr.match(/(\d{1,2})月/)
                if (matches) {
                  monthNum = parseInt(matches[1], 10)
                }
              }

              if (monthNum >= 1 && monthNum <= 12) {
                const amount = parseFloat(amountStr.replace(/[^\d.-]/g, ''))
                if (!isNaN(amount)) {
                  monthPredictions[monthNum] = Math.round(amount * 100)
                }
              }
            }
          })

          let runningPred = 0
          for (let i = 0; i < 12; i++) {
            if (i < endMonthSelected) {
              forecastSeries[i] = actualSeries[i]
            } else {
              const monthNum = i + 1
              if (monthPredictions[monthNum]) {
                runningPred += monthPredictions[monthNum] || 0
                const cumul = accActualCentsAtSelected + runningPred
                forecastSeries[i] = Number((cumul / 100).toFixed(2))
              } else {
                forecastSeries[i] = null
              }
            }
          }
        }

        // 计算当月值
        const actualMonthly = new Array(12).fill(null)
        for (let i = 0; i < 12; i++) {
          if (actualSeries[i] != null) {
            const prev = (i > 0 && actualSeries[i - 1] != null) ? actualSeries[i - 1] : 0
            actualMonthly[i] = Number((actualSeries[i] - prev).toFixed(2))
          }
        }

        const anchorIndex = Math.max(0, endMonthSelected - 1)
        const forecastMonthly = new Array(12).fill(null)
        for (let i = 0; i < 12; i++) {
          if (forecastSeries[i] != null) {
            if (i > anchorIndex && forecastSeries[i - 1] != null) {
              forecastMonthly[i] = Number((forecastSeries[i] - forecastSeries[i - 1]).toFixed(2))
            } else {
              forecastMonthly[i] = null
            }
          }
        }

        chartInstance.value.setOption({
          tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove',
            axisPointer: { type: 'cross' },
            confine: true,
            formatter(params) {
              const items = Array.isArray(params) ? params : (params ? [params] : [])
              if (items.length === 0) return ''
              const title = items[0]?.axisValue || ''
              const lines = items.map(it => {
                const v = Array.isArray(it.value) ? it.value[1] : it.value
                const num = (typeof v === 'number') ? v : parseFloat(v)
                const totalText = (num == null || isNaN(num)) ? '-' : `¥${formatNumber(num)}`
                let monthText = ''
                if (typeof it.dataIndex === 'number') {
                  if (it.seriesName === '实际累计') {
                    const mv = actualMonthly[it.dataIndex]
                    if (mv != null && !isNaN(mv)) monthText = `；当月：¥${formatNumber(mv)}`
                  } else if (it.seriesName === '预测累计') {
                    const mv = forecastMonthly[it.dataIndex]
                    if (mv != null && !isNaN(mv)) monthText = `；当月：¥${formatNumber(mv)}`
                  }
                }
                return `${it.marker}${it.seriesName}：${totalText}${monthText}`
              })
              return `${title}<br/>${lines.join('<br/>')}`
            }
          },
          legend: {
            data: hasBudgetData ? ['预算值', '实际累计', '预测累计'] : ['实际累计', '预测累计']
          },
          grid: { left: 40, right: 20, bottom: 30, top: 30 },
          xAxis: { type: 'category', data: allMonths.map(m => `${parseInt(m.split('-')[1])}月`) },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: (value) => (typeof value === 'number' ? value.toFixed(2) : value)
            }
          },
          series: [
            // 只有当有预算数据时才添加预算值系列
            ...(hasBudgetData ? [{
              name: '预算值',
              type: 'line',
              data: budgetSeries,
              smooth: false,
              showSymbol: true,
              symbol: 'circle',
              symbolSize: 6,
              label: { show: false },
              lineStyle: { type: 'dashed', width: 2, color: '#909399' }
            }] : []),
            {
              name: '预测累计',
              type: 'line',
              data: forecastSeries,
              smooth: false,
              connectNulls: true,
              showSymbol: true,
              symbol: 'circle',
              symbolSize: 6,
              label: {
                show: true,
                position: 'top',
                formatter: (p) => (typeof p.value === 'number' ? p.value.toFixed(2) : p.value)
              },
              lineStyle: { width: 2, color: '#409EFF' }
            },
            {
              name: '实际累计',
              type: 'line',
              data: actualSeries,
              smooth: false,
              connectNulls: false,
              showSymbol: true,
              symbol: 'circle',
              symbolSize: 6,
              label: {
                show: true,
                position: 'top',
                formatter: (p) => (typeof p.value === 'number' ? p.value.toFixed(2) : p.value)
              },
              lineStyle: { width: 2, color: '#F59E0B' }
            }
          ]
        })
      } catch (e) {
        console.error('渲染趋势图失败:', e)
      } finally {
        if (chartInstance.value) chartInstance.value.hideLoading()
        chartLoading.value = false
      }
    }

    // 轮询控制方法
    const stopMinutePolling = () => {
      if (pollTimer.value) {
        clearInterval(pollTimer.value)
        pollTimer.value = null
      }
    }

    const startMinutePolling = () => {
      stopMinutePolling()
      pollTimer.value = setInterval(async () => {
        try {
          await loadLatestAiAnalysisByIndicator()
          const st = aiAnalysisContent.value && aiAnalysisContent.value.executeStatus
          if (st === 'COMPLETED' || st === 'FAILED') {
            stopMinutePolling()
            await renderTrendChart()
          }
        } catch (e) {
          console.error('轮询检查失败:', e)
        }
      }, 60000)
    }

    // 重新分析
    const rerunAnalysis = async () => {
      if (!props.expenseData) return

      try {
        chartLoading.value = true
        const indicatorId = props.expenseData.indicatorId
        const period = props.expenseData.period || props.filterForm.selectedMonth
        const resp = await predictAndAnalyzeByIndicator(indicatorId, period)

        if (resp?.data?.success) {
          // 立即拉取一次 + 开启每分钟轮询直至完成
          await loadLatestAiAnalysisByIndicator()
          startMinutePolling()
        }
      } catch (e) {
        console.error('重新分析失败:', e)
      } finally {
        chartLoading.value = false
      }

      await renderTrendChart()
    }

    // 导出分析报告
    const exportAnalysis = () => {
      // 导出功能待实现
      console.log('导出分析报告功能开发中')
    }

    // 构建树形数据
    const buildTreeData = (financeCategories, businessKnowledgeByCategory) => {
      try {
        // 创建分类ID到分类对象的映射
        const categoryMap = new Map()
        ;(financeCategories || []).forEach(category => {
          categoryMap.set(category.id, {
            ...category,
            children: []
          })
        })

        // 构建树形结构
        const rootNodes = []
        ;(financeCategories || []).forEach(category => {
          if (category.parentId === 0 || category.parentId === null) {
            rootNodes.push(categoryMap.get(category.id))
          } else {
            const parent = categoryMap.get(category.parentId)
            if (parent) {
              parent.children.push(categoryMap.get(category.id))
            }
          }
        })

        // 转换为树形数据格式并添加业务知识
        const treeData = []
        const defaultExpandedKeys = []

        const convertToTreeNode = (category) => {
          const categoryName = category.fullPath || category.categoryName
          let knowledgeList = []
          const byName = businessKnowledgeByCategory[categoryName]
          const byId = businessKnowledgeByCategory[category.id] || businessKnowledgeByCategory[String(category.id)]
          if (Array.isArray(byName)) knowledgeList = knowledgeList.concat(byName)
          if (Array.isArray(byId)) knowledgeList = knowledgeList.concat(byId)
          if ((!knowledgeList || knowledgeList.length === 0) && businessKnowledgeByCategory && typeof businessKnowledgeByCategory === 'object') {
            try {
              const allLists = Object.values(businessKnowledgeByCategory).flat()
              knowledgeList = allLists.filter(k => k && (k.knowledgeCategory === category.id || String(k.knowledgeCategory) === String(category.id)))
            } catch (e) {
              // ignore
            }
          }

          // 按创建时间排序（假设字段 createTime/createdAt/createdTime 之一；取最先存在的）
          const toTs = (k) => {
            const v = k.createTime || k.createdAt || k.createdTime || k.gmtCreate || k.addTime
            return v ? new Date(v).getTime() : 0
          }
          knowledgeList.sort((a, b) => toTs(a) - toTs(b))

          const categoryNode = {
            id: `category_${category.id}`,
            label: category.categoryName,
            type: 'category',
            categoryId: category.id,
            categoryName: category.categoryName,
            fullPath: category.fullPath,
            knowledgeCount: (knowledgeList && knowledgeList.length) || 0,
            children: []
          }

          // 添加业务知识节点
          ;(knowledgeList || []).forEach(knowledge => {
            categoryNode.children.push({
              id: `knowledge_${knowledge.id}`,
              label: knowledge.knowledgeTitle,
              type: 'knowledge',
              knowledge: knowledge,
              categoryId: category.id
            })
          })

          // 添加子分类
          if (category.children && category.children.length > 0) {
            // 子分类也按创建时间排序（如有字段）
            const childToTs = (c) => {
              const v = c.createTime || c.createdAt || c.createdTime || c.gmtCreate || c.addTime
              return v ? new Date(v).getTime() : 0
            }
            category.children.sort((a,b) => childToTs(a) - childToTs(b))
            category.children.forEach(child => {
              categoryNode.children.push(convertToTreeNode(child))
            })
          }

          // 如果有内容，默认展开第一层
          if (categoryNode.children.length > 0 && category.parentId === 0) {
            defaultExpandedKeys.push(categoryNode.id)
          }

          return categoryNode
        }

        rootNodes.forEach(rootCategory => {
          treeData.push(convertToTreeNode(rootCategory))
        })

        promptDlg.treeData = treeData
        promptDlg.defaultExpandedKeys = defaultExpandedKeys

      } catch (error) {
        console.error('构建树形数据失败:', error)
        promptDlg.treeData = []
        promptDlg.defaultExpandedKeys = []
      }
    }

    // 拖拽相关方法
    const allowDrop = (draggingNode, dropNode, type) => {
      // 只允许将业务知识拖拽到分类节点上
      return draggingNode.data.type === 'knowledge' &&
             dropNode.data.type === 'category' &&
             type === 'inner'
    }

    const allowDrag = (draggingNode) => {
      // 只允许拖拽业务知识节点
      return draggingNode.data.type === 'knowledge'
    }

    const handleDragStart = (node, ev) => {
      promptDlg.isDragging = true
      console.log('开始拖拽:', node.data.label)
    }

    const handleDragEnter = (draggingNode, dropNode, ev) => {
      // 可以添加视觉反馈
    }

    const handleDragLeave = (draggingNode, dropNode, ev) => {
      // 可以移除视觉反馈
    }

    const handleDragOver = (draggingNode, dropNode, ev) => {
      // 拖拽悬停处理
    }

    const handleDragEnd = (draggingNode, dropNode, dropType, ev) => {
      promptDlg.isDragging = false
      console.log('拖拽结束')
    }

    const handleNodeDrop = async (draggingNode, dropNode, dropType, ev) => {
      if (dropType !== 'inner' ||
          draggingNode.data.type !== 'knowledge' ||
          dropNode.data.type !== 'category') {
        return
      }

      try {
        const knowledgeId = draggingNode.data.knowledge.id
        const newCategoryId = dropNode.data.categoryId
        const oldCategoryId = draggingNode.data.categoryId

        if (newCategoryId === oldCategoryId) {
          return // 没有改变分类
        }

        // 显示确认对话框
        await ElMessageBox.confirm(
          `确定要将业务知识"${draggingNode.data.label}"移动到分类"${dropNode.data.label}"吗？`,
          '确认拖拽操作',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 显示加载状态
        promptDlg.loading = true

        // 调用API更新分类
        const response = await updateBusinessKnowledgeCategory(knowledgeId, newCategoryId)

        if (response.data.success) {
          ElMessage.success('业务知识分类更新成功')

          // 刷新数据并重建树
          await refreshBusinessKnowledge()
          await refreshPromptPreview()
          buildTreeData(promptDlg.financeCategories, promptDlg.businessKnowledgeByCategory)

        } else {
          ElMessage.error(response.data.msg || '更新失败')
          // 恢复原始位置（Element Plus会自动处理）
        }

      } catch (error) {
        if (error !== 'cancel') {
          console.error('拖拽更新失败:', error)
          ElMessage.error('更新业务知识分类失败')
        }
        // 恢复原始位置（Element Plus会自动处理）
      } finally {
        promptDlg.loading = false
      }
    }

    // 右键菜单控制
    const hideContextMenu = () => {
      promptDlg.contextMenu.visible = false
      promptDlg.contextMenu.target = null
    }

    const onNodeContextMenu = (evt, data) => {
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      const menuWidth = 180
      const menuHeight = 150
      let x = evt.clientX
      let y = evt.clientY
      if (x + menuWidth > viewportWidth) x = viewportWidth - menuWidth - 4
      if (y + menuHeight > viewportHeight) y = viewportHeight - menuHeight - 4
      promptDlg.contextMenu.x = x
      promptDlg.contextMenu.y = y
      promptDlg.contextMenu.target = data
      promptDlg.contextMenu.visible = true
    }

    const onGlobalClick = () => {
      if (promptDlg.contextMenu.visible) hideContextMenu()
    }

    onMounted(() => {
      window.addEventListener('click', onGlobalClick)
      window.addEventListener('scroll', onGlobalClick)
      window.addEventListener('contextmenu', (e) => {
        // 如果不是在我们的菜单内，默认行为已在节点处理，这里仅用于点击其他位置时关闭
        if (promptDlg.contextMenu.visible) {
          const el = document.querySelector('.context-menu')
          if (el && !el.contains(e.target)) hideContextMenu()
        }
      })
    })
    onBeforeUnmount(() => {
      window.removeEventListener('click', onGlobalClick)
      window.removeEventListener('scroll', onGlobalClick)
    })

    // 右键菜单动作
    const cmAddKnowledge = (data) => { addKnowledgeToCategory(data); hideContextMenu() }
    const cmAddSubCategory = (data) => { addSubCategory(data); hideContextMenu() }
    const cmEditCategory = (data) => { editCategory(data); hideContextMenu() }
    const cmDeleteCategory = (data) => { hideContextMenu(); deleteCategory(data) }
    const cmEditKnowledge = (data) => { if (data.knowledge) editKnowledge(data.knowledge); hideContextMenu() }
    const cmCloneKnowledge = (data) => { if (data.knowledge) createFromKnowledge(data.knowledge); hideContextMenu() }
    const cmDeleteKnowledge = (data) => { hideContextMenu(); if (data.knowledge) deleteKnowledge(data.knowledge) }

    // 树节点点击：分类=新增业务知识，知识=编辑
    const onTreeNodeClick = (data, node, comp) => {
      if (promptDlg.isDragging) return
      if (data && data.type === 'category') {
        addKnowledgeToCategory(data)
      } else if (data && data.type === 'knowledge' && data.knowledge) {
        editKnowledge(data.knowledge)
      }
    }

    return {
      dialogVisible,
      chartLoading,
      aiAnalysisLoading,
      aiAnalysisContent,
      aiThinkCollapsed,
      aiThinkText,
      aiAnswerText,
      isJsonAnswer,
      aiAnswerItems,
      handleDialogVisibleChange,
      onDialogOpen,
      onDialogClose,
      closeDialog,
      formatNumber,
      getStatusTagType,
      getStatusText,
      loadLatestAiAnalysisByIndicator,
      parseAiResultText,
      aiAnswerHtml,
      isKnowledgeApplied,
      renderTrendChart,
      // 提示词编辑器
      promptDlg,
      activeCategories,
      openPromptEditor,
      confirmPromptAndRun,
      // 业务知识管理
      editKnowledge,
      deleteKnowledge,
      createFromKnowledge,
      saveKnowledge,
      addSimpleCategory,
      refreshBusinessKnowledge,
      refreshPromptPreview,
      // 分类管理
      addRootCategory,
      addSubCategory,
      editCategory,
      deleteCategory,
      saveCategory,
      refreshFinanceCategories,
      // 树形组件
      treeProps,
      buildTreeData,
      allowDrop,
      allowDrag,
      handleDragStart,
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDragEnd,
      handleNodeDrop,
      addKnowledgeToCategory,
      onTreeNodeClick,
      onNodeContextMenu,
      cmAddKnowledge,
      cmAddSubCategory,
      cmEditCategory,
      cmDeleteCategory,
      cmEditKnowledge,
      cmCloneKnowledge,
      cmDeleteKnowledge,
      // 旧重跑方法（保留以兼容）
      rerunAnalysis,
      exportAnalysis,
      // 轮询控制
      startMinutePolling,
      stopMinutePolling
    }
  }
}
</script>

<style scoped>
/* 分析弹窗样式 */
.analysis-content {
  padding: 10px 0;
}

.expense-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.prompt-editor { display: flex; gap: 16px; }
.prompt-editor .left-view, .prompt-editor .right-view { flex: 1; }
.prompt-editor .section-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-actions {
  display: flex;
  gap: 5px;
}
.prompt-editor .prompt-preview { max-height: 520px; overflow: auto; background: #fafafa; padding: 10px; border: 1px solid #eee; border-radius: 6px; white-space: pre-wrap; }

.business-rules-list { max-height: 520px; overflow: auto; background: #f8f9fa; border: 1px solid #e4e7ed; border-radius: 6px; padding: 12px; }
.business-rules-list .no-rules { color: #909399; text-align: center; padding: 40px 20px; font-style: italic; }
.business-rules-list .rule-item { display: flex; margin-bottom: 12px; padding: 8px; background: #fff; border-radius: 4px; border-left: 3px solid #67C23A; }
.business-rules-list .rule-item:last-child { margin-bottom: 0; }
.business-rules-list .rule-index { color: #67C23A; font-weight: 600; margin-right: 8px; min-width: 20px; }
.business-rules-list .rule-content { color: #606266; line-height: 1.5; flex: 1; }

.expense-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.expense-amount {
  font-size: 16px;
  font-weight: bold;
  color: #e6a23c;
  margin: 5px 0;
}

.expense-desc {
  color: #666;
  margin: 5px 0 0 0;
}

.trend-chart-section {
  margin-bottom: 25px;
}

.trend-chart-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.ai-analysis-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.ai-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.ai-title {
  margin: 0;
}

.ai-status-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-status-hint {
  margin-left: 8px;
  color: #909399;
}

.ai-answer :deep(.ai-sec-title) {
  font-weight: 700;
  font-size: 18px;
  display: inline-block;
  margin-top: 8px;
}

.ai-answer {
  white-space: pre-wrap;
  word-break: break-word;
  margin-top: 8px;
}

/* 强制重置 markdown 元素的上下外边距为 0 */
.ai-content :deep(h1),
.ai-content :deep(h2),
.ai-content :deep(h3),
.ai-content :deep(h4),
.ai-content :deep(h5),
.ai-content :deep(h6),
.ai-content :deep(p),
.ai-content :deep(ul),
.ai-content :deep(ol),
.ai-content :deep(li),
.ai-content :deep(pre),
.ai-content :deep(code),
.ai-content :deep(blockquote),
.ai-content :deep(table),
.ai-content :deep(thead),
.ai-content :deep(tbody),
.ai-content :deep(tr),
.ai-content :deep(th),
.ai-content :deep(td) {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.ai-content {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
}

.analysis-item {
  margin-bottom: 15px;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-label {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
  font-size: 14px;
}

.analysis-text {
  color: #666;
  line-height: 1;
}

/* 思考过程折叠样式 */
.think-box {
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  background: #fafafa;
}

.think-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  color: #606266;
}

.think-header:hover {
  background: #f2f6fc;
}

.think-content {
  padding: 8px 12px 12px 12px;
}

/* JSON分析样式 */
.json-analysis {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.context-card {
  background: linear-gradient(135deg, #eef2ff, #f0f9ff);
  border: 1px solid #e6f0ff;
  border-radius: 8px;
  padding: 12px 14px;
}

.context-title {
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 6px;
}

.context-text {
  color: #374151;
  line-height: 1.6;
}

.forecast-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  padding: 12px 14px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.forecast-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.month-badge {
  background: #eef2ff;
  color: #3b82f6;
  padding: 2px 8px;
  border-radius: 999px;
  font-weight: 600;
}

.amount {
  color: #10b981;
  font-weight: 700;
}

.reason {
  color: #4b5563;
  line-height: 1.6;
}

/* 业务知识管理样式 */
.business-knowledge-manager {
  max-height: 520px;
  overflow: auto;
  background: #fafafa;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 6px;
}

.empty-knowledge {
  text-align: center;
  color: #999;
  padding: 20px;
}

.category-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.category-name {
  font-weight: bold;
  color: #333;
}

.knowledge-count {
  color: #999;
  margin-left: 8px;
}

.add-knowledge-btn {
  margin-left: auto;
  color: #67C23A;
}

.add-category-btn {
  color: #409EFF;
}

.edit-category-btn {
  color: #E6A23C;
}

.delete-category-btn {
  color: #F56C6C;
}

.knowledge-list {
  padding: 10px 0;
}

.empty-category {
  text-align: center;
  color: #999;
  padding: 10px;
  font-style: italic;
}

.knowledge-item {
  margin-bottom: 12px;
  padding: 12px;
  background: #fff;
  border: 1px solid #67C23A;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.knowledge-title {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.knowledge-actions {
  display: flex;
  gap: 8px;
}

.knowledge-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.delete-btn {
  color: #F56C6C;
}

.knowledge-content {
  color: #666;
  line-height: 1.5;
  font-size: 13px;
  white-space: pre-wrap;
}

/* 树形组件样式 */
.knowledge-tree {
  margin-top: 10px;
}

.tree-node-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.applied-knowledge {
  border: none;
  background: transparent;
}
.applied-knowledge .node-label {
  color: #67C23A;
  font-weight: 600;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-label {
  font-size: 14px;
  color: #333;
}

.knowledge-count {
  font-size: 12px;
  color: #999;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 10px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node-content:hover .node-actions {
  opacity: 1;
}

.node-actions .el-button {
  padding: 2px 6px;
  font-size: 12px;
  min-height: auto;
}

/* 拖拽状态样式 */
.el-tree-node.is-drop-inner > .el-tree-node__content {
  background-color: #e6f7ff;
  border: 2px dashed #1890ff;
}

.el-tree-node__content:hover {
  background-color: #f5f5f5;
}

/* 业务知识节点特殊样式 */
.el-tree-node[data-type="knowledge"] > .el-tree-node__content {
  background-color: #f9f9f9;
  border-left: 3px solid #67C23A;
  margin-left: 10px;
  border-radius: 4px;
}

.el-tree-node[data-type="knowledge"] > .el-tree-node__content:hover {
  background-color: #f0f9ff;
}

/* 分类节点样式 */
.el-tree-node[data-type="category"] > .el-tree-node__content {
  font-weight: 500;
  background-color: #fafafa;
}

.el-tree-node[data-type="category"] > .el-tree-node__content:hover {
  background-color: #f0f0f0;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  z-index: 3000;
  width: 180px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 6px 18px rgba(0,0,0,0.12);
  overflow: hidden;
}
.context-menu .cm-item {
  padding: 8px 12px;
  font-size: 13px;
  color: #303133;
  cursor: pointer;
}
.context-menu .cm-item:hover {
  background: #f5f7fa;
}
.context-menu .cm-item.danger {
  color: #f56c6c;
}
</style>
