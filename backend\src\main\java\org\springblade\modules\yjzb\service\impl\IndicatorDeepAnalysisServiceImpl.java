package org.springblade.modules.yjzb.service.impl;

import lombok.RequiredArgsConstructor;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO;
import org.springblade.modules.yjzb.service.IIndicatorDeepAnalysisService;
import org.springblade.modules.yjzb.service.IIndicatorValuesDetailService;
import org.springblade.modules.yjzb.service.IIndicatorAnnualBudgetService;
import org.springblade.modules.yjzb.service.ExpenseForecastService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
@RequiredArgsConstructor
public class IndicatorDeepAnalysisServiceImpl implements IIndicatorDeepAnalysisService {

    private final IIndicatorValuesDetailService indicatorValuesDetailService;
    private final IIndicatorAnnualBudgetService indicatorAnnualBudgetService;
    private final ExpenseForecastService expenseForecastService;

    @Override
    public Map<String, Object> generateCategoryStructureChartData(Long indicatorId, String period) {
        Map<String, Object> result = new LinkedHashMap<>();

        // 参数验证
        if (indicatorId == null) {
            throw new IllegalArgumentException("指标ID不能为空");
        }
        if (period == null || period.trim().isEmpty()) {
            throw new IllegalArgumentException("期间不能为空");
        }

        System.out.println(
                "generateCategoryStructureChartData called with indicatorId: " + indicatorId + ", period: " + period);

        try {
            // 1. 查询该指标在指定期间的明细，按分类汇总
            List<IndicatorValuesDetailVO> list = indicatorValuesDetailService.sumAmountByCategory(indicatorId, period);
            if (list == null) {
                list = Collections.emptyList();
            }

            System.out.println("sumAmountByCategory returned " + list.size() + " records");

            // 汇总总金额
            BigDecimal total = list.stream()
                    .map(IndicatorValuesDetailVO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 2. 组装行数据：name/amount/ratio
            List<Map<String, Object>> rows = new ArrayList<>();
            for (IndicatorValuesDetailVO vo : list) {
                String name = Optional.ofNullable(vo.getCategory()).orElse("未分类");
                BigDecimal amount = Optional.ofNullable(vo.getAmount()).orElse(BigDecimal.ZERO);

                // 计算占比
                double ratio = 0.0;
                if (total.signum() > 0) {
                    ratio = amount.multiply(BigDecimal.valueOf(100))
                            .divide(total, 4, RoundingMode.HALF_UP)
                            .setScale(2, RoundingMode.HALF_UP)
                            .doubleValue();
                }

                Map<String, Object> item = new LinkedHashMap<>();
                item.put("name", name);
                item.put("amount", amount.setScale(2, RoundingMode.HALF_UP));
                item.put("ratio", ratio);
                rows.add(item);
            }

            // 3. 组装返回结构
            String periodDisplay = period != null ? period.replace("-", "年") : "";
            result.put("title", String.format("表1：%s办公费用明细与结构占比", periodDisplay));
            result.put("columns", Arrays.asList("费用项目", "本月实际支出(元)", "占比(%)"));
            result.put("rows", rows);

            Map<String, Object> totalObj = new LinkedHashMap<>();
            totalObj.put("amount", total.setScale(2, RoundingMode.HALF_UP));
            totalObj.put("ratio", 100.00);
            result.put("total", totalObj);

            System.out.println("Final result: " + result);
            return result;

        } catch (Exception e) {
            System.err.println("generateCategoryStructureChartData error: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("生成分类结构数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> generateTrendComparisonChartData(Long indicatorId, String period) {
        Map<String, Object> result = new LinkedHashMap<>();

        // 参数验证
        if (indicatorId == null) {
            throw new IllegalArgumentException("指标ID不能为空");
        }
        if (period == null || period.trim().isEmpty()) {
            throw new IllegalArgumentException("期间不能为空");
        }

        System.out.println(
                "generateTrendComparisonChartData called with indicatorId: " + indicatorId + ", period: " + period);

        try {
            // 解析期间，获取年份
            String[] periodParts = period.split("-");
            if (periodParts.length != 2) {
                throw new IllegalArgumentException("期间格式错误，应为YYYY-MM格式");
            }

            int currentYear = Integer.parseInt(periodParts[0]);
            int currentMonth = Integer.parseInt(periodParts[1]);

            // 获取过去12个月的月度数据
            List<Map<String, Object>> monthlyData = new ArrayList<>();
            List<Double> historicalValues = new ArrayList<>(); // 历史数据（前11个月）
            final double[] currentValueHolder = { 0.0 }; // 使用数组来存储当前月份数据

            // 从当前月份往前推12个月
            for (int i = 11; i >= 0; i--) {
                int targetMonth = currentMonth - i;
                int targetYear = currentYear;

                if (targetMonth <= 0) {
                    targetMonth += 12;
                    targetYear -= 1;
                }

                String yearMonth = targetYear + "-" + String.format("%02d", targetMonth);
                BigDecimal monthTotal = indicatorValuesDetailService.sumAmountByIndicatorIdAndPeriod(indicatorId,
                        yearMonth);
                double value = monthTotal != null ? monthTotal.doubleValue() : 0.0;

                Map<String, Object> monthData = new LinkedHashMap<>();
                monthData.put("month", yearMonth);
                monthData.put("value", value);
                monthData.put("isCurrentMonth", i == 0); // 标记当前月份
                monthlyData.add(monthData);

                if (i == 0) {
                    currentValueHolder[0] = value; // 当前月份
                } else {
                    historicalValues.add(value); // 历史数据
                }
            }

            final double currentValue = currentValueHolder[0]; // 提取当前值

            // 基于历史数据计算趋势指标
            double historicalMean = historicalValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double historicalStdDev = Math.sqrt(historicalValues.stream()
                    .mapToDouble(v -> Math.pow(v - historicalMean, 2))
                    .average()
                    .orElse(0.0));

            // 计算当前月份相对于历史数据的趋势变化
            double currentDeviation = currentValue - historicalMean;
            double currentDeviationRate = historicalMean > 0 ? currentDeviation / historicalMean : 0.0;
            double currentZScore = historicalStdDev > 0 ? currentDeviation / historicalStdDev : 0.0;

            // 趋势合理性判断
            String trendStatus = "normal";
            String trendLevel = "low";
            String trendAssessment = "";

            if (Math.abs(currentZScore) <= 1.0) {
                trendStatus = "normal";
                trendLevel = "low";
                trendAssessment = "当前月份趋势变化在正常范围内";
            } else if (Math.abs(currentZScore) <= 2.0) {
                trendStatus = "moderate";
                trendLevel = "medium";
                trendAssessment = "当前月份趋势变化较大，需要关注";
            } else {
                trendStatus = "high";
                trendLevel = "high";
                trendAssessment = "当前月份趋势变化异常，需要重点关注";
            }

            // 趋势方向分析
            String trendDirection = currentDeviation > 0 ? "上升" : currentDeviation < 0 ? "下降" : "平稳";
            String directionDescription = currentDeviation > 0 ? "当前月份费用较历史均值上升"
                    : currentDeviation < 0 ? "当前月份费用较历史均值下降" : "当前月份费用与历史均值持平";

            // 趋势变化幅度分析
            String amplitudeDescription = "";
            if (Math.abs(currentDeviationRate) <= 0.05) {
                amplitudeDescription = "趋势变化幅度较小（≤5%）";
            } else if (Math.abs(currentDeviationRate) <= 0.15) {
                amplitudeDescription = "趋势变化幅度中等（5%-15%）";
            } else if (Math.abs(currentDeviationRate) <= 0.30) {
                amplitudeDescription = "趋势变化幅度较大（15%-30%）";
            } else {
                amplitudeDescription = "趋势变化幅度很大（>30%）";
            }

            // 趋势持续性分析
            String trendPersistence = "";
            if (historicalValues.size() >= 3) {
                // 分析最近3个月的趋势
                double recentAvg = historicalValues.subList(historicalValues.size() - 3, historicalValues.size())
                        .stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double earlierAvg = historicalValues.subList(0, Math.min(3, historicalValues.size()))
                        .stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

                if (Math.abs(recentAvg - earlierAvg) / historicalMean < 0.05) {
                    trendPersistence = "趋势相对稳定，变化不大";
                } else if (recentAvg > earlierAvg) {
                    trendPersistence = "近期呈上升趋势";
                } else {
                    trendPersistence = "近期呈下降趋势";
                }
            }

            // 计算历史数据的统计指标
            double historicalMax = historicalValues.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            double historicalMin = historicalValues.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
            double historicalRange = historicalMax - historicalMin;
            double historicalCV = historicalMean > 0 ? historicalStdDev / historicalMean : 0.0;

            // 当前月份在历史数据中的位置
            long historicalRank = historicalValues.stream().filter(v -> v < currentValue).count() + 1;
            double percentile = (double) historicalRank / historicalValues.size() * 100;

            // 计算趋势控制限
            Map<String, Object> trendLimits = new LinkedHashMap<>();
            trendLimits.put("upper3Sigma", Math.round((historicalMean + 3 * historicalStdDev) * 100.0) / 100.0);
            trendLimits.put("upper2Sigma", Math.round((historicalMean + 2 * historicalStdDev) * 100.0) / 100.0);
            trendLimits.put("lower2Sigma", Math.round((historicalMean - 2 * historicalStdDev) * 100.0) / 100.0);
            trendLimits.put("lower3Sigma", Math.round((historicalMean - 3 * historicalStdDev) * 100.0) / 100.0);
            trendLimits.put("historicalMean", Math.round(historicalMean * 100.0) / 100.0);

            // 组装指标数据
            Map<String, Object> indicators = new LinkedHashMap<>();
            indicators.put("currentValue", Math.round(currentValue * 100.0) / 100.0);
            indicators.put("historicalMean", Math.round(historicalMean * 100.0) / 100.0);
            indicators.put("historicalStdDev", Math.round(historicalStdDev * 100.0) / 100.0);
            indicators.put("currentDeviation", Math.round(currentDeviation * 100.0) / 100.0);
            indicators.put("currentDeviationRate", Math.round(currentDeviationRate * 10000.0) / 10000.0);
            indicators.put("currentZScore", Math.round(currentZScore * 100.0) / 100.0);
            indicators.put("trendStatus", trendStatus);
            indicators.put("trendLevel", trendLevel);
            indicators.put("trendDirection", trendDirection);
            indicators.put("historicalRank", historicalRank);
            indicators.put("percentile", Math.round(percentile * 10.0) / 10.0);
            indicators.put("historicalCV", Math.round(historicalCV * 1000.0) / 1000.0);
            indicators.put("historicalRange", Math.round(historicalRange * 100.0) / 100.0);

            // 组装分析结论
            Map<String, Object> trendAnalysis = new LinkedHashMap<>();
            trendAnalysis.put("assessment", trendAssessment);
            trendAnalysis.put("directionDescription", directionDescription);
            trendAnalysis.put("amplitudeDescription", amplitudeDescription);
            trendAnalysis.put("trendPersistence", trendPersistence);
            trendAnalysis.put("currentMonth", period);
            trendAnalysis.put("historicalPeriod", "过去11个月");

            // 组装返回结构
            result.put("monthlyData", monthlyData);
            result.put("indicators", indicators);
            result.put("trendLimits", trendLimits);
            result.put("trendAnalysis", trendAnalysis);

            System.out.println("Trend analysis result: " + result);
            return result;

        } catch (Exception e) {
            System.err.println("generateTrendComparisonChartData error: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("生成趋势分析数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> generateVolatilityAnalysisChartData(Long indicatorId, String period) {
        Map<String, Object> result = new LinkedHashMap<>();

        // 参数验证
        if (indicatorId == null) {
            throw new IllegalArgumentException("指标ID不能为空");
        }
        if (period == null || period.trim().isEmpty()) {
            throw new IllegalArgumentException("期间不能为空");
        }

        System.out.println(
                "generateVolatilityAnalysisChartData called with indicatorId: " + indicatorId + ", period: " + period);

        try {
            // 解析期间，获取年份
            String[] periodParts = period.split("-");
            if (periodParts.length != 2) {
                throw new IllegalArgumentException("期间格式错误，应为YYYY-MM格式");
            }

            int currentYear = Integer.parseInt(periodParts[0]);
            int currentMonth = Integer.parseInt(periodParts[1]);

            // 获取过去12个月的月度数据
            List<Map<String, Object>> monthlyData = new ArrayList<>();
            List<Double> historicalValues = new ArrayList<>(); // 历史数据（前11个月）
            final double[] currentValueHolder = { 0.0 }; // 使用数组来存储当前月份数据

            // 从当前月份往前推12个月
            for (int i = 11; i >= 0; i--) {
                int targetMonth = currentMonth - i;
                int targetYear = currentYear;

                if (targetMonth <= 0) {
                    targetMonth += 12;
                    targetYear -= 1;
                }

                String yearMonth = targetYear + "-" + String.format("%02d", targetMonth);
                BigDecimal monthTotal = indicatorValuesDetailService.sumAmountByIndicatorIdAndPeriod(indicatorId,
                        yearMonth);
                double value = monthTotal != null ? monthTotal.doubleValue() : 0.0;

                Map<String, Object> monthData = new LinkedHashMap<>();
                monthData.put("month", yearMonth);
                monthData.put("value", value);
                monthData.put("isCurrentMonth", i == 0); // 标记当前月份
                monthlyData.add(monthData);

                if (i == 0) {
                    currentValueHolder[0] = value; // 当前月份
                } else {
                    historicalValues.add(value); // 历史数据
                }
            }

            final double currentValue = currentValueHolder[0]; // 提取当前值

            // 基于历史数据计算统计指标
            double historicalMean = historicalValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double historicalStdDev = Math.sqrt(historicalValues.stream()
                    .mapToDouble(v -> Math.pow(v - historicalMean, 2))
                    .average()
                    .orElse(0.0));

            // 计算当前月份相对于历史数据的波动
            double currentDeviation = currentValue - historicalMean;
            double currentDeviationRate = historicalMean > 0 ? currentDeviation / historicalMean : 0.0;
            double currentZScore = historicalStdDev > 0 ? currentDeviation / historicalStdDev : 0.0;

            // 波动合理性判断
            String volatilityStatus = "normal";
            String volatilityLevel = "low";
            String volatilityAssessment = "";

            if (Math.abs(currentZScore) <= 1.0) {
                volatilityStatus = "normal";
                volatilityLevel = "low";
                volatilityAssessment = "当前月份波动在正常范围内";
            } else if (Math.abs(currentZScore) <= 2.0) {
                volatilityStatus = "moderate";
                volatilityLevel = "medium";
                volatilityAssessment = "当前月份波动较大，需要关注";
            } else {
                volatilityStatus = "high";
                volatilityLevel = "high";
                volatilityAssessment = "当前月份波动异常，需要重点关注";
            }

            // 波动方向分析
            String volatilityDirection = currentDeviation > 0 ? "上升" : currentDeviation < 0 ? "下降" : "持平";
            String directionDescription = currentDeviation > 0 ? "当前月份费用较历史均值上升"
                    : currentDeviation < 0 ? "当前月份费用较历史均值下降" : "当前月份费用与历史均值持平";

            // 波动幅度分析
            String amplitudeDescription = "";
            if (Math.abs(currentDeviationRate) <= 0.05) {
                amplitudeDescription = "波动幅度较小（≤5%）";
            } else if (Math.abs(currentDeviationRate) <= 0.15) {
                amplitudeDescription = "波动幅度中等（5%-15%）";
            } else if (Math.abs(currentDeviationRate) <= 0.30) {
                amplitudeDescription = "波动幅度较大（15%-30%）";
            } else {
                amplitudeDescription = "波动幅度很大（>30%）";
            }

            // 季节性分析（简单实现）
            String seasonalAnalysis = "";
            if (currentMonth >= 3 && currentMonth <= 5) {
                seasonalAnalysis = "春季期间，可能存在季节性波动";
            } else if (currentMonth >= 6 && currentMonth <= 8) {
                seasonalAnalysis = "夏季期间，可能存在季节性波动";
            } else if (currentMonth >= 9 && currentMonth <= 11) {
                seasonalAnalysis = "秋季期间，可能存在季节性波动";
            } else {
                seasonalAnalysis = "冬季期间，可能存在季节性波动";
            }

            // 计算历史数据的统计指标
            double historicalMax = historicalValues.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            double historicalMin = historicalValues.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
            double historicalRange = historicalMax - historicalMin;
            double historicalCV = historicalMean > 0 ? historicalStdDev / historicalMean : 0.0;

            // 当前月份在历史数据中的位置
            long historicalRank = historicalValues.stream().filter(v -> v < currentValue).count() + 1;
            double percentile = (double) historicalRank / historicalValues.size() * 100;

            // 计算控制限（基于历史数据）
            Map<String, Object> controlLimits = new LinkedHashMap<>();
            controlLimits.put("upper3Sigma", Math.round((historicalMean + 3 * historicalStdDev) * 100.0) / 100.0);
            controlLimits.put("upper2Sigma", Math.round((historicalMean + 2 * historicalStdDev) * 100.0) / 100.0);
            controlLimits.put("lower2Sigma", Math.round((historicalMean - 2 * historicalStdDev) * 100.0) / 100.0);
            controlLimits.put("lower3Sigma", Math.round((historicalMean - 3 * historicalStdDev) * 100.0) / 100.0);
            controlLimits.put("historicalMean", Math.round(historicalMean * 100.0) / 100.0);

            // 组装指标数据
            Map<String, Object> indicators = new LinkedHashMap<>();
            indicators.put("currentValue", Math.round(currentValue * 100.0) / 100.0);
            indicators.put("historicalMean", Math.round(historicalMean * 100.0) / 100.0);
            indicators.put("historicalStdDev", Math.round(historicalStdDev * 100.0) / 100.0);
            indicators.put("currentDeviation", Math.round(currentDeviation * 100.0) / 100.0);
            indicators.put("currentDeviationRate", Math.round(currentDeviationRate * 10000.0) / 10000.0);
            indicators.put("currentZScore", Math.round(currentZScore * 100.0) / 100.0);
            indicators.put("volatilityStatus", volatilityStatus);
            indicators.put("volatilityLevel", volatilityLevel);
            indicators.put("volatilityDirection", volatilityDirection);
            indicators.put("historicalRank", historicalRank);
            indicators.put("percentile", Math.round(percentile * 10.0) / 10.0);
            indicators.put("historicalCV", Math.round(historicalCV * 1000.0) / 1000.0);
            indicators.put("historicalRange", Math.round(historicalRange * 100.0) / 100.0);

            // 组装分析结论
            Map<String, Object> volatilityAnalysis = new LinkedHashMap<>();
            volatilityAnalysis.put("assessment", volatilityAssessment);
            volatilityAnalysis.put("directionDescription", directionDescription);
            volatilityAnalysis.put("amplitudeDescription", amplitudeDescription);
            volatilityAnalysis.put("seasonalAnalysis", seasonalAnalysis);
            volatilityAnalysis.put("currentMonth", period);
            volatilityAnalysis.put("historicalPeriod", "过去11个月");

            // 组装返回结构
            result.put("monthlyData", monthlyData);
            result.put("indicators", indicators);
            result.put("controlLimits", controlLimits);
            result.put("volatilityAnalysis", volatilityAnalysis);

            System.out.println("Volatility analysis result: " + result);
            return result;

        } catch (Exception e) {
            System.err.println("generateVolatilityAnalysisChartData error: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("生成波动性分析数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> generateGrowthAnalysisChartData(Long indicatorId, String period) {
        Map<String, Object> result = new LinkedHashMap<>();

        // 参数验证
        if (indicatorId == null) {
            throw new IllegalArgumentException("指标ID不能为空");
        }
        if (period == null || period.trim().isEmpty()) {
            throw new IllegalArgumentException("期间不能为空");
        }

        System.out.println(
                "generateGrowthAnalysisChartData called with indicatorId: " + indicatorId + ", period: " + period);

        try {
            // 解析期间，获取年份
            String[] periodParts = period.split("-");
            if (periodParts.length != 2) {
                throw new IllegalArgumentException("期间格式错误，应为YYYY-MM格式");
            }

            int currentYear = Integer.parseInt(periodParts[0]);
            int currentMonth = Integer.parseInt(periodParts[1]);

            // 获取过去12个月的月度数据
            List<Map<String, Object>> monthlyData = new ArrayList<>();
            List<Double> historicalValues = new ArrayList<>(); // 历史数据（前11个月）
            final double[] currentValueHolder = { 0.0 }; // 使用数组来存储当前月份数据

            // 从当前月份往前推12个月
            for (int i = 11; i >= 0; i--) {
                int targetMonth = currentMonth - i;
                int targetYear = currentYear;

                if (targetMonth <= 0) {
                    targetMonth += 12;
                    targetYear -= 1;
                }

                String yearMonth = targetYear + "-" + String.format("%02d", targetMonth);
                BigDecimal monthTotal = indicatorValuesDetailService.sumAmountByIndicatorIdAndPeriod(indicatorId,
                        yearMonth);
                double value = monthTotal != null ? monthTotal.doubleValue() : 0.0;

                Map<String, Object> monthData = new LinkedHashMap<>();
                monthData.put("month", yearMonth);
                monthData.put("value", value);
                monthData.put("isCurrentMonth", i == 0); // 标记当前月份
                monthlyData.add(monthData);

                if (i == 0) {
                    currentValueHolder[0] = value; // 当前月份
                } else {
                    historicalValues.add(value); // 历史数据
                }
            }

            final double currentValue = currentValueHolder[0]; // 提取当前值

            // 计算当前月份的增长率
            final double[] currentMomGrowthHolder = { 0.0 }; // 当前月份环比增长率
            final double[] currentYoyGrowthHolder = { 0.0 }; // 当前月份同比增长率

            if (historicalValues.size() > 0) {
                double previousValue = historicalValues.get(historicalValues.size() - 1);
                if (previousValue > 0) {
                    currentMomGrowthHolder[0] = (currentValue - previousValue) / previousValue;
                }
            }

            final double currentMomGrowth = currentMomGrowthHolder[0];
            final double currentYoyGrowth = currentYoyGrowthHolder[0];

            // 计算历史增长率数据
            List<Double> historicalMomGrowthRates = new ArrayList<>();
            // List<Double> historicalYoyGrowthRates = new ArrayList<>(); // 未使用，移除

            for (int i = 1; i < historicalValues.size(); i++) {
                double current = historicalValues.get(i);
                double previous = historicalValues.get(i - 1);
                if (previous > 0) {
                    historicalMomGrowthRates.add((current - previous) / previous);
                }
            }

            // 计算历史增长率统计指标
            double historicalMomMean = historicalMomGrowthRates.isEmpty() ? 0.0
                    : historicalMomGrowthRates.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double historicalMomStdDev = historicalMomGrowthRates.isEmpty() ? 0.0
                    : Math.sqrt(historicalMomGrowthRates.stream()
                            .mapToDouble(r -> Math.pow(r - historicalMomMean, 2))
                            .average().orElse(0.0));

            // 计算当前月份增长率相对于历史的变化
            double currentMomDeviation = currentMomGrowth - historicalMomMean;
            double currentMomZScore = historicalMomStdDev > 0 ? currentMomDeviation / historicalMomStdDev : 0.0;

            // 增长率合理性判断
            String growthStatus = "normal";
            String growthLevel = "low";
            String growthAssessment = "";

            if (Math.abs(currentMomZScore) <= 1.0) {
                growthStatus = "normal";
                growthLevel = "low";
                growthAssessment = "当前月份增长率变化在正常范围内";
            } else if (Math.abs(currentMomZScore) <= 2.0) {
                growthStatus = "moderate";
                growthLevel = "medium";
                growthAssessment = "当前月份增长率变化较大，需要关注";
            } else {
                growthStatus = "high";
                growthLevel = "high";
                growthAssessment = "当前月份增长率变化异常，需要重点关注";
            }

            // 增长率方向分析
            String growthDirection = currentMomGrowth > 0 ? "正增长" : currentMomGrowth < 0 ? "负增长" : "零增长";
            String directionDescription = currentMomGrowth > 0 ? "当前月份费用较上月增长"
                    : currentMomGrowth < 0 ? "当前月份费用较上月下降" : "当前月份费用与上月持平";

            // 增长率幅度分析
            String amplitudeDescription = "";
            if (Math.abs(currentMomGrowth) <= 0.05) {
                amplitudeDescription = "增长率幅度较小（≤5%）";
            } else if (Math.abs(currentMomGrowth) <= 0.15) {
                amplitudeDescription = "增长率幅度中等（5%-15%）";
            } else if (Math.abs(currentMomGrowth) <= 0.30) {
                amplitudeDescription = "增长率幅度较大（15%-30%）";
            } else {
                amplitudeDescription = "增长率幅度很大（>30%）";
            }

            // 增长率趋势分析
            String growthTrend = "";
            if (historicalMomGrowthRates.size() >= 3) {
                // 分析最近3个月的增长率趋势
                double recentAvg = historicalMomGrowthRates
                        .subList(historicalMomGrowthRates.size() - 3, historicalMomGrowthRates.size())
                        .stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double earlierAvg = historicalMomGrowthRates.subList(0, Math.min(3, historicalMomGrowthRates.size()))
                        .stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

                if (Math.abs(recentAvg - earlierAvg) < 0.05) {
                    growthTrend = "增长率趋势相对稳定";
                } else if (recentAvg > earlierAvg) {
                    growthTrend = "增长率呈上升趋势";
                } else {
                    growthTrend = "增长率呈下降趋势";
                }
            }

            // 计算历史增长率的统计指标
            double historicalMomMax = historicalMomGrowthRates.stream().mapToDouble(Double::doubleValue).max()
                    .orElse(0.0);
            double historicalMomMin = historicalMomGrowthRates.stream().mapToDouble(Double::doubleValue).min()
                    .orElse(0.0);
            double historicalMomRange = historicalMomMax - historicalMomMin;
            double historicalMomCV = historicalMomMean != 0 ? historicalMomStdDev / Math.abs(historicalMomMean) : 0.0;

            // 当前月份增长率在历史数据中的位置
            long historicalRank = historicalMomGrowthRates.stream().filter(r -> r < currentMomGrowth).count() + 1;
            double percentile = historicalMomGrowthRates.isEmpty() ? 0.0
                    : (double) historicalRank / historicalMomGrowthRates.size() * 100;

            // 计算增长率控制限
            Map<String, Object> growthLimits = new LinkedHashMap<>();
            growthLimits.put("upper3Sigma",
                    Math.round((historicalMomMean + 3 * historicalMomStdDev) * 10000.0) / 10000.0);
            growthLimits.put("upper2Sigma",
                    Math.round((historicalMomMean + 2 * historicalMomStdDev) * 10000.0) / 10000.0);
            growthLimits.put("lower2Sigma",
                    Math.round((historicalMomMean - 2 * historicalMomStdDev) * 10000.0) / 10000.0);
            growthLimits.put("lower3Sigma",
                    Math.round((historicalMomMean - 3 * historicalMomStdDev) * 10000.0) / 10000.0);
            growthLimits.put("historicalMean", Math.round(historicalMomMean * 10000.0) / 10000.0);

            // 组装指标数据
            Map<String, Object> indicators = new LinkedHashMap<>();
            indicators.put("currentValue", Math.round(currentValue * 100.0) / 100.0);
            indicators.put("currentMomGrowth", Math.round(currentMomGrowth * 10000.0) / 10000.0);
            indicators.put("currentYoyGrowth", Math.round(currentYoyGrowth * 10000.0) / 10000.0);
            indicators.put("historicalMomMean", Math.round(historicalMomMean * 10000.0) / 10000.0);
            indicators.put("historicalMomStdDev", Math.round(historicalMomStdDev * 10000.0) / 10000.0);
            indicators.put("currentMomDeviation", Math.round(currentMomDeviation * 10000.0) / 10000.0);
            indicators.put("currentMomZScore", Math.round(currentMomZScore * 100.0) / 100.0);
            indicators.put("growthStatus", growthStatus);
            indicators.put("growthLevel", growthLevel);
            indicators.put("growthDirection", growthDirection);
            indicators.put("historicalRank", historicalRank);
            indicators.put("percentile", Math.round(percentile * 10.0) / 10.0);
            indicators.put("historicalMomCV", Math.round(historicalMomCV * 1000.0) / 1000.0);
            indicators.put("historicalMomRange", Math.round(historicalMomRange * 10000.0) / 10000.0);

            // 组装分析结论
            Map<String, Object> growthAnalysis = new LinkedHashMap<>();
            growthAnalysis.put("assessment", growthAssessment);
            growthAnalysis.put("directionDescription", directionDescription);
            growthAnalysis.put("amplitudeDescription", amplitudeDescription);
            growthAnalysis.put("growthTrend", growthTrend);
            growthAnalysis.put("currentMonth", period);
            growthAnalysis.put("historicalPeriod", "过去11个月");

            // 组装返回结构
            result.put("monthlyData", monthlyData);
            result.put("indicators", indicators);
            result.put("growthLimits", growthLimits);
            result.put("growthAnalysis", growthAnalysis);

            System.out.println("Growth analysis result: " + result);
            return result;

        } catch (Exception e) {
            System.err.println("generateGrowthAnalysisChartData error: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("生成增长率分析数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> generateBudgetAnalysisChartData(Long indicatorId, String period) {
        Map<String, Object> result = new LinkedHashMap<>();

        // 参数验证
        if (indicatorId == null) {
            throw new IllegalArgumentException("指标ID不能为空");
        }
        if (period == null || period.trim().isEmpty()) {
            throw new IllegalArgumentException("期间不能为空");
        }

        System.out.println(
                "generateBudgetAnalysisChartData called with indicatorId: " + indicatorId + ", period: " + period);

        try {
            // 解析期间，获取年份
            String[] periodParts = period.split("-");
            if (periodParts.length != 2) {
                throw new IllegalArgumentException("期间格式错误，应为YYYY-MM格式");
            }

            int currentYear = Integer.parseInt(periodParts[0]);
            int currentMonth = Integer.parseInt(periodParts[1]);

            // 获取年度预算数据（以当月为中心）：办公费场景合并销售+管理两条预算
            BigDecimal annualBudget = BigDecimal.ZERO;
            try {
                // 办公费（销售费用）与 办公费（管理费用）固定ID
                final long OFFICE_SALES_ID = 1952675507458174978L;
                final long OFFICE_MGMT_ID = 1952675507458175077L;

                if (Objects.equals(indicatorId, OFFICE_SALES_ID) || Objects.equals(indicatorId, OFFICE_MGMT_ID)) {
                    BigDecimal sum = BigDecimal.ZERO;
                    // 销售办公费（优先 current_used，其次 midyearBudget，再次 initialBudget）
                    var budSales = indicatorAnnualBudgetService.findByIndicatorAndYear(OFFICE_SALES_ID, currentYear);
                    if (budSales != null) {
                        BigDecimal cur = budSales.getCurrentUsed();
                        BigDecimal mid = budSales.getMidyearBudget();
                        BigDecimal init = budSales.getInitialBudget();
                        if (cur != null && cur.signum() > 0)
                            sum = sum.add(cur);
                        else if (mid != null && mid.signum() > 0)
                            sum = sum.add(mid);
                        else if (init != null)
                            sum = sum.add(init);
                    }
                    // 管理办公费（优先 current_used，其次 midyearBudget，再次 initialBudget）
                    var budMgmt = indicatorAnnualBudgetService.findByIndicatorAndYear(OFFICE_MGMT_ID, currentYear);
                    if (budMgmt != null) {
                        BigDecimal cur = budMgmt.getCurrentUsed();
                        BigDecimal mid = budMgmt.getMidyearBudget();
                        BigDecimal init = budMgmt.getInitialBudget();
                        if (cur != null && cur.signum() > 0)
                            sum = sum.add(cur);
                        else if (mid != null && mid.signum() > 0)
                            sum = sum.add(mid);
                        else if (init != null)
                            sum = sum.add(init);
                    }
                    annualBudget = sum;
                } else {
                    // 普通指标：单条预算
                    var budgetEntity = indicatorAnnualBudgetService.findByIndicatorAndYear(indicatorId, currentYear);
                    if (budgetEntity != null) {
                        BigDecimal midyearBudget = budgetEntity.getMidyearBudget();
                        BigDecimal initialBudget = budgetEntity.getInitialBudget();
                        annualBudget = (midyearBudget != null && midyearBudget.signum() > 0) ? midyearBudget
                                : initialBudget;
                    }
                }
            } catch (Exception e) {
                System.err.println("获取年度预算失败: " + e.getMessage());
                // 如果获取年度预算失败，使用默认值
                annualBudget = BigDecimal.valueOf(1200000); // 默认120万年度预算
            }

            // 计算月度预算（全年预算/12）
            double monthlyBudget = annualBudget.doubleValue() / 12.0;

            // 获取过去12个月的月度数据（以当月为中心）
            List<Map<String, Object>> monthlyData = new ArrayList<>();
            double totalActual = 0.0;
            double totalBudget = 0.0;
            double currentMonthActual = 0.0;
            double currentMonthBudget = monthlyBudget;

            // 从当前月份往前推12个月
            for (int i = 11; i >= 0; i--) {
                int targetMonth = currentMonth - i;
                int targetYear = currentYear;

                if (targetMonth <= 0) {
                    targetMonth += 12;
                    targetYear -= 1;
                }

                String yearMonth = targetYear + "-" + String.format("%02d", targetMonth);

                // 获取实际费用数据
                BigDecimal actualAmount = indicatorValuesDetailService.sumAmountByIndicatorIdAndPeriod(indicatorId,
                        yearMonth);
                double actual = actualAmount != null ? actualAmount.doubleValue() : 0.0;

                // 使用年度预算均分得到的月度预算
                double budget = monthlyBudget;

                // 计算偏差
                double variance = actual - budget;
                double varianceRate = budget > 0 ? variance / budget : 0.0;

                // 标记是否为当前月份
                boolean isCurrentMonth = (targetYear == currentYear && targetMonth == currentMonth);

                Map<String, Object> monthData = new LinkedHashMap<>();
                monthData.put("month", yearMonth);
                monthData.put("budget", Math.round(budget * 100.0) / 100.0);
                monthData.put("actual", Math.round(actual * 100.0) / 100.0);
                monthData.put("variance", Math.round(variance * 100.0) / 100.0);
                monthData.put("varianceRate", Math.round(varianceRate * 10000.0) / 10000.0);
                monthData.put("isCurrentMonth", isCurrentMonth);
                monthlyData.add(monthData);

                totalActual += actual;
                totalBudget += budget;

                // 记录当前月份的实际值
                if (isCurrentMonth) {
                    currentMonthActual = actual;
                }
            }

            // 计算统计指标
            double totalVariance = totalActual - totalBudget;
            double budgetExecutionRate = totalBudget > 0 ? totalActual / totalBudget : 0.0;

            // 当月预算执行情况
            double currentMonthVariance = currentMonthActual - currentMonthBudget;
            double currentMonthExecutionRate = currentMonthBudget > 0 ? currentMonthActual / currentMonthBudget : 0.0;

            // 统计超支和节支月份
            long overBudgetMonths = monthlyData.stream().filter(m -> (Double) m.get("variance") > 0).count();
            long underBudgetMonths = monthlyData.stream().filter(m -> (Double) m.get("variance") < 0).count();

            // 找出最大超支和最大节支
            double maxOverBudget = monthlyData.stream()
                    .mapToDouble(m -> (Double) m.get("variance"))
                    .filter(v -> v > 0)
                    .max()
                    .orElse(0.0);

            double maxUnderBudget = monthlyData.stream()
                    .mapToDouble(m -> (Double) m.get("variance"))
                    .filter(v -> v < 0)
                    .min()
                    .orElse(0.0);

            // 组装指标数据
            Map<String, Object> indicators = new LinkedHashMap<>();
            indicators.put("totalBudget", Math.round(totalBudget * 100.0) / 100.0);
            indicators.put("totalActual", Math.round(totalActual * 100.0) / 100.0);
            indicators.put("totalVariance", Math.round(totalVariance * 100.0) / 100.0);
            indicators.put("budgetExecutionRate", Math.round(budgetExecutionRate * 10000.0) / 10000.0);
            indicators.put("overBudgetMonths", overBudgetMonths);
            indicators.put("underBudgetMonths", underBudgetMonths);
            indicators.put("maxOverBudget", Math.round(maxOverBudget * 100.0) / 100.0);
            indicators.put("maxUnderBudget", Math.round(maxUnderBudget * 100.0) / 100.0);

            // 当月相关指标
            indicators.put("currentMonthBudget", Math.round(currentMonthBudget * 100.0) / 100.0);
            indicators.put("currentMonthActual", Math.round(currentMonthActual * 100.0) / 100.0);
            indicators.put("currentMonthVariance", Math.round(currentMonthVariance * 100.0) / 100.0);
            indicators.put("currentMonthExecutionRate", Math.round(currentMonthExecutionRate * 10000.0) / 10000.0);
            indicators.put("annualBudget", Math.round(annualBudget.doubleValue() * 100.0) / 100.0);
            indicators.put("monthlyBudget", Math.round(monthlyBudget * 100.0) / 100.0);

            // 预算风险分析
            Map<String, Object> budgetRisk = new LinkedHashMap<>();

            // 计算风险分数
            double riskScore = 0.0;
            List<String> riskFactors = new ArrayList<>();

            // 当月预算执行率风险（权重更高）
            if (currentMonthExecutionRate > 1.15) {
                riskScore += 0.4;
                riskFactors.add("当月预算执行率过高");
            } else if (currentMonthExecutionRate > 1.05) {
                riskScore += 0.2;
                riskFactors.add("当月预算执行率偏高");
            } else if (currentMonthExecutionRate < 0.85) {
                riskScore += 0.1;
                riskFactors.add("当月预算执行率偏低");
            }

            // 整体预算执行率风险
            if (budgetExecutionRate > 1.1) {
                riskScore += 0.2;
                riskFactors.add("整体预算执行率过高");
            } else if (budgetExecutionRate < 0.9) {
                riskScore += 0.1;
                riskFactors.add("整体预算执行率过低");
            }

            // 超支月份风险
            if (overBudgetMonths > 8) {
                riskScore += 0.2;
                riskFactors.add("超支月份过多");
            } else if (overBudgetMonths > 6) {
                riskScore += 0.1;
                riskFactors.add("超支月份较多");
            }

            // 偏差幅度风险
            if (Math.abs(totalVariance) / totalBudget > 0.1) {
                riskScore += 0.1;
                riskFactors.add("预算偏差幅度较大");
            }

            // 连续超支风险
            int consecutiveOverBudget = 0;
            for (Map<String, Object> monthData : monthlyData) {
                if ((Double) monthData.get("variance") > 0) {
                    consecutiveOverBudget++;
                } else {
                    consecutiveOverBudget = 0;
                }
                if (consecutiveOverBudget >= 3) {
                    riskScore += 0.1;
                    riskFactors.add("连续超支");
                    break;
                }
            }

            // 确定风险等级
            String riskLevel = "low";
            if (riskScore >= 0.7) {
                riskLevel = "high";
            } else if (riskScore >= 0.4) {
                riskLevel = "medium";
            }

            budgetRisk.put("riskLevel", riskLevel);
            budgetRisk.put("riskScore", Math.round(riskScore * 100.0) / 100.0);
            budgetRisk.put("riskFactors", riskFactors);

            // 组装返回结构
            result.put("monthlyData", monthlyData);
            result.put("indicators", indicators);
            result.put("budgetRisk", budgetRisk);

            System.out.println("Budget analysis result: " + result);
            return result;

        } catch (Exception e) {
            System.err.println("generateBudgetAnalysisChartData error: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("生成预算分析数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> generateForecastAnalysisChartData(Long indicatorId, String period) {
        Map<String, Object> result = new LinkedHashMap<>();

        // 参数验证
        if (indicatorId == null) {
            throw new IllegalArgumentException("指标ID不能为空");
        }
        if (period == null || period.trim().isEmpty()) {
            throw new IllegalArgumentException("期间不能为空");
        }

        System.out.println(
                "generateForecastAnalysisChartData called with indicatorId: " + indicatorId + ", period: " + period);

        try {
            // 解析期间，获取年份
            String[] periodParts = period.split("-");
            if (periodParts.length != 2) {
                throw new IllegalArgumentException("期间格式错误，应为YYYY-MM格式");
            }

            int currentYear = Integer.parseInt(periodParts[0]);
            int currentMonth = Integer.parseInt(periodParts[1]);

            // 计算预算（与第6章一致：年度预算/12；办公费=销售+管理合并且优先current_used）
            BigDecimal annualBudget = BigDecimal.ZERO;
            try {
                final long OFFICE_SALES_ID = 1952675507458174978L;
                final long OFFICE_MGMT_ID = 1952675507458175077L;
                if (Objects.equals(indicatorId, OFFICE_SALES_ID) || Objects.equals(indicatorId, OFFICE_MGMT_ID)) {
                    BigDecimal sum = BigDecimal.ZERO;
                    var budSales = indicatorAnnualBudgetService.findByIndicatorAndYear(OFFICE_SALES_ID, currentYear);
                    if (budSales != null) {
                        BigDecimal cur = budSales.getCurrentUsed();
                        BigDecimal mid = budSales.getMidyearBudget();
                        BigDecimal init = budSales.getInitialBudget();
                        if (cur != null && cur.signum() > 0)
                            sum = sum.add(cur);
                        else if (mid != null && mid.signum() > 0)
                            sum = sum.add(mid);
                        else if (init != null)
                            sum = sum.add(init);
                    }
                    var budMgmt = indicatorAnnualBudgetService.findByIndicatorAndYear(OFFICE_MGMT_ID, currentYear);
                    if (budMgmt != null) {
                        BigDecimal cur = budMgmt.getCurrentUsed();
                        BigDecimal mid = budMgmt.getMidyearBudget();
                        BigDecimal init = budMgmt.getInitialBudget();
                        if (cur != null && cur.signum() > 0)
                            sum = sum.add(cur);
                        else if (mid != null && mid.signum() > 0)
                            sum = sum.add(mid);
                        else if (init != null)
                            sum = sum.add(init);
                    }
                    annualBudget = sum;
                } else {
                    var budgetEntity = indicatorAnnualBudgetService.findByIndicatorAndYear(indicatorId, currentYear);
                    if (budgetEntity != null) {
                        BigDecimal midyearBudget = budgetEntity.getMidyearBudget();
                        BigDecimal initialBudget = budgetEntity.getInitialBudget();
                        annualBudget = (midyearBudget != null && midyearBudget.signum() > 0) ? midyearBudget
                                : initialBudget;
                    }
                }
            } catch (Exception e) {
                System.err.println("获取年度预算失败(预测分析): " + e.getMessage());
                annualBudget = BigDecimal.ZERO;
            }
            double monthlyBudget = annualBudget.doubleValue() / 12.0;

            // 仅生成当年1-12月的月度数据
            List<Map<String, Object>> monthlyData = new ArrayList<>();
            List<Double> historicalValues = new ArrayList<>();
            final double[] currentValueHolder = { 0.0 };

            // 办公费双指标ID
            final long OFFICE_SALES_ID = 1952675507458174978L;
            final long OFFICE_MGMT_ID = 1952675507458175077L;

            for (int m = 1; m <= 12; m++) {
                String yearMonth = currentYear + "-" + String.format("%02d", m);
                double value = 0.0;
                if (m <= currentMonth) {
                    // 当为办公费任一指标时，按“销售办公费 + 管理办公费”合并取值；否则单指标
                    if (Objects.equals(indicatorId, OFFICE_SALES_ID) || Objects.equals(indicatorId, OFFICE_MGMT_ID)) {
                        BigDecimal vSales = indicatorValuesDetailService
                                .sumAmountByIndicatorIdAndPeriod(OFFICE_SALES_ID, yearMonth);
                        BigDecimal vMgmt = indicatorValuesDetailService
                                .sumAmountByIndicatorIdAndPeriod(OFFICE_MGMT_ID, yearMonth);
                        double s = vSales != null ? vSales.doubleValue() : 0.0;
                        double g = vMgmt != null ? vMgmt.doubleValue() : 0.0;
                        value = s + g;
                    } else {
                        BigDecimal monthTotal = indicatorValuesDetailService
                                .sumAmountByIndicatorIdAndPeriod(indicatorId, yearMonth);
                        value = monthTotal != null ? monthTotal.doubleValue() : 0.0;
                    }
                    historicalValues.add(value);
                }

                double budget = monthlyBudget;

                Map<String, Object> monthData = new LinkedHashMap<>();
                monthData.put("month", yearMonth);
                monthData.put("value", Math.round(value * 100.0) / 100.0);
                monthData.put("budget", Math.round(budget * 100.0) / 100.0);
                monthData.put("isCurrentMonth", m == currentMonth);
                monthlyData.add(monthData);

                if (m == currentMonth) {
                    currentValueHolder[0] = value;
                }
            }

            final double currentValue = currentValueHolder[0];

            // 使用按类别预测方法
            List<Map<String, Object>> categoryForecastResults = expenseForecastService
                    .forecastRemainingMonthsByCategory(indicatorId, period);
            System.out.println("按类别预测结果: " + categoryForecastResults);

            // 生成预测与历史数据（使用累计值）
            List<Map<String, Object>> predictedData = new ArrayList<>();
            List<Map<String, Object>> historicalData = new ArrayList<>();
            List<Map<String, Object>> budgetData = new ArrayList<>();

            // 历史（截至当月）的累计
            double runningActual = 0.0;
            for (int m = 1; m <= currentMonth; m++) {
                Map<String, Object> monthData = monthlyData.get(m - 1);
                String month = (String) monthData.get("month");
                double valueMonthly = (Double) monthData.get("value");
                runningActual += valueMonthly;
                double budgetCumul = monthlyBudget * m;

                Map<String, Object> historicalItem = new LinkedHashMap<>();
                historicalItem.put("month", month);
                historicalItem.put("value", Math.round(runningActual * 100.0) / 100.0);
                historicalItem.put("budget", Math.round(budgetCumul * 100.0) / 100.0);
                historicalData.add(historicalItem);

                Map<String, Object> budgetItem = new LinkedHashMap<>();
                budgetItem.put("month", month);
                budgetItem.put("budget", Math.round(budgetCumul * 100.0) / 100.0);
                budgetData.add(budgetItem);
            }

            // 生成未来预测（使用按类别预测的结果）
            double predCumul = runningActual;
            for (Map<String, Object> forecastResult : categoryForecastResults) {
                String yearMonth = (String) forecastResult.get("month");
                double predictedMonthly = ((Number) forecastResult.get("value")).doubleValue();
                predCumul += predictedMonthly;
                double predictedBudget = monthlyBudget * Integer.parseInt(yearMonth.split("-")[1]);

                Map<String, Object> predictedItem = new LinkedHashMap<>();
                predictedItem.put("month", yearMonth);
                predictedItem.put("value", Math.round(predCumul * 100.0) / 100.0);
                predictedItem.put("budget", Math.round(predictedBudget * 100.0) / 100.0);
                predictedData.add(predictedItem);
            }

            // 计算预测指标
            double currentMonthBudget = monthlyData.get(monthlyData.size() - 1).get("budget") != null
                    ? (Double) monthlyData.get(monthlyData.size() - 1).get("budget")
                    : currentValue * 1.1;
            double currentMonthVariance = currentValue - currentMonthBudget;
            double currentMonthExecutionRate = currentMonthBudget > 0 ? currentValue / currentMonthBudget : 0.0;

            // 计算累计数据
            double totalActual = historicalValues.stream().mapToDouble(Double::doubleValue).sum();
            double totalBudget = monthlyData.stream()
                    .mapToDouble(m -> (Double) m.get("budget"))
                    .sum();
            double totalVariance = totalActual - totalBudget;
            double overallExecutionRate = totalBudget > 0 ? totalActual / totalBudget : 0.0;

            // 预测准确度不再计算

            // 趋势方向（基于按类别预测的结果）
            String trendDirection = "基于AI模型预测";

            // 保留计算但不再使用置信度和准确度指标
            // double confidenceLevel = Math.min(0.95, forecastAccuracy * 0.9 + 0.1);

            // 组装指标数据
            Map<String, Object> indicators = new LinkedHashMap<>();
            indicators.put("currentMonthActual", Math.round(currentValue * 100.0) / 100.0);
            indicators.put("currentMonthBudget", Math.round(currentMonthBudget * 100.0) / 100.0);
            indicators.put("currentMonthVariance", Math.round(currentMonthVariance * 100.0) / 100.0);
            indicators.put("currentMonthExecutionRate", Math.round(currentMonthExecutionRate * 1000.0) / 1000.0);
            indicators.put("totalActual", Math.round(totalActual * 100.0) / 100.0);
            indicators.put("totalBudget", Math.round(totalBudget * 100.0) / 100.0);
            indicators.put("totalVariance", Math.round(totalVariance * 100.0) / 100.0);
            indicators.put("overallExecutionRate", Math.round(overallExecutionRate * 1000.0) / 1000.0);
            indicators.put("trendDirection", trendDirection);

            // 预测数据详情
            Map<String, Object> forecastData = new LinkedHashMap<>();
            forecastData.put("historicalData", historicalData);
            forecastData.put("predictedData", predictedData);
            forecastData.put("budgetData", budgetData);
            forecastData.put("predictionMethod", "CategoryBasedMLOps");

            // 置信区间
            Map<String, Object> confidenceInterval = new LinkedHashMap<>();
            double stdDev = Math.sqrt(historicalValues.stream()
                    .mapToDouble(v -> Math.pow(
                            v - historicalValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0), 2))
                    .average().orElse(0.0));

            confidenceInterval.put("upperBound", Math.round((currentValue + 2 * stdDev) * 100.0) / 100.0);
            confidenceInterval.put("lowerBound", Math.round((currentValue - 2 * stdDev) * 100.0) / 100.0);
            confidenceInterval.put("confidenceLevel", 0.95);
            forecastData.put("confidenceInterval", confidenceInterval);

            // 组装返回结构
            result.put("monthlyData", monthlyData);
            result.put("indicators", indicators);
            result.put("forecastData", forecastData);

            System.out.println("Forecast analysis result: " + result);
            return result;

        } catch (Exception e) {
            System.err.println("generateForecastAnalysisChartData error: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("生成预测分析数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行线性回归预测
     * 
     * @param values 历史数据
     * @return [slope, intercept, rSquared]
     */
    private double[] performLinearRegression(List<Double> values) {
        int n = values.size();
        if (n < 2) {
            return new double[] { 0.0, values.isEmpty() ? 0.0 : values.get(0), 0.0 };
        }

        // 创建x坐标（时间序列）
        double[] x = new double[n];
        for (int i = 0; i < n; i++) {
            x[i] = i;
        }

        // 计算均值
        double xMean = 0.0, yMean = 0.0;
        for (int i = 0; i < n; i++) {
            xMean += x[i];
            yMean += values.get(i);
        }
        xMean /= n;
        yMean /= n;

        // 计算斜率和截距
        double numerator = 0.0, denominator = 0.0;
        for (int i = 0; i < n; i++) {
            numerator += (x[i] - xMean) * (values.get(i) - yMean);
            denominator += (x[i] - xMean) * (x[i] - xMean);
        }

        double slope = denominator != 0 ? numerator / denominator : 0.0;
        double intercept = yMean - slope * xMean;

        // 计算R²
        double ssRes = 0.0, ssTot = 0.0;
        for (int i = 0; i < n; i++) {
            double predicted = slope * x[i] + intercept;
            ssRes += Math.pow(values.get(i) - predicted, 2);
            ssTot += Math.pow(values.get(i) - yMean, 2);
        }

        double rSquared = ssTot != 0 ? 1 - (ssRes / ssTot) : 0.0;

        return new double[] { slope, intercept, rSquared };
    }
}
