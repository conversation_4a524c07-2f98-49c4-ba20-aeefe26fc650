import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/yjzb/indicatorValues/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/yjzb/indicatorValues/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/yjzb/indicatorValues/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/yjzb/indicatorValues/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/yjzb/indicatorValues/submit',
    method: 'post',
    data: row
  })
}

export const getListByType = (current, size, indicatorTypeId, params = {}) => {
  return request({
    url: '/yjzb/indicatorValues/listByType',
    method: 'get',
    params: {
      indicatorTypeId,
      ...params,
      current,
      size,
    }
  })
}

export const getStatistics = (period, indicatorTypeId, params = {}) => {
  return request({
    url: '/yjzb/indicatorValues/statistics',
    method: 'get',
    params: {
      period,
      indicatorTypeId,
      ...params
    }
  })
}

// 按指标ID查询（支持透传 period_like 等筛选），用于折线图趋势数据
export const getListByIndicator = (current, size, indicatorId, params = {}) => {
  return request({
    url: '/yjzb/indicatorValues/list',
    method: 'get',
    params: {
      indicatorId_equal: indicatorId,
      ...params,
      current,
      size,
    }
  })
}

// 重新开始AI分析
export const restartAiAnalysis = (indicatorValueId) => {
  return request({
    url: '/yjzb/indicatorValues/restart-ai-analysis',
    method: 'post',
    params: { indicatorValueId }
  })
}

// 查询最新AI分析结果（按指标值ID）
export const getLatestAiAnalysis = (indicatorValueId) => {
  return request({
    url: '/yjzb/indicatorValues/latest-ai-analysis',
    method: 'get',
    params: { indicatorValueId }
  })
}

// 按指标ID+期间查询最新AI解读
export const getLatestAiByIndicator = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicatorValues/ai/latest-by-indicator',
    method: 'get',
    params: { indicatorId, period }
  })
}

// 新：按指标+期间 预测并落库 + 启动AI解读
export const predictAndAnalyzeByIndicator = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicatorValues/ai/predict-and-analyze',
    method: 'post',
    params: { indicatorId, period }
  })
}

// 新：按指标+期间 预测并落库 + 启动AI解读（自定义提示词）
export const predictAndAnalyzeByIndicatorWithPrompt = (indicatorId, period, prompt) => {
  return request({
    url: '/yjzb/indicatorValues/ai/predict-and-analyze-with-prompt',
    method: 'post',
    params: { indicatorId, period, prompt }
  })
}

// 获取指标预测分析的提示词预览
export const getIndicatorPromptPreview = (indicatorId, period) => {
  return request({
    url: '/yjzb/indicatorValues/ai/prompt-preview',
    method: 'get',
    params: { indicatorId, period }
  });
};

// 获取指标关联的预测类业务知识列表
export const getIndicatorBusinessRules = (indicatorId) => {
  return request({
    url: '/yjzb/indicatorValues/ai/business-rules',
    method: 'get',
    params: { indicatorId }
  });
}

// 获取指标关联的业务知识详细信息（按分类分组）
export const getBusinessKnowledgeByCategory = (indicatorId) => {
  return request({
    url: '/yjzb/indicatorValues/ai/business-knowledge-by-category',
    method: 'get',
    params: { indicatorId }
  })
}

// 保存或更新业务知识
export const saveBusinessKnowledge = (businessKnowledge) => {
  return request({
    url: '/yjzb/indicatorValues/ai/business-knowledge/save',
    method: 'post',
    data: businessKnowledge
  })
}

// 删除业务知识
export const deleteBusinessKnowledge = (id) => {
  return request({
    url: '/yjzb/indicatorValues/ai/business-knowledge/delete',
    method: 'post',
    params: { id }
  })
}

// 获取业务知识分类列表
export const getBusinessKnowledgeCategories = (indicatorId) => {
  return request({
    url: '/yjzb/indicatorValues/ai/business-knowledge-categories',
    method: 'get',
    params: { indicatorId }
  })
}

// 获取所有财务分类列表
export const getFinanceCategories = () => {
  return request({
    url: '/yjzb/indicatorValues/ai/finance-categories',
    method: 'get'
  })
};

// 更新业务知识的分类（拖拽功能）
export const updateBusinessKnowledgeCategory = (knowledgeId, categoryId) => {
  return request({
    url: '/yjzb/indicatorValues/ai/business-knowledge/update-category',
    method: 'post',
    params: { knowledgeId, categoryId }
  })
};


// 当月某类型AI指标解读 - 重启并记录
export const restartMonthlyTypeAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/monthlyTypeAnalysis/restart',
    method: 'post',
    params: { indicatorTypeId, period }
  })
}

// 当月某类型AI指标解读 - 查询最新
export const getLatestMonthlyTypeAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/monthlyTypeAnalysis/latest',
    method: 'get',
    params: { indicatorTypeId, period }
  })
}

// 总览AI分析 - 费用管理
export const restartExpenseOverviewAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/overview/expense/restart',
    method: 'post',
    params: { indicatorTypeId, period }
  })
}

// 总览AI分析 - 资产负债
export const restartBalanceOverviewAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/overview/balance/restart',
    method: 'post',
    params: { indicatorTypeId, period }
  })
}

// 总览AI分析 - 利润
export const restartProfitOverviewAnalysis = (indicatorTypeId, period) => {
  return request({
    url: '/yjzb/indicatorValues/overview/profit/restart',
    method: 'post',
    params: { indicatorTypeId, period }
  })
}

// Excel数据导入
export const importExcelData = (formData) => {
  return request({
    url: '/yjzb/indicatorValues/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

