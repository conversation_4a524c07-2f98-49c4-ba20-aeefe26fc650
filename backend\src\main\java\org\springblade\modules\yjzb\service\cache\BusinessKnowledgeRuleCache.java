package org.springblade.modules.yjzb.service.cache;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 业务知识规则缓存：按 指标ID + 规则类型(knowledgeType) 缓存 Markdown 列表规则。
 * key 格式："<indicatorId>:<knowledgeType>"，例如 "19526:1"。
 */
@Slf4j
@Component

public class BusinessKnowledgeRuleCache {

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 聚合后的规则缓存：key = indicatorId:knowledgeType -> Markdown 列表文本
    private final ConcurrentHashMap<String, String> cache = new ConcurrentHashMap<>();
    // 细粒度缓存：每个 key 下按实体ID存储贡献的行列表，用于增量更新
    private final ConcurrentHashMap<String, ConcurrentHashMap<Long, List<String>>> keyEntityLines = new ConcurrentHashMap<>();
    // 反向索引：实体ID -> 其贡献过的所有 key 集合
    private final ConcurrentHashMap<Long, Set<String>> entityToKeys = new ConcurrentHashMap<>();

    public String get(Long indicatorId, int knowledgeType) {
        if (indicatorId == null)
            return "";
        return cache.getOrDefault(key(indicatorId, knowledgeType), "");
    }

    public synchronized void rebuildWith(java.util.List<BusinessKnowledgeEntity> all) {
        long start = System.currentTimeMillis();
        Map<String, List<String>> temp = new HashMap<>();
        keyEntityLines.clear();
        entityToKeys.clear();
        try {
            if (all == null)
                all = java.util.Collections.emptyList();
            for (BusinessKnowledgeEntity bk : all) {
                if (bk == null || bk.getId() == null)
                    continue;
                int type = bk.getKnowledgeType() == null ? 0 : bk.getKnowledgeType();
                if (type == 0)
                    continue;
                String content = sanitize(bk.getKnowledgeContent());
                if (content.isEmpty())
                    continue;
                Set<String> idSet = parseApplicableIndicators(bk.getApplicableIndicators());
                for (String idStr : idSet) {
                    String k = key(Long.valueOf(idStr), type);
                    temp.computeIfAbsent(k, x -> new ArrayList<>()).add("- " + content);
                    keyEntityLines
                            .computeIfAbsent(k, x -> new ConcurrentHashMap<>())
                            .computeIfAbsent(bk.getId(), x -> new ArrayList<>())
                            .add("- " + content);
                    entityToKeys.computeIfAbsent(bk.getId(), x -> new HashSet<>()).add(k);
                }
            }
        } catch (Exception e) {
            log.warn("[BusinessKnowledgeRuleCache] rebuild 异常: {}", e.getMessage());
        }
        // 合并并替换聚合缓存
        ConcurrentHashMap<String, String> newMap = new ConcurrentHashMap<>();
        for (Map.Entry<String, List<String>> e : temp.entrySet()) {
            newMap.put(e.getKey(), String.join("\n", e.getValue()));
        }
        cache.clear();
        cache.putAll(newMap);
        log.info("[BusinessKnowledgeRuleCache] rebuild 完成, keys={}, cost={}ms", cache.size(),
                System.currentTimeMillis() - start);
    }

    /**
     * 增量更新：插入或更新单条业务知识的贡献
     */
    public synchronized void upsertEntity(BusinessKnowledgeEntity bk) {
        if (bk == null || bk.getId() == null)
            return;
        // 先清除旧贡献
        removeEntityInternal(bk.getId());
        // 再写入新贡献
        int type = bk.getKnowledgeType() == null ? 0 : bk.getKnowledgeType();
        if (type == 0)
            return;
        String content = sanitize(bk.getKnowledgeContent());
        if (content.isEmpty())
            return;
        Set<String> idSet = parseApplicableIndicators(bk.getApplicableIndicators());
        for (String idStr : idSet) {
            Long indicatorId = Long.valueOf(idStr);
            String k = key(indicatorId, type);
            keyEntityLines
                    .computeIfAbsent(k, x -> new ConcurrentHashMap<>())
                    .computeIfAbsent(bk.getId(), x -> new ArrayList<>())
                    .add("- " + content);
            entityToKeys.computeIfAbsent(bk.getId(), x -> new HashSet<>()).add(k);
            // 重新聚合该 key
            aggregateKey(k);
        }
    }

    /**
     * 增量更新：根据实体ID删除其贡献
     */
    public synchronized void removeEntity(Long entityId) {
        if (entityId == null)
            return;
        removeEntityInternal(entityId);
    }

    private void removeEntityInternal(Long entityId) {
        Set<String> keys = entityToKeys.remove(entityId);
        if (keys == null || keys.isEmpty())
            return;
        for (String k : keys) {
            ConcurrentHashMap<Long, List<String>> entityMap = keyEntityLines.get(k);
            if (entityMap != null) {
                entityMap.remove(entityId);
                if (entityMap.isEmpty()) {
                    keyEntityLines.remove(k);
                }
            }
            aggregateKey(k);
        }
    }

    private void aggregateKey(String k) {
        ConcurrentHashMap<Long, List<String>> entityMap = keyEntityLines.get(k);
        if (entityMap == null || entityMap.isEmpty()) {
            cache.remove(k);
            return;
        }
        List<String> lines = new ArrayList<>();
        for (List<String> ls : entityMap.values()) {
            lines.addAll(ls);
        }
        cache.put(k, String.join("\n", lines));
    }

    private String key(Long indicatorId, int type) {
        return indicatorId + ":" + type;
    }

    private String sanitize(String content) {
        if (content == null)
            return "";
        String line = content.trim();
        if (line.isEmpty())
            return "";
        return line.replace('\r', ' ').replace('\n', ' ');
    }

    private Set<String> parseApplicableIndicators(String raw) {
        Set<String> set = new HashSet<>();
        if (raw == null || raw.isBlank())
            return set;
        String s = raw.trim();
        try {
            if (s.startsWith("[") && s.endsWith("]")) {
                List<Object> arr = objectMapper.readValue(s, new TypeReference<List<Object>>() {
                });
                for (Object o : arr) {
                    if (o == null)
                        continue;
                    String id = String.valueOf(o).trim();
                    if (!id.isEmpty())
                        set.add(id);
                }
                return set;
            }
        } catch (Exception ignore) {
        }
        // fallback: comma separated
        for (String p : s.split(",")) {
            String id = p.trim();
            if (!id.isEmpty())
                set.add(id);
        }
        return set;
    }
}
