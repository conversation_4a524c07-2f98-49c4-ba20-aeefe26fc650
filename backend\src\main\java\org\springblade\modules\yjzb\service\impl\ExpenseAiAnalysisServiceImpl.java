package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.yjzb.entity.ExpenseAiAnalysis;
import org.springblade.modules.yjzb.mapper.ExpenseAiAnalysisMapper;
import org.springblade.modules.yjzb.service.ExpenseAiAnalysisService;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springblade.modules.yjzb.service.IIndicatorDeepAnalysisService;
import org.springblade.modules.yjzb.service.cache.BusinessKnowledgeRuleCache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 办公费用AI解读分析服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExpenseAiAnalysisServiceImpl extends ServiceImpl<ExpenseAiAnalysisMapper, ExpenseAiAnalysis>
        implements ExpenseAiAnalysisService {

    private final IDifyService difyService;
    private final ObjectMapper objectMapper;
    private final IIndicatorDeepAnalysisService indicatorDeepAnalysisService;
    private final BusinessKnowledgeRuleCache businessKnowledgeRuleCache;

    private static final int KNOWLEDGE_TYPE_ATTRIBUTION = 2; // 归因知识类型

    // 已去掉difyApiKey字段，不再使用，仅使用下面的agentkey

    @Value("${dify.api.agentkey.expenseanalysisagent:}")
    private String expenseAnalysisAgentKey;

    @Override
    public String executeAnalysis(Long indicatorId, String period, String analysisType, String inputParams,
            boolean force) {
        log.info("开始执行办公费用AI分析 - indicatorId: {}, period: {}, analysisType: {}", indicatorId, period, analysisType);

        // 检查是否有缓存结果（当未强制刷新时）
        if (!force && hasCachedResult(indicatorId, period, analysisType)) {
            log.info("发现缓存的分析结果，直接返回");
            ExpenseAiAnalysis cachedResult = getAnalysisResult(indicatorId, period, analysisType);
            return cachedResult.getAnswerContent();
        }

        // 创建分析记录
        ExpenseAiAnalysis analysis = new ExpenseAiAnalysis();
        analysis.setIndicatorId(indicatorId);
        analysis.setPeriod(period);
        analysis.setAnalysisType(analysisType);
        // 后端统一构造传入Agent的数据
        String dataTextForAgent;
        try {
            Map<String, Object> dataObj = buildDataForAnalysisType(indicatorId, period, analysisType);
            dataTextForAgent = objectMapper.writeValueAsString(dataObj);
        } catch (Exception e) {
            log.error("构造传入Agent的数据失败", e);
            dataTextForAgent = inputParams; // 兜底：仍然记录前端传入，避免丢失
        }
        analysis.setInputParams(dataTextForAgent);
        analysis.setExecuteTime(LocalDateTime.now());
        analysis.setExecuteStatus("RUNNING");

        // 保存到数据库
        save(analysis);

        try {
            // 调用Dify API进行AI分析（仅 data 与 prompt）
            String result = callDifyApi(indicatorId, analysisType, dataTextForAgent);

            // 更新分析结果
            analysis.setExecuteStatus("COMPLETED");
            analysis.setResult(result);
            analysis.setAnswerContent(result);
            analysis.setExecuteTime(LocalDateTime.now());

            updateById(analysis);

            log.info("AI分析完成 - analysisId: {}", analysis.getId());
            return result;

        } catch (Exception e) {
            log.error("AI分析失败", e);

            // 更新失败状态
            analysis.setExecuteStatus("FAILED");
            analysis.setResult("分析失败: " + e.getMessage());
            updateById(analysis);

            throw new RuntimeException("AI分析失败: " + e.getMessage());
        }
    }

    @Override
    public ExpenseAiAnalysis getAnalysisResult(Long indicatorId, String period, String analysisType) {
        LambdaQueryWrapper<ExpenseAiAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseAiAnalysis::getIndicatorId, indicatorId)
                .eq(ExpenseAiAnalysis::getPeriod, period)
                .eq(ExpenseAiAnalysis::getAnalysisType, analysisType)
                .eq(ExpenseAiAnalysis::getExecuteStatus, "COMPLETED")
                .orderByDesc(ExpenseAiAnalysis::getExecuteTime)
                .last("LIMIT 1");

        return getOne(queryWrapper);
    }

    @Override
    public boolean hasCachedResult(Long indicatorId, String period, String analysisType) {
        LambdaQueryWrapper<ExpenseAiAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseAiAnalysis::getIndicatorId, indicatorId)
                .eq(ExpenseAiAnalysis::getPeriod, period)
                .eq(ExpenseAiAnalysis::getAnalysisType, analysisType)
                .eq(ExpenseAiAnalysis::getExecuteStatus, "COMPLETED");

        return count(queryWrapper) > 0;
    }

    /**
     * 调用Dify API进行AI分析
     *
     * @param analysisType 分析类型
     * @param inputParams  输入参数
     * @return AI分析结果
     */
    private String callDifyApi(Long indicatorId, String analysisType, String inputParams) {
        try {
            log.info("调用Dify API(仅 data 与 prompt) - analysisType: {}", analysisType);

            // 构造仅包含 data 与 prompt 的 inputs
            // 上游已构造为合法JSON文本，这里直接传递
            String dataText = inputParams == null ? "" : inputParams;

            String prompt = buildPromptForAnalysisType(indicatorId, analysisType);
            Map<String, Object> inputs = new java.util.HashMap<>();
            inputs.put("data", dataText);
            inputs.put("prompt", prompt);

            // 获取API Key - 优先使用费用分析专用的agent key
            String apiKey = (expenseAnalysisAgentKey != null && !expenseAnalysisAgentKey.isBlank())
                    ? expenseAnalysisAgentKey
                    : null;

            // 启动Dify工作流 - 参数顺序：inputs, user, apiKey
            String workflowRunId = difyService.startWorkflowStreaming(inputs, "expense_analysis", apiKey);
            if (workflowRunId == null) {
                log.error("启动Dify工作流失败，analysisType={}", analysisType);
                throw new RuntimeException("启动Dify工作流失败");
            }

            log.info("启动Dify工作流成功 - analysisType: {}, workflowRunId: {}", analysisType, workflowRunId);

            // 轮询获取结果
            return pollDifyResult(workflowRunId, apiKey);

        } catch (Exception e) {
            log.error("调用Dify API失败", e);
            throw new RuntimeException("调用Dify API失败: " + e.getMessage());
        }
    }

    /**
     * 根据分析类型生成提示词
     */
    // 新增：对外暴露的预览方法
    @Override
    public String buildPromptPreview(Long indicatorId, String analysisType) {
        return buildPromptForAnalysisType(indicatorId, analysisType);
    }

    @Override
    public java.util.List<String> getBusinessRulesList(Long indicatorId) {
        try {
            log.info("获取业务规则列表 indicatorId={}, knowledgeType={}", indicatorId, KNOWLEDGE_TYPE_ATTRIBUTION);
            String rules = businessKnowledgeRuleCache.get(indicatorId, KNOWLEDGE_TYPE_ATTRIBUTION);
            log.info("从缓存获取到的规则内容: indicatorId={}, rules={}", indicatorId, rules);

            if (rules == null || rules.isBlank()) {
                // 通用兜底：尝试读取通用规则（indicatorId=0 或 -1）
                log.info("指标专属规则为空，尝试获取通用规则");
                String common0 = businessKnowledgeRuleCache.get(0L, KNOWLEDGE_TYPE_ATTRIBUTION);
                String commonNeg = businessKnowledgeRuleCache.get(-1L, KNOWLEDGE_TYPE_ATTRIBUTION);
                log.info("通用规则: common0={}, commonNeg={}", common0, commonNeg);
                rules = (common0 != null && !common0.isBlank()) ? common0 : (commonNeg != null ? commonNeg : "");
            }

            if (rules == null || rules.isBlank()) {
                log.warn("未找到任何业务规则 indicatorId={}", indicatorId);
                return java.util.Arrays.asList("暂无匹配的业务知识规则");
            }

            // 将规则文本按行分割，过滤空行和仅包含符号的行
            java.util.List<String> rulesList = java.util.Arrays.stream(rules.split("\n"))
                    .map(String::trim)
                    .filter(line -> !line.isEmpty() && !line.matches("^[-*+\\s]*$"))
                    .map(line -> line.replaceFirst("^[-*+]\\s*", "")) // 移除行首的列表符号
                    .filter(line -> !line.isEmpty())
                    .collect(java.util.stream.Collectors.toList());

            log.info("解析后的规则列表 indicatorId={}, count={}, rules={}", indicatorId, rulesList.size(), rulesList);
            return rulesList;
        } catch (Exception e) {
            log.warn("获取业务规则列表失败 indicatorId={}, err={}", indicatorId, e.getMessage());
            return java.util.Arrays.asList("业务知识规则加载失败");
        }
    }

    private String buildPromptForAnalysisType(Long indicatorId, String analysisType) {
        String common = "/no_think 你是一名资深经营管理与财务分析专家。系统通过 inputs 传入 JSON 字符串 data（包含 indicatorId、period 及各章节的结构化数据）。\n"
                +
                "请严格以 data 为唯一信息源开展业务驱动的深度分析，避免过度数理化表达：\n" +
                "- 不臆造数据，不引用外部样本或行业通识；\n" +
                "- 结论必须从业务角度解释数据变化的根本原因，明确与经营活动的关联；\n" +
                "- 每条结论均需包含：现象 → 依据（来自 data 的字段/对比，这里需要把他用中文进行翻译，而不是英文字段）→ 业务原因解释；\n" +
                "- 优先参考“业务规则约束”中的知识作为解释依据，与数据相互印证；如与建议冲突，以规则为准并说明原因；\n" +
                "- 数据不足时，带有不确定性的内容不输出，避免越权推断；\n" +
                "- 仅输出 Markdown 正文，不要输出 JSON 或无关说明。\n" +
                "\n" +
                "金额统一使用“¥”并标注单位，百分比保留 1 位小数。\n" +
                "\n" +
                "分析时请参考以下业务规则约束：" +
                "- 以下为与当前指标相关的归因类业务规则。若为空则忽略本节；若建议与规则冲突，以规则为准：\n" +
                "{{BUSINESS_RULES}}\n" +
                "推荐输出结构（以当前月份为主体，历史仅作对比基线）：\n" +
                "## 本月经营解读（现象-原因）\n";
        // "## 关键业务驱动因素（因素-依据-影响范围）\n"+
        // "## 管理建议与后续动作（按优先级给出负责人/时间节点）\n" +
        // "## 风险与不确定性（关注经营稳定性与执行风险，避免纯统计阈值用语）\n";

        String focus;
        switch (analysisType) {
            case "overview":
                focus = "侧重点：从经营管理角度解读费用总体状况，关注对业务运营的实际影响（成本压力、费用效率、投入产出、与预算/目标一致性）。";
                break;
            case "structure":
                focus = "侧重点：解释费用结构变化背后的业务决策与运营调整（投放策略、渠道/区域/品类组合、组织与采购策略变化等）。";
                break;
            case "trend":
                focus = "侧重点：解释趋势变化的业务驱动因素（市场环境、季节性、价格/合同条款调整、战略/渠道/产品/客户组合变动等）；判断趋势的持续性与潜在拐点，并给出业务信号与对应动作。";
                break;
            case "volatility":
                focus = "侧重点：从业务稳定性出发分析波动原因（执行节奏、费用确认口径、一次性事项、供应/交付不确定性等）。";
                break;
            case "growth":
                focus = "侧重点：解释增长变化的业务逻辑（业务扩张、市场变化、客单/转化、产能与交付能力、政策与税费等）.";
                break;
            case "budget":
                focus = "侧重点：从预算管理与成本控制视角分析执行偏差的业务原因（编制口径、执行时点错配、价格/政策变化、专项投放等）。";
                break;
            case "forecast":
                focus = "侧重点：结合经营计划与在手信息进行前瞻性研判（业务推进节奏、合同/项目储备、采购与价格趋势、政策环境等）。";
                break;
            default:
                focus = "侧重点：围绕本章节数据最能说明的问题进行业务驱动的重点解读。";
        }

        String rulesText = resolveBusinessRules(indicatorId, analysisType);
        String commonWithRules = common.replace("{{BUSINESS_RULES}}", rulesText);
        return commonWithRules + "\n\n" + "\n" + focus;

    }

    /**
     * 从业务知识缓存获取与当前指标最相关的归因类规则，并格式化为 Markdown 文本
     */
    private String resolveBusinessRules(Long indicatorId, String analysisType) {
        try {
            String rules = businessKnowledgeRuleCache.get(indicatorId, KNOWLEDGE_TYPE_ATTRIBUTION);
            if (rules == null || rules.isBlank()) {
                // 通用兜底：尝试读取通用规则（indicatorId=0 或 -1）
                String common0 = businessKnowledgeRuleCache.get(0L, KNOWLEDGE_TYPE_ATTRIBUTION);
                String commonNeg = businessKnowledgeRuleCache.get(-1L, KNOWLEDGE_TYPE_ATTRIBUTION);
                rules = (common0 != null && !common0.isBlank()) ? common0 : (commonNeg != null ? commonNeg : "");
            }
            if (rules == null || rules.isBlank()) {
                return "- （暂无匹配规则，按通用业务分析框架解读）";
            }
            return rules;
        } catch (Exception e) {
            log.warn("加载业务规则失败 indicatorId={}, type={}, err={}", indicatorId, analysisType, e.getMessage());
            return "- （业务规则加载失败，按通用业务分析框架解读）";
        }
    }

    // 已移除未使用的工作流ID映射方法，Agent只依赖 data 与 prompt

    /**
     * 根据章节组装传入 Agent 的 data 对象
     */
    private Map<String, Object> buildDataForAnalysisType(Long indicatorId, String period, String analysisType) {
        Map<String, Object> data = new java.util.LinkedHashMap<>();
        data.put("indicatorId", indicatorId);
        data.put("period", period);

        try {
            switch (analysisType) {
                case "overview":
                    // 总览可复用结构、趋势、预算等的关键片段
                    data.put("structure",
                            indicatorDeepAnalysisService.generateCategoryStructureChartData(indicatorId, period));
                    data.put("trend",
                            indicatorDeepAnalysisService.generateTrendComparisonChartData(indicatorId, period));
                    data.put("budget",
                            indicatorDeepAnalysisService.generateBudgetAnalysisChartData(indicatorId, period));
                    break;
                case "structure":
                    data.put("structure",
                            indicatorDeepAnalysisService.generateCategoryStructureChartData(indicatorId, period));
                    break;
                case "trend":
                    data.put("trend",
                            indicatorDeepAnalysisService.generateTrendComparisonChartData(indicatorId, period));
                    break;
                case "volatility":
                    data.put("volatility",
                            indicatorDeepAnalysisService.generateVolatilityAnalysisChartData(indicatorId, period));
                    break;
                case "growth":
                    data.put("growth",
                            indicatorDeepAnalysisService.generateGrowthAnalysisChartData(indicatorId, period));
                    break;
                case "budget":
                    data.put("budget",
                            indicatorDeepAnalysisService.generateBudgetAnalysisChartData(indicatorId, period));
                    break;
                case "forecast":
                    data.put("forecast",
                            indicatorDeepAnalysisService.generateForecastAnalysisChartData(indicatorId, period));
                    break;
                default:
                    // 默认提供结构+趋势的核心信息
                    data.put("structure",
                            indicatorDeepAnalysisService.generateCategoryStructureChartData(indicatorId, period));
                    data.put("trend",
                            indicatorDeepAnalysisService.generateTrendComparisonChartData(indicatorId, period));
            }
        } catch (Exception e) {
            log.error("构建章节数据失败: indicatorId={}, period={}, type={}", indicatorId, period, analysisType, e);
        }

        return data;
    }

    /**
     * 轮询Dify结果
     */
    private String pollDifyResult(String workflowRunId, String apiKey) {
        int maxAttempts = 30; // 最多轮询30次
        int attempt = 0;

        while (attempt < maxAttempts) {
            try {
                Thread.sleep(10000); // 等待10秒

                String detail = difyService.getWorkflowRunDetail(workflowRunId, apiKey);
                if (detail == null || detail.isBlank()) {
                    log.warn("未获取到工作流详情: workflowRunId={}", workflowRunId);
                    attempt++;
                    continue;
                }

                // 提取状态
                String status = extractStatus(detail);
                String lower = status == null ? null : status.toLowerCase();

                log.info("轮询结果: workflowRunId={}, status={}", workflowRunId, lower);

                if ("succeeded".equals(lower) || "completed".equals(lower)) {
                    // 提取输出文本
                    String outputText = extractOutputText(detail);
                    return outputText != null ? outputText : detail;
                } else if ("failed".equals(lower) || "error".equals(lower)) {
                    throw new RuntimeException("Dify工作流执行失败: " + detail);
                }

                attempt++;

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("轮询被中断");
            } catch (Exception e) {
                log.error("轮询Dify结果异常", e);
                attempt++;
            }
        }

        throw new RuntimeException("轮询超时，未获取到结果");
    }

    /**
     * 提取工作流状态字段
     */
    private String extractStatus(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;

            // 直接在根查找
            if (node.has("status")) {
                return node.get("status").asText();
            }

            // 在 data 节点查找
            if (node.has("data")) {
                com.fasterxml.jackson.databind.JsonNode data = node.get("data");
                if (data.isObject() && data.has("status")) {
                    return data.get("status").asText();
                }
                if (data.isTextual()) {
                    String dataText = data.asText();
                    com.fasterxml.jackson.databind.JsonNode dataObj = objectMapper.readTree(dataText);
                    if (dataObj != null && dataObj.has("status")) {
                        return dataObj.get("status").asText();
                    }
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 提取 outputs.text 内容
     */
    private String extractOutputText(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;

            com.fasterxml.jackson.databind.JsonNode outputs = node.get("outputs");
            if (outputs == null)
                return null;

            if (outputs.isObject()) {
                com.fasterxml.jackson.databind.JsonNode text = outputs.get("text");
                return text != null ? text.asText() : outputs.toString();
            }

            if (outputs.isTextual()) {
                String outputsText = outputs.asText();
                com.fasterxml.jackson.databind.JsonNode outObj = objectMapper.readTree(outputsText);
                if (outObj != null && outObj.has("text")) {
                    return outObj.get("text").asText();
                }
                return outputsText;
            }
        } catch (Exception ignore) {
        }
        return null;
    }
}
