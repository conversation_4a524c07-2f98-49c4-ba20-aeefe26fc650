/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesDetailEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;
import java.math.BigDecimal;

/**
 * 指标数据明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Mapper
public interface IndicatorValuesDetailMapper extends BaseMapper<IndicatorValuesDetailEntity> {

    /**
     * 分页查询指标数据明细
     *
     * @param page                  分页参数
     * @param indicatorValuesDetail 查询条件
     * @return 分页结果
     */
    IPage<IndicatorValuesDetailVO> selectIndicatorValuesDetailPage(IPage<IndicatorValuesDetailVO> page,
            @Param("indicatorValuesDetail") IndicatorValuesDetailEntity indicatorValuesDetail);

    /**
     * 根据指标ID和期间查询明细列表
     *
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 明细列表
     */
    List<IndicatorValuesDetailVO> selectByIndicatorIdAndPeriod(@Param("indicatorId") Long indicatorId,
            @Param("period") String period);

    /**
     * 根据指标ID和期间区间查询明细列表
     *
     * @param indicatorId 指标ID
     * @param startPeriod 开始期间
     * @param endPeriod   结束期间
     * @return 明细列表
     */
    List<IndicatorValuesDetailVO> selectByIndicatorIdAndPeriodRange(@Param("indicatorId") Long indicatorId,
            @Param("startPeriod") String startPeriod, @Param("endPeriod") String endPeriod);

    /**
     * 根据指标ID和期间计算总金额
     *
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 总金额
     */
    BigDecimal sumAmountByIndicatorIdAndPeriod(@Param("indicatorId") Long indicatorId, @Param("period") String period);

    /**
     * 根据指标ID和期间区间计算总金额
     *
     * @param indicatorId 指标ID
     * @param startPeriod 开始期间
     * @param endPeriod   结束期间
     * @return 总金额
     */
    BigDecimal sumAmountByIndicatorIdAndPeriodRange(@Param("indicatorId") Long indicatorId,
            @Param("startPeriod") String startPeriod, @Param("endPeriod") String endPeriod);

    /**
     * 根据分类统计金额
     *
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 分类统计结果
     */
    List<IndicatorValuesDetailVO> sumAmountByCategory(@Param("indicatorId") Long indicatorId,
            @Param("period") String period);

    /**
     * 按部门统计金额
     *
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 部门聚合结果
     */
    List<IndicatorValuesDetailVO> sumAmountByDept(@Param("indicatorId") Long indicatorId,
            @Param("period") String period);

    /**
     * 批量插入指标数据明细
     *
     * @param list 明细列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<IndicatorValuesDetailEntity> list);

    /**
     * 根据凭证号查询明细
     *
     * @param voucherNo 凭证号
     * @return 明细列表
     */
    List<IndicatorValuesDetailVO> selectByVoucherNo(@Param("voucherNo") String voucherNo);

    /**
     * 根据指标ID、分类和期间查询金额
     *
     * @param indicatorId 指标ID
     * @param category    分类
     * @param period      期间
     * @return 金额
     */
    BigDecimal sumAmountByIndicatorIdAndCategoryAndPeriod(@Param("indicatorId") Long indicatorId,
            @Param("category") String category,
            @Param("period") String period);

    /**
     * 根据指标ID和分类查询指定期间范围内的金额
     *
     * @param indicatorId 指标ID
     * @param category    分类
     * @param startPeriod 开始期间
     * @param endPeriod   结束期间
     * @return 金额
     */
    BigDecimal sumAmountByIndicatorIdAndCategoryAndPeriodRange(@Param("indicatorId") Long indicatorId,
            @Param("category") String category,
            @Param("startPeriod") String startPeriod,
            @Param("endPeriod") String endPeriod);

    /**
     * 根据指标ID和分类查询指定年份的所有月份数据
     *
     * @param indicatorId 指标ID
     * @param category    分类
     * @param year        年份
     * @return 月份数据列表
     */
    List<Map<String, Object>> selectMonthlyDataByIndicatorIdAndCategoryAndYear(@Param("indicatorId") Long indicatorId,
            @Param("category") String category,
            @Param("year") Integer year);

}
