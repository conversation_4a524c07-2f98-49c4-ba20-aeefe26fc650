/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesDetailEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO;
import org.springblade.modules.yjzb.pojo.dto.IndicatorValuesDetailDTO;
import org.springblade.modules.yjzb.excel.IndicatorValuesDetailExcel;
import org.springblade.modules.yjzb.wrapper.IndicatorValuesDetailWrapper;
import org.springblade.modules.yjzb.service.IIndicatorValuesDetailService;
import org.springblade.modules.yjzb.service.IIndicatorTypesService;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;

import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Arrays;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.web.multipart.MultipartFile;

/**
 * 指标数据明细 控制器
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicatorValuesDetail")
@Tag(name = "指标数据明细", description = "指标数据明细接口")
public class IndicatorValuesDetailController extends BladeController {

    private final IIndicatorValuesDetailService indicatorValuesDetailService;
    private final IIndicatorTypesService indicatorTypesService;

    /**
     * 指标数据明细 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicatorValuesDetail")
    public R<IndicatorValuesDetailVO> detail(IndicatorValuesDetailEntity indicatorValuesDetail) {
        IndicatorValuesDetailEntity detail = indicatorValuesDetailService
                .getOne(Condition.getQueryWrapper(indicatorValuesDetail));
        return R.data(IndicatorValuesDetailWrapper.build(indicatorTypesService).entityVO(detail));
    }

    /**
     * 指标数据明细 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicatorValuesDetail")
    public R<IPage<IndicatorValuesDetailVO>> list(
            @Parameter(hidden = true) @RequestParam Map<String, Object> indicatorValuesDetail,
            Query query) {
        IPage<IndicatorValuesDetailVO> pages = indicatorValuesDetailService.selectIndicatorValuesDetailPage(
                Condition.getPage(query), BeanUtil.copy(indicatorValuesDetail, IndicatorValuesDetailEntity.class));
        return R.data(pages);
    }

    /**
     * 指标数据明细 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "新增", description = "传入indicatorValuesDetail")
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    public R save(@Valid @RequestBody IndicatorValuesDetailDTO indicatorValuesDetailDTO) {
        return R.status(indicatorValuesDetailService.saveIndicatorValuesDetail(indicatorValuesDetailDTO));
    }

    /**
     * 指标数据明细 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "修改", description = "传入indicatorValuesDetail")
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    public R update(@Valid @RequestBody IndicatorValuesDetailDTO indicatorValuesDetailDTO) {
        return R.status(indicatorValuesDetailService.updateIndicatorValuesDetail(indicatorValuesDetailDTO));
    }

    /**
     * 指标数据明细 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "删除", description = "传入ids")
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    public R remove(@RequestParam String ids) {
        return R.status(indicatorValuesDetailService.deleteIndicatorValuesDetailBatch(Func.toLongList(ids)));
    }

    /**
     * 指标数据明细 批量删除
     */
    @PostMapping("/remove-batch")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "批量删除", description = "传入ids")
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    public R removeBatch(@RequestBody List<Long> ids) {
        return R.status(indicatorValuesDetailService.deleteIndicatorValuesDetailBatch(ids));
    }

    /**
     * 根据指标ID和期间查询明细列表
     */
    @GetMapping("/by-indicator-period")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "根据指标ID和期间查询明细", description = "传入indicatorId和period")
    public R<List<IndicatorValuesDetailVO>> getByIndicatorAndPeriod(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period) {

        System.out.println("=== 获取明细数据请求 ===");
        System.out.println("indicatorId: " + indicatorId);
        System.out.println("period: " + period);

        List<IndicatorValuesDetailVO> list = indicatorValuesDetailService.selectByIndicatorIdAndPeriod(indicatorId,
                period);

        System.out.println("查询结果数量: " + (list != null ? list.size() : 0));
        if (list != null && !list.isEmpty()) {
            System.out.println("第一条数据: " + list.get(0));
        }

        return R.data(list);
    }

    /**
     * 根据指标ID和期间区间查询明细列表
     */
    @GetMapping("/by-indicator-period-range")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "根据指标ID和期间区间查询明细", description = "传入indicatorId、startPeriod和endPeriod")
    public R<List<IndicatorValuesDetailVO>> getByIndicatorAndPeriodRange(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "开始期间") @RequestParam String startPeriod,
            @Parameter(description = "结束期间") @RequestParam String endPeriod) {
        List<IndicatorValuesDetailVO> list = indicatorValuesDetailService.selectByIndicatorIdAndPeriodRange(indicatorId,
                startPeriod, endPeriod);
        return R.data(list);
    }

    /**
     * 根据指标ID和期间计算总金额
     */
    @GetMapping("/sum-amount")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "根据指标ID和期间计算总金额", description = "传入indicatorId和period")
    public R<BigDecimal> getSumAmount(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period) {
        BigDecimal sumAmount = indicatorValuesDetailService.sumAmountByIndicatorIdAndPeriod(indicatorId, period);
        return R.data(sumAmount != null ? sumAmount : BigDecimal.ZERO);
    }

    /**
     * 根据指标ID和期间区间计算总金额
     */
    @GetMapping("/sum-amount-range")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "根据指标ID和期间区间计算总金额", description = "传入indicatorId、startPeriod和endPeriod")
    public R<BigDecimal> getSumAmountRange(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "开始期间") @RequestParam String startPeriod,
            @Parameter(description = "结束期间") @RequestParam String endPeriod) {
        BigDecimal sumAmount = indicatorValuesDetailService.sumAmountByIndicatorIdAndPeriodRange(indicatorId,
                startPeriod, endPeriod);
        return R.data(sumAmount != null ? sumAmount : BigDecimal.ZERO);
    }

    /**
     * 根据分类统计金额
     */
    @GetMapping("/sum-by-category")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "根据分类统计金额", description = "传入indicatorId和period")
    public R<List<IndicatorValuesDetailVO>> getSumByCategory(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period) {
        List<IndicatorValuesDetailVO> list = indicatorValuesDetailService.sumAmountByCategory(indicatorId, period);
        return R.data(list);
    }

    /**
     * 按部门统计金额
     */
    @GetMapping("/sum-by-dept")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "按部门统计金额", description = "传入indicatorId和period")
    public R<List<IndicatorValuesDetailVO>> getSumByDept(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period) {
        List<IndicatorValuesDetailVO> list = indicatorValuesDetailService.sumAmountByDept(indicatorId, period);
        return R.data(list);
    }

    /**
     * 根据凭证号查询明细
     */
    @GetMapping("/by-voucher-no")
    @ApiOperationSupport(order = 12)
    @Operation(summary = "根据凭证号查询明细", description = "传入voucherNo")
    public R<List<IndicatorValuesDetailVO>> getByVoucherNo(
            @Parameter(description = "凭证号") @RequestParam String voucherNo) {
        List<IndicatorValuesDetailVO> list = indicatorValuesDetailService.selectByVoucherNo(voucherNo);
        return R.data(list);
    }

    /**
     * 批量导入指标数据明细
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 13)
    @Operation(summary = "批量导入", description = "传入Excel文件")
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    public R importData(@RequestParam("file") MultipartFile file) {
        try {
            List<IndicatorValuesDetailExcel> list = ExcelUtil.read(file, IndicatorValuesDetailExcel.class);
            List<IndicatorValuesDetailEntity> entities = list.stream()
                    .map(excel -> {
                        IndicatorValuesDetailEntity entity = new IndicatorValuesDetailEntity();
                        entity.setIndicatorId(excel.getIndicatorId());
                        entity.setPeriod(excel.getPeriod());
                        entity.setVoucherNo(excel.getVoucherNo());
                        entity.setDataSummary(excel.getDataSummary());
                        entity.setAmount(excel.getAmount());
                        entity.setCategory(excel.getCategory());
                        return entity;
                    })
                    .collect(Collectors.toList());

            boolean success = indicatorValuesDetailService.batchInsert(entities);
            return R.status(success);
        } catch (Exception e) {
            return R.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 费用明细数据预览
     */
    @PostMapping("/preview-fee-detail")
    @ApiOperationSupport(order = 15)
    @Operation(summary = "费用明细数据预览", description = "传入Excel文件进行预览")
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    public R<List<Map<String, Object>>> previewFeeDetailData(
            @Parameter(description = "Excel文件") @RequestParam("file") MultipartFile file) {

        try {
            List<Map<String, Object>> previewData = indicatorValuesDetailService.previewFeeDetailFile(file);
            return R.data(previewData, "预览成功");
        } catch (Exception e) {
            return R.fail("预览失败：" + e.getMessage());
        }
    }

    /**
     * 费用明细数据导入
     */
    @PostMapping("/import-fee-detail")
    @ApiOperationSupport(order = 16)
    @Operation(summary = "费用明细数据导入", description = "传入指标ID和Excel文件")
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    public R<Map<String, Object>> importFeeDetailData(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "Excel文件") @RequestParam("file") MultipartFile file) {

        Map<String, Object> result = new HashMap<>();

        try {
            // 调用服务层处理文件导入
            Map<String, Object> importResult = indicatorValuesDetailService.importFeeDetailFile(indicatorId, file);

            return R.data(importResult, (Boolean) importResult.get("success") ? "导入成功" : "导入失败");

        } catch (Exception e) {
            result.put("success", false);
            result.put("totalCount", 0);
            result.put("successCount", 0);
            result.put("failCount", 1);
            result.put("errors", Arrays.asList("导入异常: " + e.getMessage()));
            return R.fail("导入失败").data(result);
        }
    }

    /**
     * 导出指标数据明细
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 14)
    @Operation(summary = "导出", description = "导出Excel")
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    public void export(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorValuesDetail,
            HttpServletResponse response) {
        List<IndicatorValuesDetailEntity> list = indicatorValuesDetailService
                .list(Condition.getQueryWrapper(indicatorValuesDetail, IndicatorValuesDetailEntity.class));
        List<IndicatorValuesDetailVO> voList = IndicatorValuesDetailWrapper.build(indicatorTypesService)
                .entityVOList(list);
        List<IndicatorValuesDetailExcel> excelList = voList.stream()
                .map(vo -> {
                    IndicatorValuesDetailExcel excel = new IndicatorValuesDetailExcel();
                    excel.setIndicatorId(vo.getIndicatorId());
                    excel.setPeriod(vo.getPeriod());
                    excel.setVoucherNo(vo.getVoucherNo());
                    excel.setDataSummary(vo.getDataSummary());
                    excel.setAmount(vo.getAmount());
                    excel.setCategory(vo.getCategory());
                    excel.setCreateTime(vo.getCreateTime());
                    excel.setUpdateTime(vo.getUpdateTime());
                    return excel;
                })
                .collect(Collectors.toList());
        ExcelUtil.export(response, "指标数据明细", "指标数据明细", excelList, IndicatorValuesDetailExcel.class);
    }

}
