/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.vo.BusinessKnowledgeVO;
import org.springblade.modules.yjzb.excel.BusinessKnowledgeExcel;
import org.springblade.modules.yjzb.wrapper.BusinessKnowledgeWrapper;
import org.springblade.modules.yjzb.service.IBusinessKnowledgeService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 业务知识 控制器
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/businessKnowledge")
@Tag(name = "业务知识", description = "业务知识接口")
public class BusinessKnowledgeController extends BladeController {

	private final IBusinessKnowledgeService businessKnowledgeService;

	/**
	 * 业务知识 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入businessKnowledge")
	public R<BusinessKnowledgeVO> detail(BusinessKnowledgeEntity businessKnowledge) {
		BusinessKnowledgeEntity detail = businessKnowledgeService.getOne(Condition.getQueryWrapper(businessKnowledge));
		return R.data(BusinessKnowledgeWrapper.build().entityVO(detail));
	}
	/**
	 * 业务知识 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入businessKnowledge")
	public R<IPage<BusinessKnowledgeVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> businessKnowledge, Query query) {
		IPage<BusinessKnowledgeEntity> pages = businessKnowledgeService.page(Condition.getPage(query), Condition.getQueryWrapper(businessKnowledge, BusinessKnowledgeEntity.class));
		return R.data(BusinessKnowledgeWrapper.build().pageVO(pages));
	}

	/**
	 * 业务知识 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入businessKnowledge")
	public R<IPage<BusinessKnowledgeVO>> page(BusinessKnowledgeVO businessKnowledge, Query query) {
		IPage<BusinessKnowledgeVO> pages = businessKnowledgeService.selectBusinessKnowledgePage(Condition.getPage(query), businessKnowledge);
		return R.data(pages);
	}

	/**
	 * 业务知识 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入businessKnowledge")
	public R save(@Valid @RequestBody BusinessKnowledgeEntity businessKnowledge) {
		return R.status(businessKnowledgeService.save(businessKnowledge));
	}

	/**
	 * 业务知识 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入businessKnowledge")
	public R update(@Valid @RequestBody BusinessKnowledgeEntity businessKnowledge) {
		return R.status(businessKnowledgeService.updateById(businessKnowledge));
	}

	/**
	 * 业务知识 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入businessKnowledge")
	public R submit(@Valid @RequestBody BusinessKnowledgeEntity businessKnowledge) {
		return R.status(businessKnowledgeService.saveOrUpdate(businessKnowledge));
	}

	/**
	 * 业务知识 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(businessKnowledgeService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-businessKnowledge")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入businessKnowledge")
	public void exportBusinessKnowledge(@Parameter(hidden = true) @RequestParam Map<String, Object> businessKnowledge, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<BusinessKnowledgeEntity> queryWrapper = Condition.getQueryWrapper(businessKnowledge, BusinessKnowledgeEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(BusinessKnowledge::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(BusinessKnowledgeEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<BusinessKnowledgeExcel> list = businessKnowledgeService.exportBusinessKnowledge(queryWrapper);
		ExcelUtil.export(response, "业务知识数据" + DateUtil.time(), "业务知识数据表", list, BusinessKnowledgeExcel.class);
	}

}
