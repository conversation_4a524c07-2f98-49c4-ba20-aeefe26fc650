/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 */
package org.springblade.modules.yjzb.service.impl;

import org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity;
import org.springblade.modules.yjzb.mapper.IndicatorAiAnalysisMapper;
import org.springblade.modules.yjzb.service.IIndicatorAiAnalysisService;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.pojo.entity.IndicatorEntity;
import org.springblade.modules.yjzb.service.IIndicatorValuesService;
import org.springblade.modules.yjzb.service.IIndicatorService;
import org.springblade.modules.yjzb.service.IIndicatorAnnualBudgetService;
import org.springblade.modules.yjzb.service.IBusinessKnowledgeService;
import org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.vo.BusinessKnowledgeVO;
import org.springblade.modules.yjzb.wrapper.BusinessKnowledgeWrapper;
import org.springblade.modules.yjzb.service.cache.BusinessKnowledgeRuleCache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springblade.core.mp.base.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.yjzb.pojo.entity.IndicatorTypesEntity;
import org.springblade.modules.yjzb.service.IIndicatorTypesService;
import org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 指标AI解读分析 服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorAiAnalysisServiceImpl
        extends BaseServiceImpl<IndicatorAiAnalysisMapper, IndicatorAiAnalysisEntity>
        implements IIndicatorAiAnalysisService {

    private final IIndicatorValuesService indicatorValuesService;
    private final IIndicatorService indicatorService;
    private final IIndicatorAnnualBudgetService indicatorAnnualBudgetService;
    private final org.springblade.modules.yjzb.service.IIndicatorForecastService indicatorForecastService;
    private final IIndicatorTypesService indicatorTypesService;
    private final IBusinessKnowledgeService businessKnowledgeService;
    private final BusinessKnowledgeRuleCache businessKnowledgeRuleCache;
    @Value("${dify.api.agentkey.totalanalysisagent:}")
    private String totalAnalysisApiKey;
    private final IDifyService difyService;
    private final ObjectMapper objectMapper;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    // 自定义提示词透传用（避免大范围重构，使用 ThreadLocal 在同线程调用链传递）
    private final ThreadLocal<String> promptOverrideTL = new ThreadLocal<>();

    @Value("${dify.api.agentkey.analysisagent:}")
    private String difyApiKey;

    @Override
    public Long restartAnalysisForIndicatorValue(Long indicatorValueId) {
        IndicatorValuesEntity indicatorValue = indicatorValuesService.getById(indicatorValueId);
        if (indicatorValue == null) {
            log.error("未找到指标数据，id={}", indicatorValueId);
            return null;
        }

        // 1) 组装输入参数（按新规范）
        Map<String, Object> inputs = new HashMap<>();
        String period = indicatorValue.getPeriod();
        IndicatorEntity indicator = null;
        try {
            indicator = indicatorService.getById(indicatorValue.getIndicatorId());
        } catch (Exception ignore) {
        }

        // indicator_name 指标名
        inputs.put("indicator_name", indicator == null ? null : indicator.getName());

        // background 背景
        try {
            int curYear;
            if (period != null && period.matches("\\\\d{4}-\\\\d{2}")) {
                curYear = Integer.parseInt(period.substring(0, 4));
            } else {
                curYear = java.time.LocalDate.now().getYear();
            }
            int startYear = curYear - 3; // 前三年起始年
            int endYear = curYear - 1; // 去年
            String indicatorName = indicator == null ? "" : (indicator.getName() == null ? "" : indicator.getName());
            String bg = String.format("以下是自 %d 年 1 月至 %d 年 12 月的“%s”历史数据。数据包含年月和当月总金额。",
                    startYear, endYear, indicatorName);
            inputs.put("background", bg);
        } catch (Exception e) {
            inputs.put("background", "");
        }

        // business_rule 业务规则：动态读取当前指标的“预测知识”（knowledgeType=1）内容，按Markdown列表拼接
        try {
            String businessRule = businessKnowledgeRuleCache != null
                    ? businessKnowledgeRuleCache.get(indicatorValue.getIndicatorId(), 1)
                    : null;
            if (businessRule == null || businessRule.isBlank()) {
                businessRule = buildBusinessRuleForIndicator(indicatorValue.getIndicatorId());
            }
            inputs.put("business_rule", businessRule == null ? "" : businessRule);
        } catch (Exception e) {
            inputs.put("business_rule", "");
        }

        // history_data 历史数据（最近三年CSV，year_month,monthly_total_amount）
        try {
            String csv = buildThreeYearsMonthlyCsv(indicatorValue.getIndicatorId(), period);
            inputs.put("history_data", csv);
        } catch (Exception e) {
            log.warn("构建两年CSV失败, indicatorId={}, period={}, err= {}", indicatorValue.getIndicatorId(), period,
                    e.getMessage());
        }

        // this_year_data 今年数据（当年1..12月，year_month,monthly_total_amount）
        try {
            String yearCsv = buildCurrentYearMonthlyCsv(indicatorValue.getIndicatorId(), period);
            inputs.put("this_year_data", yearCsv);
        } catch (Exception e) {
            log.warn("构建今年CSV失败, indicatorId={}, period={}, err= {}", indicatorValue.getIndicatorId(), period,
                    e.getMessage());
        }

        // 2) 启动Dify工作流（streaming，只取第一个事件拿到workflow_run_id）
        // 读取API-KEY：从配置或数据库。这里沿用DifyServiceImpl内部已读的配置；接口需要显式传入。
        // 为复用现有签名，这里从环境变量或系统属性读取；若为空将回退到application配置。
        String apiKey = (difyApiKey == null || difyApiKey.isBlank())
                ? System.getProperty("dify.api.key", System.getenv("DIFY_API_KEY"))
                : difyApiKey;
        // 若设置了自定义提示词，则透传到 inputs.systemPrompt
        String _override = promptOverrideTL.get();
        if (_override != null && !_override.isBlank()) {
            inputs.put("systemPrompt", _override);
        }

        String workflowRunId = difyService.startWorkflowStreaming(inputs, "yjzb", apiKey);
        log.info("启动AI分析：indicatorValueId={}, indicatorId={}, period={}, workflowRunId={}", indicatorValueId,
                indicatorValue.getIndicatorId(), indicatorValue.getPeriod(), workflowRunId);
        if (workflowRunId == null) {
            log.error("启动Dify工作流失败，indicatorValueId={}，请检查 Dify 配置与权限", indicatorValueId);
            return null;
        }

        // 3) 写入AI解读分析表（RUNNING）
        IndicatorAiAnalysisEntity ai = new IndicatorAiAnalysisEntity();
        ai.setIndicatorId(indicatorValue.getIndicatorId());
        ai.setPeriod(indicatorValue.getPeriod());
        try {
            ai.setInputParams(objectMapper.writeValueAsString(inputs));
        } catch (Exception e) {
            ai.setInputParams(String.valueOf(inputs));
        }
        ai.setExecuteTime(new Date());
        ai.setExecuteStatus("RUNNING");
        ai.setWorkflowRunId(workflowRunId);
        save(ai);

        Long aiId = ai.getId();

        // 4) 启动后台轮询任务：每3分钟查询一次执行结果
        log.info("[AI分析-轮询] 启动轮询任务: aiId={}, workflowRunId={}, period={}, indicatorId={}, 间隔=3分钟",
                aiId, workflowRunId, indicatorValue.getPeriod(), indicatorValue.getIndicatorId());
        final String finalApiKey = apiKey;
        final long aiRecordIdFinal = aiId;
        final String workflowRunIdFinal = workflowRunId;
        final AtomicReference<ScheduledFuture<?>> futureRef = new AtomicReference<>();
        ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
            try {
                log.debug("[AI分析-轮询] tick: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                IndicatorAiAnalysisEntity current = getById(aiRecordIdFinal);
                if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                    // 已非运行状态，结束轮询
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[AI分析-轮询] 停止轮询: aiId={}, 当前状态={}", aiRecordIdFinal,
                            current == null ? "null" : current.getExecuteStatus());
                    return;
                }
                String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                if (detail == null || detail.isBlank()) {
                    log.warn("[AI分析-轮询] 未获取到工作流详情: aiId={}, workflowRunId={}", aiRecordIdFinal,
                            workflowRunIdFinal);
                    return;
                }
                // 从返回内容中提取 status（兼容 data 为 JSON 字符串的情况）
                String status = extractStatus(detail);
                String lower = status == null ? null : status.toLowerCase();
                log.info("[AI分析-轮询] 查询结果: aiId={}, workflowRunId={}, status={}", aiRecordIdFinal,
                        workflowRunIdFinal, lower);
                if ("succeeded".equals(lower) || "completed".equals(lower)) {
                    // 仅存 outputs.text，前端直接展示；避免大量转义与嵌套JSON
                    String outputText = extractOutputText(detail);
                    // 清理 <think>..</think>，仅保留正文
                    String cleaned = stripThinkBlock(outputText);
                    current.setExecuteStatus("COMPLETED");
                    // 兼容：输出可能是 ```json ... ``` 或直接 JSON，这里统一提取为纯 JSON
                    String candidate = cleaned != null ? cleaned : (outputText != null ? outputText : detail);
                    String normalized = normalizeJsonPayload(candidate);
                    current.setResult(normalized);
                    updateById(current);

                    // 解析正文中的预测JSON数组，并将"预测月份->预测金额"写回年度预测表
                    try {
                        if (cleaned != null && !cleaned.isBlank()) {
                            upsertForecastsFromAiText(current.getIndicatorId(), cleaned);
                        }
                    } catch (Exception ex) {
                        log.warn("[AI分析] 解析并写回预测结果失败, indicatorId={}, err={}", current.getIndicatorId(),
                                ex.getMessage());
                    }
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[AI分析-轮询] 已完成: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                } else if ("failed".equals(lower) || "error".equals(lower)) {
                    current.setExecuteStatus("FAILED");
                    current.setResult(detail);
                    updateById(current);
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[AI分析-轮询] 已失败: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                }

            } catch (Exception ex) {
                log.error("轮询Dify工作流结果异常, workflowRunId={}", workflowRunIdFinal, ex);
            }
        }, 0, 10, TimeUnit.SECONDS);
        futureRef.set(scheduled);

        return aiId;
    }

    /**
     * 根据指标ID构建业务规则：查询 yjzb_business_knowledge 中所有 knowledgeType=1 的记录，
     * 过滤出 applicableIndicators 包含该指标ID的条目，将 knowledgeContent 以 Markdown 列表拼接。
     */
    private String buildBusinessRuleForIndicator(Long indicatorId) {
        try {
            if (indicatorId == null)
                return "";
            // 先按类型筛选，再在内存中解析 applicableIndicators 字段进行二次过滤，避免不同数据库 JSON 查询差异
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<BusinessKnowledgeEntity> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            queryWrapper.eq("knowledge_type", 1);
            java.util.List<BusinessKnowledgeEntity> list = businessKnowledgeService.list(queryWrapper);
            if (list == null || list.isEmpty())
                return "";
            String idStr = String.valueOf(indicatorId);
            StringBuilder sb = new StringBuilder();
            for (BusinessKnowledgeEntity bk : list) {
                if (bk == null)
                    continue;
                String apps = bk.getApplicableIndicators();
                if (!applicableToIndicator(apps, idStr))
                    continue;
                String content = bk.getKnowledgeContent();
                if (content == null)
                    continue;
                String line = content.trim();
                if (line.isEmpty())
                    continue;
                // 去掉换行，避免列表项过长时格式断裂，可按需保留
                line = line.replace('\r', ' ').replace('\n', ' ');
                sb.append("- ").append(line).append('\n');
            }
            return sb.toString().trim();
        } catch (Exception e) {
            return "";
        }
    }

    private boolean applicableToIndicator(String applicableIndicators, String indicatorId) {
        if (applicableIndicators == null || applicableIndicators.isBlank())
            return false;
        String s = applicableIndicators.trim();
        try {
            if (s.startsWith("[") && s.endsWith("]")) {
                com.fasterxml.jackson.core.type.TypeReference<java.util.List<Object>> type = new com.fasterxml.jackson.core.type.TypeReference<>() {
                };
                java.util.List<Object> arr = objectMapper.readValue(s, type);
                if (arr != null) {
                    for (Object o : arr) {
                        if (indicatorId.equals(String.valueOf(o)))
                            return true;
                    }
                }
                return false;
            }
        } catch (Exception ignore) {
            // 回退走逗号分隔
        }
        // 逗号分隔或其它格式：简单包含判断（去除空白）
        String[] parts = s.split(",");
        for (String p : parts) {
            if (indicatorId.equals(p.trim()))
                return true;
        }
        // 最后回退到模糊包含，降低误杀风险
        return ("," + s + ",").contains("," + indicatorId + ",");
    }

    private java.math.BigDecimal getActualValue(Long indicatorId, String period) {
        try {
            org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity q = new org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity();
            q.setIndicatorId(indicatorId);
            q.setPeriod(period);
            org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity e = indicatorValuesService
                    .getOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>(q));
            return e == null ? null : e.getValue();
        } catch (Exception ignore) {
            return null;
        }
    }

    /**
     * 提取工作流状态字段，兼容以下返回结构：
     * 1) { status: "succeeded" }
     * 2) { data: { status: "succeeded" } }
     * 3) { data: "{\"status\":\"succeeded\", ... }" }
     */
    private String extractStatus(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;
            // 直接在根查找
            if (node.has("status")) {
                return node.get("status").asText();
            }
            // 在 data 节点查找
            if (node.has("data")) {
                com.fasterxml.jackson.databind.JsonNode data = node.get("data");
                if (data.isObject() && data.has("status")) {
                    return data.get("status").asText();
                }
                if (data.isTextual()) {
                    // data 是一个 JSON 字符串，再次反序列化
                    String dataText = data.asText();
                    com.fasterxml.jackson.databind.JsonNode dataObj = objectMapper.readTree(dataText);
                    if (dataObj != null && dataObj.has("status")) {
                        return dataObj.get("status").asText();
                    }
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 提取 outputs.text 内容；若 outputs 为字符串JSON，会先反序列化再取 text
     */
    private String extractOutputText(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;
            com.fasterxml.jackson.databind.JsonNode outputs = node.get("outputs");
            if (outputs == null)
                return null;
            if (outputs.isObject()) {
                com.fasterxml.jackson.databind.JsonNode text = outputs.get("text");
                return text != null ? text.asText() : outputs.toString();
            }
            if (outputs.isTextual()) {
                String outputsText = outputs.asText();
                com.fasterxml.jackson.databind.JsonNode outObj = objectMapper.readTree(outputsText);
                if (outObj != null && outObj.has("text")) {
                    return outObj.get("text").asText();
                }
                return outputsText;
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 去除 <think>..</think> 内容，仅保留正文
     */
    private String stripThinkBlock(String text) {
        if (text == null)
            return null;
        try {
            // (?is) -> DOTALL + CASE_INSENSITIVE，在java中使用 Pattern 标志更安全；这里简化为两次替换
            return text.replaceAll("(?is)<think>.*?</think>", "").trim();
        } catch (Exception ignore) {
            return text;
        }
    }

    /**
     * 统一将可能带有 Markdown 代码块(```json ... ```) 或直接 JSON 的文本，提取为纯净 JSON 字符串。
     * 处理流程：
     * 1) 若整体被 ``` 包裹，优先提取第一对围栏中的内容（跳过语言标识行）
     * 2) 若未包裹但以 { 或 [ 开头，直接尝试解析
     * 3) 尝试从原始文本中匹配 ```json ... ``` 的片段
     * 成功解析后用 objectMapper 规范化输出（去格式化），失败则返回原文本
     */
    private String normalizeJsonPayload(String text) {
        if (text == null)
            return null;
        String original = text;
        String s = text.trim();
        try {
            if (s.startsWith("```")) {
                int firstFence = s.indexOf("```");
                int secondFence = s.indexOf("```", firstFence + 3);
                if (secondFence > firstFence) {
                    int newline = s.indexOf('\n', firstFence + 3);
                    if (newline > 0 && newline < secondFence) {
                        s = s.substring(newline + 1, secondFence);
                    } else {
                        s = s.substring(firstFence + 3, secondFence);
                    }
                    s = s.trim();
                }
            }
            // 尝试解析（包含直接JSON或上面抽取后的内容）
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(s);
            return objectMapper.writeValueAsString(node);
        } catch (Exception e) {
            // 回退：从原始文本中匹配 ```json ... ``` 片段
            try {
                java.util.regex.Pattern p = java.util.regex.Pattern.compile("(?is)```\\s*json\\s*(.*?)\\s*```");
                java.util.regex.Matcher m = p.matcher(original);
                if (m.find()) {
                    String inner = m.group(1).trim();
                    com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(inner);
                    return objectMapper.writeValueAsString(node);
                }
            } catch (Exception ignore) {
            }
            // 最后尝试：若看起来像JSON起始，再次解析
            try {
                if (s.startsWith("{") || s.startsWith("[")) {
                    com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(s);
                    return objectMapper.writeValueAsString(node);
                }
            } catch (Exception ignore2) {
            }
            return original; // 保底返回原文本
        }
    }

    /**
     * 从AI返回正文中提取预测数组，并按月份写回到 yjzb_indicator_forecast 表。
     * 仅更新返回中出现的月份；金额四舍五入保留两位小数。
     */
    private void upsertForecastsFromAiText(Long indicatorId, String bodyText) {
        if (indicatorId == null || bodyText == null || bodyText.isBlank())
            return;
        java.util.List<java.util.Map<String, Object>> items = parseForecastItems(bodyText);
        if (items == null || items.isEmpty())
            return;

        // 将条目按年份分组，分别upsert
        java.util.Map<Integer, java.util.List<java.util.Map<String, Object>>> yearGroups = new java.util.HashMap<>();
        for (var it : items) {
            String ym = asString(it.get("预测月份"));
            java.util.Map<String, Integer> ymd = parseChineseYearMonth(ym);
            if (ymd == null)
                continue;
            Integer y = ymd.get("year");
            yearGroups.computeIfAbsent(y, k -> new java.util.ArrayList<>()).add(it);
        }

        for (var entry : yearGroups.entrySet()) {
            Integer year = entry.getKey();
            java.util.List<java.util.Map<String, Object>> list = entry.getValue();
            if (year == null || list == null || list.isEmpty())
                continue;

            IndicatorForecastEntity forecast = indicatorForecastService.findByIndicatorAndYear(indicatorId, year);
            if (forecast == null) {
                forecast = new IndicatorForecastEntity();
                forecast.setIndicatorId(indicatorId);
                forecast.setYear(year);
            }

            for (var it : list) {
                String ym = asString(it.get("预测月份"));
                java.util.Map<String, Integer> ymd = parseChineseYearMonth(ym);
                if (ymd == null)
                    continue;
                int month = ymd.get("month");
                BigDecimal amount = parseBigDecimal(it.get("预测金额"));
                if (amount == null)
                    continue;
                amount = amount.setScale(2, java.math.RoundingMode.HALF_UP);
                setForecastMonthValue(forecast, month, amount);
            }

            // upsert
            IndicatorForecastEntity exist = indicatorForecastService.findByIndicatorAndYear(indicatorId, year);
            if (exist == null || exist.getId() == null) {
                indicatorForecastService.save(forecast);
            } else {
                forecast.setId(exist.getId());
                indicatorForecastService.updateById(forecast);
            }
        }
    }

    private String asString(Object o) {
        return o == null ? null : String.valueOf(o);
    }

    private BigDecimal parseBigDecimal(Object o) {
        if (o == null)
            return null;
        try {
            String s = String.valueOf(o).replace(",", "").trim();
            if (s.isEmpty())
                return null;
            return new BigDecimal(s);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从正文中提取预测数组。正文可能包含除JSON外的文字，这里尝试定位第一个以 '[' 开头、以 ']' 结束的JSON数组片段。
     */
    private java.util.List<java.util.Map<String, Object>> parseForecastItems(String text) {
        try {
            // 先尝试整体解析
            com.fasterxml.jackson.core.type.TypeReference<java.util.List<java.util.Map<String, Object>>> type = new com.fasterxml.jackson.core.type.TypeReference<>() {
            };
            if (text.trim().startsWith("[")) {
                return objectMapper.readValue(text, type);
            }
        } catch (Exception ignore) {
        }
        // 提取数组片段
        try {
            int start = text.indexOf('[');
            int end = text.lastIndexOf(']');
            if (start >= 0 && end > start) {
                String arr = text.substring(start, end + 1);
                com.fasterxml.jackson.core.type.TypeReference<java.util.List<java.util.Map<String, Object>>> type = new com.fasterxml.jackson.core.type.TypeReference<>() {
                };
                return objectMapper.readValue(arr, type);
            }
        } catch (Exception ignore) {
        }
        return java.util.Collections.emptyList();
    }

    /**
     * 解析中文年月，例如："2025年5月"、"2025年05月" 或 "2025-05"，返回 {year, month}
     */
    private java.util.Map<String, Integer> parseChineseYearMonth(String ym) {
        if (ym == null || ym.isBlank())
            return null;
        ym = ym.trim();
        try {
            if (ym.matches("\\d{4}年\\d{1,2}月")) {
                int y = Integer.parseInt(ym.substring(0, 4));
                int m = Integer.parseInt(ym.substring(5, ym.length() - 1));
                java.util.Map<String, Integer> map = new java.util.HashMap<>();
                map.put("year", y);
                map.put("month", m);
                return map;
            }
            if (ym.matches("\\d{4}-\\d{2}")) {
                int y = Integer.parseInt(ym.substring(0, 4));
                int m = Integer.parseInt(ym.substring(5, 7));
                java.util.Map<String, Integer> map = new java.util.HashMap<>();
                map.put("year", y);
                map.put("month", m);
                return map;
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    private void setForecastMonthValue(IndicatorForecastEntity forecast, int month, BigDecimal value) {
        switch (month) {
            case 2 -> forecast.setForecastM02(value);
            case 3 -> forecast.setForecastM03(value);
            case 4 -> forecast.setForecastM04(value);
            case 5 -> forecast.setForecastM05(value);
            case 6 -> forecast.setForecastM06(value);
            case 7 -> forecast.setForecastM07(value);
            case 8 -> forecast.setForecastM08(value);
            case 9 -> forecast.setForecastM09(value);
            case 10 -> forecast.setForecastM10(value);
            case 11 -> forecast.setForecastM11(value);
            case 12 -> forecast.setForecastM12(value);
            default -> {
                // 不处理 1 月或非法月份（需求为2-12月预测）
            }
        }
    }

    @Override
    public void saveMonthlyAiInterpretation(Long indicatorId, String period, String resultText) {
        if (indicatorId == null || period == null || resultText == null)
            return;
        IndicatorAiAnalysisEntity ai = new IndicatorAiAnalysisEntity();
        ai.setIndicatorId(indicatorId);
        ai.setPeriod(period);
        ai.setExecuteTime(new java.util.Date());
        ai.setExecuteStatus("COMPLETED");
        // 只存正文
        ai.setResult(stripThinkBlock(resultText));
        this.save(ai);
    }

    // 清理未使用的旧CSV工具方法

    /**
     * 构建最近36个月历史数据CSV：year_month,monthly_total_amount
     */
    private String buildThreeYearsMonthlyCsv(Long indicatorId, String currentPeriod) {
        StringBuilder sb = new StringBuilder();
        sb.append("year_month,monthly_total_amount\n");
        try {
            int curYear;
            if (currentPeriod != null && currentPeriod.matches("\\d{4}-\\d{2}")) {
                curYear = Integer.parseInt(currentPeriod.substring(0, 4));
            } else {
                java.time.LocalDate now = java.time.LocalDate.now();
                curYear = now.getYear();
            }

            int startYear = curYear - 3; // 前三年起始：Y-3-01
            int endYear = curYear - 1; // 到 Y-1-12

            for (int y = startYear; y <= endYear; y++) {
                for (int m = 1; m <= 12; m++) {
                    String p = String.format("%04d-%02d", y, m);
                    java.math.BigDecimal v = getActualValue(indicatorId, p);
                    sb.append(p).append(',')
                            .append(v == null ? "" : v.setScale(2, java.math.RoundingMode.HALF_UP).toPlainString())
                            .append('\n');
                }
            }
        } catch (Exception e) {
            log.warn("构建三年CSV异常: {}", e.getMessage());
        }
        return sb.toString();
    }

    /**
     * 构建今年(1..12月)该指标的月度数据CSV：year_month,monthly_total_amount
     */
    private String buildCurrentYearMonthlyCsv(Long indicatorId, String currentPeriod) {
        int year;
        int uptoMonth;
        if (currentPeriod != null && currentPeriod.matches("\\d{4}-\\d{2}")) {
            year = Integer.parseInt(currentPeriod.substring(0, 4));
            uptoMonth = Integer.parseInt(currentPeriod.substring(5, 7));
        } else {
            java.time.LocalDate now = java.time.LocalDate.now();
            year = now.getYear();
            uptoMonth = now.getMonthValue();
        }
        StringBuilder sb = new StringBuilder();
        sb.append("year_month,monthly_total_amount\n");
        for (int m = 1; m <= uptoMonth; m++) {
            String p = String.format("%04d-%02d", year, m);
            java.math.BigDecimal v = getActualValue(indicatorId, p);
            sb.append(p).append(',')
                    .append(v == null ? "" : v.setScale(2, java.math.RoundingMode.HALF_UP).toPlainString())
                    .append('\n');
        }
        return sb.toString();
    }

    /**
     * 构建今年(1..12月)该指标的月度数据CSV：year,month,period,indicator_id,value
     */
    // 清理未使用：旧方法已被替代

    @Override
    public IndicatorAiAnalysisEntity getLatestByIndicatorValueId(Long indicatorValueId) {
        IndicatorValuesEntity indicatorValue = indicatorValuesService.getById(indicatorValueId);
        if (indicatorValue == null) {
            return null;
        }
        // 按 indicatorId + period 倒序取最新一条
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<IndicatorAiAnalysisEntity> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        queryWrapper.eq("indicator_id", indicatorValue.getIndicatorId())
                .eq("period", indicatorValue.getPeriod())
                .orderByDesc("create_time")
                .last("limit 1");
        return getOne(queryWrapper);
    }

    @Override
    public Long restartTypeMonthlyAnalysis(Long indicatorTypeId, String period) {
        try {
            // 1) 聚合同类型当月数据
            var records = indicatorValuesService.listByTypeAndPeriod(indicatorTypeId, period);
            StringBuilder csv = new StringBuilder();
            csv.append("indicator_id,indicator_name,period,value\n");
            java.math.BigDecimal monthlyTotal = java.math.BigDecimal.ZERO;
            for (var v : records) {
                String val = v.getValue() == null ? "" : v.getValue().toString();
                if (v.getValue() != null)
                    monthlyTotal = monthlyTotal.add(new java.math.BigDecimal(val));
                csv.append(v.getIndicatorId()).append(',')
                        .append('"')
                        .append((v.getIndicatorName() == null ? "" : v.getIndicatorName()).replace("\"", "\"\""))
                        .append('"').append(',')
                        .append(period).append(',')
                        .append(val)
                        .append('\n');
            }

            // 2) 计算当年累计
            String year = period.substring(0, 4);
            java.math.BigDecimal yearBudget = java.math.BigDecimal.ZERO;
            for (int m = 1; m <= 12; m++) {
                String pm = String.format("%s-%02d", year, m);
                var listM = indicatorValuesService.listByTypeAndPeriod(indicatorTypeId, pm);
                for (var v : listM) {
                    if (v.getValue() != null)
                        yearBudget = yearBudget.add(new java.math.BigDecimal(v.getValue().toString()));
                }
            }

            // 3) 组装inputs并调用Dify(streaming，仅获取workflow_run_id)
            java.util.Map<String, Object> inputs = new java.util.HashMap<>();
            IndicatorTypesEntity indicatorType = indicatorTypesService.getById(indicatorTypeId);
            if (indicatorType != null) {
                inputs.put("indicatorTypeName", indicatorType.getTypeName());
            }
            inputs.put("period", period);
            inputs.put("monthlyTotal", monthlyTotal.toPlainString());
            inputs.put("yearBudget", yearBudget.toPlainString());
            inputs.put("detailData", csv.toString());

            String apiKey = (totalAnalysisApiKey == null || totalAnalysisApiKey.isBlank()) ? null : totalAnalysisApiKey;
            String workflowRunId = difyService.startWorkflowStreaming(inputs, "yjzb", apiKey);
            log.info("启动类型总体AI解读：indicatorTypeId={}, period={}, workflowRunId={}", indicatorTypeId, period,
                    workflowRunId);
            if (workflowRunId == null) {
                log.error("类型总体AI解读启动失败, indicatorTypeId={}, period={}", indicatorTypeId, period);
                return null;
            }

            // 4) 存储RUNNING记录（indicatorId=indicatorTypeId）
            IndicatorAiAnalysisEntity ai = new IndicatorAiAnalysisEntity();
            ai.setIndicatorId(indicatorTypeId);
            ai.setPeriod(period);
            ai.setInputParams("{\"scope\":\"type-monthly\"}");
            ai.setExecuteTime(new java.util.Date());
            ai.setExecuteStatus("RUNNING");
            ai.setWorkflowRunId(workflowRunId);
            save(ai);

            Long aiId = ai.getId();

            // 5) 启动轮询，与单条一致（当前为每10秒）
            final String finalApiKey = apiKey;
            final long aiRecordIdFinal = aiId;
            final String workflowRunIdFinal = workflowRunId;
            final AtomicReference<ScheduledFuture<?>> futureRef = new AtomicReference<>();
            ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
                try {
                    log.debug("[AI类型-轮询] tick: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    IndicatorAiAnalysisEntity current = getById(aiRecordIdFinal);
                    if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI类型-轮询] 停止轮询: aiId={}, 当前状态={}", aiRecordIdFinal,
                                current == null ? "null" : current.getExecuteStatus());
                        return;
                    }
                    String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                    if (detail == null || detail.isBlank()) {
                        log.warn("[AI类型-轮询] 未获取到工作流详情: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                        return;
                    }
                    String status = extractStatus(detail);
                    String lower = status == null ? null : status.toLowerCase();
                    log.info("[AI类型-轮询] 查询结果: aiId={}, workflowRunId={}, status={}", aiRecordIdFinal,
                            workflowRunIdFinal, lower);
                    if ("succeeded".equals(lower) || "completed".equals(lower)) {
                        String outputText = extractOutputText(detail);
                        current.setExecuteStatus("COMPLETED");
                        current.setResult(outputText != null ? outputText : detail);
                        updateById(current);
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI类型-轮询] 已完成: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    } else if ("failed".equals(lower) || "error".equals(lower)) {
                        current.setExecuteStatus("FAILED");
                        current.setResult(detail);
                        updateById(current);
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI类型-轮询] 已失败: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    }
                } catch (Exception ex) {
                    log.error("[AI类型-轮询] 异常, workflowRunId={}", workflowRunIdFinal, ex);
                }
            }, 0, 10, TimeUnit.SECONDS);
            futureRef.set(scheduled);

            return aiId;
        } catch (Exception e) {
            log.error("类型总体AI解读异常, indicatorTypeId={}, period={}", indicatorTypeId, period, e);
            return null;
        }
    }

    @Override
    public Long restartExpenseOverviewAnalysis(Long indicatorTypeId, String period) {
        return restartOverviewWithPrompt(indicatorTypeId, period, "expense_overview");
    }

    @Override
    public Long restartBalanceOverviewAnalysis(Long indicatorTypeId, String period) {
        return restartOverviewWithPrompt(indicatorTypeId, period, "balance_overview");
    }

    @Override
    public Long restartProfitOverviewAnalysis(Long indicatorTypeId, String period) {
        return restartOverviewWithPrompt(indicatorTypeId, period, "profit_overview");
    }

    /**
     * 按类型与月份聚合“总览所需数据”并触发Dify工作流。
     * overviewType: expense_overview / balance_overview / profit_overview
     */
    private Long restartOverviewWithPrompt(Long indicatorTypeId, String period, String overviewType) {
        try {
            // 当月明细CSV与当月合计
            var records = indicatorValuesService.listByTypeAndPeriod(indicatorTypeId, period);
            StringBuilder csv = new StringBuilder();
            csv.append("indicator_id,indicator_name,period,value\n");
            java.math.BigDecimal monthlyTotal = java.math.BigDecimal.ZERO;
            for (var v : records) {
                String val = v.getValue() == null ? "" : v.getValue().toString();
                if (v.getValue() != null)
                    monthlyTotal = monthlyTotal.add(new java.math.BigDecimal(val));
                csv.append(v.getIndicatorId()).append(',')
                        .append('"')
                        .append((v.getIndicatorName() == null ? "" : v.getIndicatorName()).replace("\"", "\"\""))
                        .append('"').append(',')
                        .append(period).append(',')
                        .append(val)
                        .append('\n');
            }

            String systemPrompt;
            java.util.Map<String, Object> inputs = new java.util.HashMap<>();
            IndicatorTypesEntity indicatorType = indicatorTypesService.getById(indicatorTypeId);
            if (indicatorType != null)
                inputs.put("indicatorTypeName", indicatorType.getTypeName());
            inputs.put("period", period);
            inputs.put("overviewType", overviewType);
            inputs.put("detailData", csv.toString());

            if ("expense_overview".equalsIgnoreCase(overviewType)) {
                // 费用页：本月、上月、去年同期、年度预算、执行进度%
                java.math.BigDecimal cur = getStatsTotalValue(period, indicatorTypeId);
                String lastMonth = prevMonth(period);
                String lastYearSame = (Integer.parseInt(period.substring(0, 4)) - 1) + "-" + period.substring(5, 7);
                java.math.BigDecimal prev = getStatsTotalValue(lastMonth, indicatorTypeId);
                java.math.BigDecimal lastYearVal = getStatsTotalValue(lastYearSame, indicatorTypeId);
                int year = Integer.parseInt(period.substring(0, 4));
                java.math.BigDecimal annualBudget = indicatorAnnualBudgetService.sumBudgetByTypeAndYear(indicatorTypeId,
                        year);
                java.math.BigDecimal ytd = sumStatsTotalValueYearToMonth(period, indicatorTypeId);
                java.math.BigDecimal completionRate = java.math.BigDecimal.ZERO;
                if (annualBudget != null && annualBudget.signum() > 0 && ytd != null) {
                    completionRate = ytd.divide(annualBudget, 6, java.math.RoundingMode.HALF_UP)
                            .multiply(new java.math.BigDecimal("100"));
                }

                systemPrompt = "你是企业费用管理专家。以下为本月费用总览（单位：元）：\n"
                        + "- 本月总费用：" + formatCurrency(cur) + "\n"
                        + "- 上月费用：" + formatCurrency(prev) + "\n"
                        + "- 去年同期：" + formatCurrency(lastYearVal) + "\n"
                        + "- 年度预算：" + formatCurrency(annualBudget) + "\n"
                        + "- 年度执行进度：" + formatPercent(completionRate) + "\n\n"
                        + "请结合以上数值与提供的当月费用明细（detailData 为CSV），输出中文结构化解读：\n"
                        + "1) 核心结论：对比上月/去年同期的总体变化；\n"
                        + "2) 趋势解读：上升/下降的主要类别或科目；\n"
                        + "3) 预算执行：进度是否合理，风险点与预警；\n"
                        + "4) 异常与建议：指出异常点并给出可执行的优化建议。";
            } else if ("balance_overview".equalsIgnoreCase(overviewType)) {
                var list = indicatorValuesService.listByTypeAndPeriod(indicatorTypeId, period);
                java.math.BigDecimal currentAssets = pickByNames(list, new String[] { "流动资产合计" });
                java.math.BigDecimal nonCurrentAssets = pickByNames(list, new String[] { "非流动资产合计" });
                java.math.BigDecimal totalAssets = nvl(currentAssets).add(nvl(nonCurrentAssets));
                java.math.BigDecimal currentLiabilities = pickByNames(list, new String[] { "流动负债合计" });
                java.math.BigDecimal nonCurrentLiabilities = pickByNames(list, new String[] { "非流动负债合计" });
                java.math.BigDecimal totalLiabilities = nvl(currentLiabilities).add(nvl(nonCurrentLiabilities));
                java.math.BigDecimal ratio = java.math.BigDecimal.ZERO;
                if (totalAssets.signum() > 0) {
                    ratio = totalLiabilities.divide(totalAssets, 6, java.math.RoundingMode.HALF_UP)
                            .multiply(new java.math.BigDecimal("100"));
                }

                systemPrompt = "你是资产负债分析专家。以下为本月资产负债概览（单位：元）：\n"
                        + "- 流动资产：" + formatCurrency(currentAssets) + "；非流动资产：" + formatCurrency(nonCurrentAssets)
                        + "；资产合计：" + formatCurrency(totalAssets) + "\n"
                        + "- 流动负债：" + formatCurrency(currentLiabilities) + "；非流动负债："
                        + formatCurrency(nonCurrentLiabilities) + "；负债合计：" + formatCurrency(totalLiabilities) + "\n"
                        + "- 资产负债率：" + formatPercent(ratio) + "\n\n"
                        + "请结合以上数值与提供的当月明细（detailData 为CSV），输出中文结构化解读：\n"
                        + "1) 偿债能力与流动性：资产负债率、流动与非流动匹配情况；\n"
                        + "2) 结构分析：结构变化的影响与原因；\n"
                        + "3) 风险点：短期偿债压力、杠杆水平；\n"
                        + "4) 建议：资产盘活、负债优化与资金管理。";
            } else { // 利润页
                var list = indicatorValuesService.listByTypeAndPeriod(indicatorTypeId, period);
                java.math.BigDecimal operatingRevenue = pickByNames(list, new String[] { "营业总收入", "营业收入" });
                java.math.BigDecimal operatingCost = pickByNames(list, new String[] { "营业总成本", "营业成本" });
                java.math.BigDecimal operatingProfit = pickByNames(list, new String[] { "营业利润" });
                java.math.BigDecimal totalProfitAmount = pickByNames(list, new String[] { "利润总额" });
                java.math.BigDecimal netProfitAmount = pickByNames(list, new String[] { "净利润" });
                java.math.BigDecimal totalComprehensiveIncome = pickByNames(list, new String[] { "综合收益总额" });

                systemPrompt = "你是利润分析专家。以下为本月利润概览（单位：元）：\n"
                        + "- 营业总收入：" + formatCurrency(operatingRevenue) + "；营业总成本："
                        + formatCurrency(operatingCost) + "；营业利润：" + formatCurrency(operatingProfit) + "\n"
                        + "- 利润总额：" + formatCurrency(totalProfitAmount) + "；净利润：" + formatCurrency(netProfitAmount)
                        + "；综合收益总额：" + formatCurrency(totalComprehensiveIncome) + "\n\n"
                        + "请结合以上数值与提供的当月明细（detailData 为CSV），输出中文结构化解读：\n"
                        + "1) 利润驱动：收入与成本的贡献；\n"
                        + "2) 费用与效率：费用率、毛利/净利变化；\n"
                        + "3) 异常波动：指出异常项目与可能原因；\n"
                        + "4) 改进建议：提升盈利能力的具体措施。";
            }

            inputs.put("systemPrompt", systemPrompt);

            // 触发Dify
            String apiKey = (totalAnalysisApiKey == null || totalAnalysisApiKey.isBlank()) ? null : totalAnalysisApiKey;
            String workflowRunId = difyService.startWorkflowStreaming(inputs, "yjzb", apiKey);
            log.info("启动总览AI解读：type={}, indicatorTypeId={}, period={}, workflowRunId={}", overviewType, indicatorTypeId,
                    period, workflowRunId);
            if (workflowRunId == null) {
                log.error("总览AI解读启动失败, type={}, indicatorTypeId={}, period={}", overviewType, indicatorTypeId, period);
                return null;
            }

            // 写入RUNNING记录
            IndicatorAiAnalysisEntity ai = new IndicatorAiAnalysisEntity();
            ai.setIndicatorId(indicatorTypeId);
            ai.setPeriod(period);
            ai.setInputParams("{\"scope\":\"overview-" + overviewType + "\"}");
            ai.setExecuteTime(new java.util.Date());
            ai.setExecuteStatus("RUNNING");
            ai.setWorkflowRunId(workflowRunId);
            save(ai);

            Long aiId = ai.getId();

            // 轮询
            final String finalApiKey = apiKey;
            final long aiRecordIdFinal = aiId;
            final String workflowRunIdFinal = workflowRunId;
            final java.util.concurrent.atomic.AtomicReference<java.util.concurrent.ScheduledFuture<?>> futureRef = new java.util.concurrent.atomic.AtomicReference<>();
            java.util.concurrent.ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
                try {
                    log.debug("[AI总览-轮询] tick: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    IndicatorAiAnalysisEntity current = getById(aiRecordIdFinal);
                    if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                        java.util.concurrent.ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI总览-轮询] 停止轮询: aiId={}, 当前状态={}", aiRecordIdFinal,
                                current == null ? "null" : current.getExecuteStatus());
                        return;
                    }
                    String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                    if (detail == null || detail.isBlank()) {
                        log.warn("[AI总览-轮询] 未获取到工作流详情: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                        return;
                    }
                    String status = extractStatus(detail);
                    String lower = status == null ? null : status.toLowerCase();
                    log.info("[AI总览-轮询] 查询结果: aiId={}, workflowRunId={}, status={}", aiRecordIdFinal,
                            workflowRunIdFinal, lower);
                    if ("succeeded".equals(lower) || "completed".equals(lower)) {
                        String outputText = extractOutputText(detail);
                        current.setExecuteStatus("COMPLETED");
                        current.setResult(outputText != null ? outputText : detail);
                        updateById(current);
                        java.util.concurrent.ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI总览-轮询] 已完成: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    } else if ("failed".equals(lower) || "error".equals(lower)) {
                        current.setExecuteStatus("FAILED");
                        current.setResult(detail);
                        updateById(current);
                        java.util.concurrent.ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI总览-轮询] 已失败: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    }
                } catch (Exception ex) {
                    log.error("[AI总览-轮询] 异常, workflowRunId={}", workflowRunIdFinal, ex);
                }
            }, 0, 20, java.util.concurrent.TimeUnit.SECONDS);
            futureRef.set(scheduled);

            return aiId;
        } catch (Exception e) {
            log.error("总览AI解读异常, type={}, indicatorTypeId={}, period={}", overviewType, indicatorTypeId, period, e);
            return null;
        }
    }

    private String buildOverviewSystemPrompt(String overviewType) {
        // 已在分支内内联提示词，这里保留兜底
        return "你是财务分析专家。上文已给出本月关键数值（以中文/人民币格式），请结合CSV明细进行结构化中文解读并给出可执行建议。";
    }

    // === 数值格式化 ===
    private String formatCurrency(java.math.BigDecimal v) {
        java.math.BigDecimal val = nvl(v).setScale(2, java.math.RoundingMode.HALF_UP);
        java.text.NumberFormat nf = java.text.NumberFormat.getInstance(java.util.Locale.CHINA);
        nf.setGroupingUsed(true);
        nf.setMaximumFractionDigits(2);
        nf.setMinimumFractionDigits(2);
        return nf.format(val);
    }

    private String formatPercent(java.math.BigDecimal percentValue) {
        java.math.BigDecimal val = nvl(percentValue).setScale(2, java.math.RoundingMode.HALF_UP);
        return val.toPlainString() + "%";
    }

    private java.math.BigDecimal getStatsTotalValue(String period, Long indicatorTypeId) {
        try {
            java.util.Map<String, Object> map = indicatorValuesService.getIndicatorStatistics(period, indicatorTypeId,
                    false);
            Object val = map == null ? null : map.get("totalValue");
            if (val == null)
                return java.math.BigDecimal.ZERO;
            return new java.math.BigDecimal(val.toString());
        } catch (Exception e) {
            return java.math.BigDecimal.ZERO;
        }
    }

    private java.math.BigDecimal sumStatsTotalValueYearToMonth(String period, Long indicatorTypeId) {
        String y = period.substring(0, 4);
        int mm = Integer.parseInt(period.substring(5, 7));
        java.math.BigDecimal sum = java.math.BigDecimal.ZERO;
        for (int m = 1; m <= mm; m++) {
            String p = String.format("%s-%02d", y, m);
            sum = sum.add(getStatsTotalValue(p, indicatorTypeId));
        }
        return sum;
    }

    private java.math.BigDecimal pickByNames(
            java.util.List<org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO> list,
            String[] nameCandidates) {
        if (list == null)
            return java.math.BigDecimal.ZERO;
        for (String name : nameCandidates) {
            java.util.Optional<org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO> found = list.stream()
                    .filter(v -> {
                        String n = v.getIndicatorName() == null ? "" : v.getIndicatorName();
                        return n.contains(name);
                    }).findFirst();
            if (found.isPresent()) {
                java.math.BigDecimal v = found.get().getValue();
                return v == null ? java.math.BigDecimal.ZERO : v;
            }
        }
        return java.math.BigDecimal.ZERO;
    }

    private java.math.BigDecimal nvl(java.math.BigDecimal v) {
        return v == null ? java.math.BigDecimal.ZERO : v;
    }

    private String prevMonth(String period) {
        java.time.YearMonth ym = java.time.YearMonth.parse(period);
        return ym.minusMonths(1).toString();
    }

    @Override
    public IndicatorAiAnalysisEntity getLatestByIndicatorAndPeriod(Long indicatorId, String period) {
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<IndicatorAiAnalysisEntity> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        queryWrapper.eq("indicator_id", indicatorId)
                .eq("period", period)
                .orderByDesc("create_time")
                .last("limit 1");
        return getOne(queryWrapper);
    }

    @Override
    public Long restartAnalysisForIndicatorValueWithPrompt(Long indicatorValueId, String systemPrompt) {
        try {
            if (systemPrompt != null && systemPrompt.isBlank()) {
                systemPrompt = null;
            }
            promptOverrideTL.set(systemPrompt);
            return restartAnalysisForIndicatorValue(indicatorValueId);
        } finally {
            promptOverrideTL.remove();
        }
    }

    @Override
    public String buildPromptPreview(Long indicatorId, String period) {
        // 这里返回Dify工作流中使用的提示词模板
        // 根据您提供的模板内容构建
        StringBuilder prompt = new StringBuilder();

        prompt.append("## 关键业务特点与规律\n");

        // 获取业务规则（预测知识，knowledgeType=1）
        try {
            String businessRule = businessKnowledgeRuleCache != null
                    ? businessKnowledgeRuleCache.get(indicatorId, 1)
                    : null;
            if (businessRule == null || businessRule.isBlank()) {
                businessRule = buildBusinessRuleForIndicator(indicatorId);
            }
            if (businessRule != null && !businessRule.isBlank()) {
                prompt.append(businessRule);
            } else {
                prompt.append("- 暂无相关业务规则");
            }
        } catch (Exception e) {
            log.warn("获取业务规则失败 indicatorId={}, err={}", indicatorId, e.getMessage());
            prompt.append("- 业务规则获取失败");
        }

        prompt.append("\n\n## 预测步骤\n");
        prompt.append("1. 阅读历史数据\n");
        prompt.append("2. 给你一些今年已发生的月份数据，已发生月份不需要预测，你用于参照来修正后续预测。\n");
        prompt.append("3. 充分考虑关键业务特点与规律\n");
        prompt.append("4. 总结今年已发生月份数据的情况，填到context字段\n");
        prompt.append("5. **核心预测**：基于分析结果，预测每个月的具体金额。\n");
        prompt.append("6. **提供理由**：详细解释每个月（已发生的月份不需要预测）预测金额的依据，结合上述数据和业务规律进行阐述。");

        return prompt.toString();
    }

    @Override
    public java.util.List<String> getBusinessRulesList(Long indicatorId) {
        try {
            log.info("获取预测类业务规则列表 indicatorId={}, knowledgeType=1", indicatorId);
            String rules = businessKnowledgeRuleCache != null
                    ? businessKnowledgeRuleCache.get(indicatorId, 1)
                    : null;
            log.info("从缓存获取到的预测规则内容: indicatorId={}, rules={}", indicatorId, rules);

            if (rules == null || rules.isBlank()) {
                // 兜底：尝试通过buildBusinessRuleForIndicator方法获取
                log.info("缓存中无预测规则，尝试通过buildBusinessRuleForIndicator获取");
                rules = buildBusinessRuleForIndicator(indicatorId);
                log.info("通过buildBusinessRuleForIndicator获取的规则: indicatorId={}, rules={}", indicatorId, rules);
            }

            if (rules == null || rules.isBlank()) {
                // 通用兜底：尝试读取通用规则（indicatorId=0 或 -1）
                log.info("指标专属预测规则为空，尝试获取通用规则");
                String common0 = businessKnowledgeRuleCache != null
                        ? businessKnowledgeRuleCache.get(0L, 1)
                        : null;
                String commonNeg = businessKnowledgeRuleCache != null
                        ? businessKnowledgeRuleCache.get(-1L, 1)
                        : null;
                log.info("通用预测规则: common0={}, commonNeg={}", common0, commonNeg);
                rules = (common0 != null && !common0.isBlank()) ? common0 : (commonNeg != null ? commonNeg : "");
            }

            if (rules == null || rules.isBlank()) {
                log.warn("未找到任何预测类业务规则 indicatorId={}", indicatorId);
                return java.util.Arrays.asList("暂无匹配的预测类业务知识规则");
            }

            // 将规则文本按行分割，过滤空行和仅包含符号的行
            java.util.List<String> rulesList = java.util.Arrays.stream(rules.split("\n"))
                    .map(String::trim)
                    .filter(line -> !line.isEmpty() && !line.matches("^[-*+\\s]*$"))
                    .map(line -> line.replaceFirst("^[-*+]\\s*", "")) // 移除行首的列表符号
                    .filter(line -> !line.isEmpty())
                    .collect(java.util.stream.Collectors.toList());

            log.info("解析后的预测规则列表 indicatorId={}, count={}, rules={}", indicatorId, rulesList.size(), rulesList);
            return rulesList;
        } catch (Exception e) {
            log.warn("获取预测类业务规则列表失败 indicatorId={}, err={}", indicatorId, e.getMessage());
            return java.util.Arrays.asList("预测类业务知识规则加载失败");
        }
    }

    @Override
    public java.util.Map<String, java.util.List<BusinessKnowledgeVO>> getBusinessKnowledgeByCategory(Long indicatorId) {
        try {
            log.info("获取指标关联的业务知识详细信息（按财务分类分组） indicatorId={}", indicatorId);

            // 查询指标关联的预测类业务知识，包含财务分类信息
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<BusinessKnowledgeEntity> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            queryWrapper.eq("knowledge_type", 1);
            java.util.List<BusinessKnowledgeEntity> list = businessKnowledgeService.list(queryWrapper);

            if (list == null || list.isEmpty()) {
                return new java.util.HashMap<>();
            }

            String idStr = String.valueOf(indicatorId);
            java.util.Map<String, java.util.List<BusinessKnowledgeVO>> categoryMap = new java.util.LinkedHashMap<>();

            for (BusinessKnowledgeEntity entity : list) {
                if (entity == null)
                    continue;

                String apps = entity.getApplicableIndicators();
                if (!applicableToIndicator(apps, idStr))
                    continue;

                BusinessKnowledgeVO vo = BusinessKnowledgeWrapper.build().entityVO(entity);

                // 优先使用财务分类名称，如果为空则使用"未分类"
                String categoryKey = vo.getFinanceCategoryName();
                if (categoryKey == null || categoryKey.trim().isEmpty()) {
                    categoryKey = "未分类";
                }

                categoryMap.computeIfAbsent(categoryKey, k -> new java.util.ArrayList<>()).add(vo);
            }

            log.info("获取到业务知识分类数量: {}", categoryMap.size());
            return categoryMap;

        } catch (Exception e) {
            log.error("获取业务知识分类失败 indicatorId={}", indicatorId, e);
            return new java.util.HashMap<>();
        }
    }

    @Override
    public boolean saveOrUpdateBusinessKnowledge(BusinessKnowledgeEntity businessKnowledge) {
        try {
            log.info("保存或更新业务知识 id={}, title={}", businessKnowledge.getId(), businessKnowledge.getKnowledgeTitle());

            boolean result = businessKnowledgeService.saveOrUpdate(businessKnowledge);

            if (result) {
                // 更新缓存
                businessKnowledgeRuleCache.rebuildWith(businessKnowledgeService.list());
                log.info("业务知识保存成功，缓存已更新");
            }

            return result;

        } catch (Exception e) {
            log.error("保存业务知识失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteBusinessKnowledge(Long id) {
        try {
            log.info("删除业务知识 id={}", id);

            boolean result = businessKnowledgeService.removeById(id);

            if (result) {
                // 更新缓存
                businessKnowledgeRuleCache.rebuildWith(businessKnowledgeService.list());
                log.info("业务知识删除成功，缓存已更新");
            }

            return result;

        } catch (Exception e) {
            log.error("删除业务知识失败 id={}", id, e);
            return false;
        }
    }

    @Override
    public java.util.List<String> getBusinessKnowledgeCategories(Long indicatorId) {
        try {
            log.info("获取业务知识分类列表（基于财务分类） indicatorId={}", indicatorId);

            // 查询指标关联的预测类业务知识
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<BusinessKnowledgeEntity> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            queryWrapper.eq("knowledge_type", 1);
            java.util.List<BusinessKnowledgeEntity> list = businessKnowledgeService.list(queryWrapper);

            if (list == null || list.isEmpty()) {
                return new java.util.ArrayList<>();
            }

            String idStr = String.valueOf(indicatorId);
            java.util.Set<String> categories = new java.util.LinkedHashSet<>();

            for (BusinessKnowledgeEntity entity : list) {
                if (entity == null)
                    continue;

                String apps = entity.getApplicableIndicators();
                if (!applicableToIndicator(apps, idStr))
                    continue;

                BusinessKnowledgeVO vo = BusinessKnowledgeWrapper.build().entityVO(entity);

                // 使用财务分类名称
                String categoryName = vo.getFinanceCategoryName();

                if (categoryName != null && !categoryName.trim().isEmpty()) {
                    categories.add(categoryName.trim());
                }
            }

            java.util.List<String> result = new java.util.ArrayList<>(categories);
            if (result.isEmpty()) {
                result.add("未分类");
            }

            log.info("获取到业务知识分类: {}", result);
            return result;

        } catch (Exception e) {
            log.error("获取业务知识分类失败 indicatorId={}", indicatorId, e);
            return java.util.Arrays.asList("未分类");
        }
    }

}
