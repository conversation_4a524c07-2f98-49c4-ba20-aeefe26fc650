<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.IndicatorValuesDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springblade.modules.yjzb.pojo.entity.IndicatorValuesDetailEntity">
        <id column="id" property="id"/>
        <result column="indicator_id" property="indicatorId"/>
        <result column="period" property="period"/>
        <result column="voucher_no" property="voucherNo"/>
        <result column="data_summary" property="dataSummary"/>
        <result column="amount" property="amount"/>
        <result column="category" property="category"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, indicator_id, period, voucher_no, data_summary, amount, category, 
        create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

    <!-- 分页查询指标数据明细 -->
    <select id="selectIndicatorValuesDetailPage" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO">
        SELECT 
            d.id,
            d.indicator_id,
            t.type_name as indicatorName,
            d.period,
            d.voucher_no,
            d.data_summary,
            d.amount,
            d.category,
            d.create_user,
            d.create_dept,
            d.create_time,
            d.update_user,
            d.update_time,
            d.status,
            CASE WHEN d.status = 1 THEN '正常' ELSE '禁用' END as statusName,
            d.is_deleted
        FROM yjzb_indicator_values_detail d
        LEFT JOIN yjzb_indicator_types t ON d.indicator_id = t.id
        <where>
            d.is_deleted = 0
            <if test="indicatorValuesDetail.indicatorId != null">
                AND d.indicator_id = #{indicatorValuesDetail.indicatorId}
            </if>
            <if test="indicatorValuesDetail.period != null and indicatorValuesDetail.period != ''">
                AND d.period = #{indicatorValuesDetail.period}
            </if>
            <if test="indicatorValuesDetail.voucherNo != null and indicatorValuesDetail.voucherNo != ''">
                AND d.voucher_no LIKE CONCAT('%', #{indicatorValuesDetail.voucherNo}, '%')
            </if>
            <if test="indicatorValuesDetail.category != null and indicatorValuesDetail.category != ''">
                AND d.category LIKE CONCAT('%', #{indicatorValuesDetail.category}, '%')
            </if>
            <if test="indicatorValuesDetail.status != null">
                AND d.status = #{indicatorValuesDetail.status}
            </if>
        </where>
        ORDER BY d.create_time DESC
    </select>

    <!-- 根据指标ID和期间查询明细列表 -->
    <select id="selectByIndicatorIdAndPeriod" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO">
        SELECT 
            d.id,
            d.indicator_id,
            t.type_name as indicatorName,
            d.period,
            d.voucher_no,
            d.data_summary,
            d.amount,
            d.category,
            d.create_user,
            d.create_dept,
            d.create_time,
            d.update_user,
            d.update_time,
            d.status,
            CASE WHEN d.status = 1 THEN '正常' ELSE '禁用' END as statusName,
            d.is_deleted
        FROM yjzb_indicator_values_detail d
        LEFT JOIN yjzb_indicator_types t ON d.indicator_id = t.id
        WHERE d.indicator_id = #{indicatorId}
          AND d.period = #{period}
          AND d.is_deleted = 0
        ORDER BY d.create_time DESC
    </select>

    <!-- 根据指标ID和期间区间查询明细列表 -->
    <select id="selectByIndicatorIdAndPeriodRange" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO">
        SELECT 
            d.id,
            d.indicator_id,
            t.type_name as indicatorName,
            d.period,
            d.voucher_no,
            d.data_summary,
            d.amount,
            d.category,
            d.create_user,
            d.create_dept,
            d.create_time,
            d.update_user,
            d.update_time,
            d.status,
            CASE WHEN d.status = 1 THEN '正常' ELSE '禁用' END as statusName,
            d.is_deleted
        FROM yjzb_indicator_values_detail d
        LEFT JOIN yjzb_indicator_types t ON d.indicator_id = t.id
        WHERE d.indicator_id = #{indicatorId}
          AND d.period BETWEEN #{startPeriod} AND #{endPeriod}
          AND d.is_deleted = 0
        ORDER BY d.period ASC, d.create_time DESC
    </select>

    <!-- 根据指标ID和期间计算总金额 -->
    <select id="sumAmountByIndicatorIdAndPeriod" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM yjzb_indicator_values_detail
        WHERE indicator_id = #{indicatorId}
          AND period = #{period}
          AND is_deleted = 0
          AND status = 1
    </select>

    <!-- 根据指标ID和期间区间计算总金额 -->
    <select id="sumAmountByIndicatorIdAndPeriodRange" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM yjzb_indicator_values_detail
        WHERE indicator_id = #{indicatorId}
          AND period BETWEEN #{startPeriod} AND #{endPeriod}
          AND is_deleted = 0
          AND status = 1
    </select>

    <!-- 根据分类统计金额 -->
    <select id="sumAmountByCategory" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO">
        SELECT 
            category,
            COALESCE(SUM(amount), 0) as amount,
            COUNT(*) as count
        FROM yjzb_indicator_values_detail
        WHERE indicator_id = #{indicatorId}
          AND period = #{period}
          AND is_deleted = 0
          AND status = 1
        GROUP BY category
        ORDER BY amount DESC
    </select>

    <!-- 按部门统计金额：基于 create_dept 聚合，并补充部门名称 -->
    <select id="sumAmountByDept" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO">
        SELECT 
            d.create_dept AS createDept,
            bd.dept_name AS createDeptName,
            COALESCE(SUM(d.amount), 0) AS amount,
            COUNT(*) AS count
        FROM yjzb_indicator_values_detail d
        LEFT JOIN blade_dept bd ON bd.id = d.create_dept AND bd.is_deleted = 0
        WHERE d.indicator_id = #{indicatorId}
          AND d.period = #{period}
          AND d.is_deleted = 0
          AND d.status = 1
        GROUP BY d.create_dept, bd.dept_name
        ORDER BY amount DESC
    </select>

    <!-- 批量插入指标数据明细 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO yjzb_indicator_values_detail (
            id, indicator_id, period, voucher_no, data_summary, amount, category,
            create_user, create_dept, create_time, update_user, update_time, status, is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.indicatorId}, #{item.period}, #{item.voucherNo}, 
                #{item.dataSummary}, #{item.amount}, #{item.category},
                #{item.createUser}, #{item.createDept}, #{item.createTime}, 
                #{item.updateUser}, #{item.updateTime}, #{item.status}, #{item.isDeleted}
            )
        </foreach>
    </insert>

    <!-- 根据凭证号查询明细 -->
    <select id="selectByVoucherNo" resultType="org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO">
        SELECT 
            d.id,
            d.indicator_id,
            t.type_name as indicatorName,
            d.period,
            d.voucher_no,
            d.data_summary,
            d.amount,
            d.category,
            d.create_user,
            d.create_dept,
            d.create_time,
            d.update_user,
            d.update_time,
            d.status,
            CASE WHEN d.status = 1 THEN '正常' ELSE '禁用' END as statusName,
            d.is_deleted
        FROM yjzb_indicator_values_detail d
        LEFT JOIN yjzb_indicator_types t ON d.indicator_id = t.id
        WHERE d.voucher_no = #{voucherNo}
          AND d.is_deleted = 0
        ORDER BY d.create_time DESC
    </select>

    <!-- 根据指标ID、分类和期间查询金额 -->
    <select id="sumAmountByIndicatorIdAndCategoryAndPeriod" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM yjzb_indicator_values_detail
        WHERE indicator_id = #{indicatorId}
          AND category = #{category}
          AND period = #{period}
          AND is_deleted = 0
          AND status = 1
    </select>

    <!-- 根据指标ID和分类查询指定期间范围内的金额 -->
    <select id="sumAmountByIndicatorIdAndCategoryAndPeriodRange" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM yjzb_indicator_values_detail
        WHERE indicator_id = #{indicatorId}
          AND category = #{category}
          AND period BETWEEN #{startPeriod} AND #{endPeriod}
          AND is_deleted = 0
          AND status = 1
    </select>

    <!-- 根据指标ID和分类查询指定年份的所有月份数据 -->
    <select id="selectMonthlyDataByIndicatorIdAndCategoryAndYear" resultType="java.util.HashMap">
        SELECT 
            period,
            COALESCE(SUM(amount), 0) as amount
        FROM yjzb_indicator_values_detail
        WHERE indicator_id = #{indicatorId}
          AND category = #{category}
          AND period LIKE CONCAT(#{year}, '-%')
          AND is_deleted = 0
          AND status = 1
        GROUP BY period
        ORDER BY period
    </select>

</mapper>
