<template>
  <div class="indicator-multi-selector">
    <el-button :disabled="disabled" size="small" type="primary" plain @click="openDialog">
      {{ buttonText }}<span v-if="selectedCount">（已选{{ selectedCount }}）</span>
    </el-button>
    <el-dialog
      v-model="visible"
      :title="dialogTitle"
      width="720px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="toolbar">
        <el-input
          v-model="search"
          :placeholder="placeholder"
          clearable
          @input="onSearchInput"
          @clear="handleSearchClear"
        >
          <template #prefix>
            <i class="el-icon-search" />
          </template>
        </el-input>
        <div class="toolbar-btns">
          <el-button size="small" @click="handleSelectAll" :loading="selectAllLoading">全选</el-button>
          <el-button size="small" @click="handleClear">清空</el-button>
        </div>
      </div>

      <el-scrollbar max-height="420px">
        <el-checkbox-group v-model="tempSelected">
          <div v-for="item in options" :key="item.id" class="option-item">
            <el-checkbox :label="item.id" :disabled="reachedMax && !tempSelected.includes(item.id)">
              <div class="option-text">
                <span class="name">{{ item.name || '未命名' }}</span>
                <span class="code">{{ item.code || '-' }}</span>
              </div>
            </el-checkbox>
          </div>
        </el-checkbox-group>
        <div v-if="!loading && options.length === 0" class="empty">暂无数据</div>
      </el-scrollbar>

      <div class="pager">
        <el-pagination
          layout="prev, pager, next"
          :page-size="page.pageSize"
          :current-page="page.current"
          :total="page.total"
          small
          @current-change="handlePageChange"
        />
      </div>

      <template #footer>
        <el-button @click="visible=false">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getIndicatorList } from '@/api/yjzb/indicator'

export default {
  name: 'IndicatorMultiSelector',
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '输入指标名称或编码搜索'
    },
    buttonText: {
      type: String,
      default: '选择指标'
    },
    max: {
      type: Number,
      default: 0 // 0 表示不限制
    },
    pageSize: {
      type: Number,
      default: 20
    },
    selectAllLimit: {
      type: Number,
      default: 1000 // 全选时最多拉取的条数
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change', 'select-items'],
  data() {
    return {
      visible: false,
      search: '',
      loading: false,
      selectAllLoading: false,
      options: [],
      tempSelected: [],
      page: {
        current: 1,
        pageSize: this.pageSize,
        total: 0,
      },
      debounceTimer: null,
      lastQuery: '',
    }
  },
  computed: {
    selectedCount() {
      return (this.modelValue || []).length
    },
    reachedMax() {
      return this.max > 0 && this.tempSelected.length >= this.max
    },
    dialogTitle() {
      return `选择指标${this.search ? ' - ' + this.search : ''}`
    }
  },
  watch: {
    pageSize(newVal) {
      this.page.pageSize = newVal
    }
  },
  methods: {
    openDialog() {
      this.visible = true
      // 初始化临时选择
      this.tempSelected = Array.isArray(this.modelValue) ? [...this.modelValue] : []
      this.fetchOptions(true)
    },
    async fetchOptions(reset = false) {
      if (reset) this.page.current = 1
      this.loading = true
      try {
        const params = {}
        if (this.search) {
          // 按后端约定使用 name_like 模糊查询（名称/编码搜索由后端实现或统一走名称模糊）
          params.name_like = this.search
        }
        const res = await getIndicatorList(this.page.current, this.page.pageSize, params)
        const data = res?.data?.data || { total: 0, records: [] }
        this.page.total = data.total || 0
        this.options = data.records || []
      } catch (e) {
        this.options = []
      } finally {
        this.loading = false
      }
    },
    handlePageChange(p) {
      this.page.current = p
      this.fetchOptions(false)
    },
    onSearchInput() {
      if (this.debounceTimer) clearTimeout(this.debounceTimer)
      this.debounceTimer = setTimeout(() => {
        // 避免重复请求
        if (this.search === this.lastQuery) return
        this.lastQuery = this.search
        this.fetchOptions(true)
      }, 300)
    },
    handleSearchClear() {
      this.search = ''
      this.lastQuery = ''
      this.fetchOptions(true)
    },
    async handleSelectAll() {
      this.selectAllLoading = true
      try {
        const params = {}
        if (this.search) {
          params.name_like = this.search
        }
        // 为避免过大数据量，限制最大获取数量
        const res = await getIndicatorList(1, this.selectAllLimit, params)
        const list = res?.data?.data?.records || []
        const ids = list.map(it => it.id)
        let merged = Array.from(new Set([...(this.tempSelected || []), ...ids]))
        if (this.max > 0 && merged.length > this.max) {
          merged = merged.slice(0, this.max)
        }
        this.tempSelected = merged
      } finally {
        this.selectAllLoading = false
      }
    },
    handleClear() {
      this.tempSelected = []
    },
    handleConfirm() {
      const value = [...this.tempSelected]
      this.$emit('update:modelValue', value)
      this.$emit('change', value)
      // 向父级回传当前页的选项（便于外部快速构建 id->名称映射）
      const selectedItems = (this.options || []).filter(it => value.includes(it.id))
      this.$emit('select-items', selectedItems)
      this.visible = false
    },
  }
}
</script>

<style scoped>
.indicator-multi-selector {
  display: inline-block;
}
.toolbar { display: flex; gap: 12px; align-items: center; margin-bottom: 10px; }
.toolbar .toolbar-btns { margin-left: auto; display: flex; gap: 8px; }
.option-item { padding: 6px 2px; }
.option-text { display: inline-flex; gap: 12px; align-items: baseline; }
.option-text .name { font-weight: 500; color: #303133; }
.option-text .code { font-size: 12px; color: #909399; }
.empty { text-align: center; color: #909399; padding: 16px 0; }
.pager { margin-top: 10px; text-align: right; }
</style>

