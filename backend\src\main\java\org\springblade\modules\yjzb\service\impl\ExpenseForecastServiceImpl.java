package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.yjzb.entity.ExpenseForecast;
import org.springblade.modules.yjzb.mapper.ExpenseForecastMapper;
import org.springblade.modules.yjzb.service.ExpenseForecastService;
import org.springblade.modules.yjzb.service.IIndicatorValuesDetailService;
import org.springblade.modules.yjzb.service.IIndicatorAnnualBudgetService;
import org.springblade.modules.yjzb.pojo.entity.IndicatorAnnualBudgetEntity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Objects;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesDetailVO;

/**
 * 办公费用预测服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@Slf4j
@Service
public class ExpenseForecastServiceImpl extends ServiceImpl<ExpenseForecastMapper, ExpenseForecast>
        implements ExpenseForecastService {

    private final ObjectMapper objectMapper;
    private final IIndicatorValuesDetailService indicatorValuesDetailService;
    private final IIndicatorAnnualBudgetService indicatorAnnualBudgetService;
    private RestTemplate restTemplate;

    @Value("${mlops.predict.url}")
    private String mlopsPredictUrl;

    @Value("${mlops.predict.jobid}")
    private String mlopsJobId;

    @Value("${mlops.predict.modelid}")
    private String mlopsModelId;

    @Value("${mlops.predict.timeout:120000}")
    private int mlopsTimeout;

    public ExpenseForecastServiceImpl(ObjectMapper objectMapper,
            IIndicatorValuesDetailService indicatorValuesDetailService,
            IIndicatorAnnualBudgetService indicatorAnnualBudgetService) {
        this.objectMapper = objectMapper;
        this.indicatorValuesDetailService = indicatorValuesDetailService;
        this.indicatorAnnualBudgetService = indicatorAnnualBudgetService;
    }

    @PostConstruct
    public void initRestTemplate() {
        // 创建带有超时配置的RestTemplate
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(mlopsTimeout);
        factory.setReadTimeout(mlopsTimeout);
        this.restTemplate = new RestTemplate(factory);
        log.info("RestTemplate初始化完成，超时时间: {}ms", mlopsTimeout);
    }

    @Override
    public ExpenseForecast executeForecast(Long indicatorId, String forecastPeriod, String forecastType,
            String inputData) {
        log.info("开始执行办公费用预测 - indicatorId: {}, forecastPeriod: {}, forecastType: {}", indicatorId, forecastPeriod,
                forecastType);

        // 检查是否有缓存结果
        if (hasCachedForecast(indicatorId, forecastPeriod, forecastType)) {
            log.info("发现缓存的预测结果，直接返回");
            return getForecastResult(indicatorId, forecastPeriod, forecastType);
        }

        // 创建预测记录
        ExpenseForecast forecast = new ExpenseForecast();
        forecast.setIndicatorId(indicatorId);
        forecast.setForecastPeriod(forecastPeriod);
        forecast.setForecastType(forecastType);
        forecast.setInputData(inputData);

        // 保存到数据库
        save(forecast);

        try {
            // 调用MLOps API进行预测
            // 将inputData字符串转换为Map格式
            Map<String, String> inputDataMap = new HashMap<>();
            inputDataMap.put("forecastType", forecastType);
            inputDataMap.put("inputData", inputData);
            ForecastResult mlopsResult = callMlopsApi(inputDataMap);

            // 更新预测结果
            forecast.setForecastValue(mlopsResult.getForecastValue());
            forecast.setConfidenceLevel(mlopsResult.getConfidenceLevel());
            forecast.setMlopsRequestId(mlopsResult.getRequestId());

            updateById(forecast);

            log.info("费用预测完成 - forecastId: {}", forecast.getId());
            return forecast;

        } catch (Exception e) {
            log.error("费用预测失败", e);
            throw new RuntimeException("费用预测失败: " + e.getMessage());
        }
    }

    @Override
    public ExpenseForecast getForecastResult(Long indicatorId, String forecastPeriod, String forecastType) {
        LambdaQueryWrapper<ExpenseForecast> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseForecast::getIndicatorId, indicatorId)
                .eq(ExpenseForecast::getForecastPeriod, forecastPeriod)
                .eq(ExpenseForecast::getForecastType, forecastType)
                .orderByDesc(ExpenseForecast::getCreateTime)
                .last("LIMIT 1");

        return getOne(queryWrapper);
    }

    @Override
    public boolean hasCachedForecast(Long indicatorId, String forecastPeriod, String forecastType) {
        LambdaQueryWrapper<ExpenseForecast> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseForecast::getIndicatorId, indicatorId)
                .eq(ExpenseForecast::getForecastPeriod, forecastPeriod)
                .eq(ExpenseForecast::getForecastType, forecastType);

        return count(queryWrapper) > 0;
    }

    /**
     * 调用MLOps API进行预测
     *
     * @param inputData 输入数据
     * @return 预测结果
     */
    private ForecastResult callMlopsApi(Map<String, String> inputData) {
        try {
            log.info("调用MLOps API - inputData: {}", inputData);

            // 准备MLOps请求参数
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 直接使用inputData作为请求体
            // 将inputData中的所有value从String转为合适的Number类型（Integer或Float），其余保持原样
            Map<String, Object> convertedInputData = new LinkedHashMap<>();
            for (Map.Entry<String, String> entry : inputData.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (value == null) {
                    convertedInputData.put(key, null);
                    continue;
                }
                // 尝试将字符串转换为整数或浮点数
                try {
                    if (value.matches("^-?\\d+$")) {
                        // 整数
                        convertedInputData.put(key, Integer.parseInt(value));
                    } else if (value.matches("^-?\\d*\\.\\d+$")) {
                        // 浮点数
                        convertedInputData.put(key, Float.parseFloat(value));
                    } else {
                        // 不是数字，原样放入
                        convertedInputData.put(key, value);
                    }
                } catch (Exception ex) {
                    // 转换失败，原样放入
                    convertedInputData.put(key, value);
                }
            }
            // 将convertedInputData转为json字符串
            String jsonBody;
            try {
                jsonBody = objectMapper.writeValueAsString(convertedInputData);
                log.info("MLOps API请求JSON: {}", jsonBody);
            } catch (Exception e) {
                log.error("转换为JSON字符串失败: {}", e.getMessage(), e);
                jsonBody = "{}";
            }
            // request传入json字符串和header
            HttpEntity<String> request = new HttpEntity<>(jsonBody, headers);

            // HttpEntity<Map<String, String>> request = new HttpEntity<>(jsonBody,
            // headers);

            // 调用MLOps API
            log.info("开始调用MLOps API，URL: {}, 超时时间: {}ms", mlopsPredictUrl, mlopsTimeout);
            long startTime = System.currentTimeMillis();

            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.postForEntity(mlopsPredictUrl, request, Map.class);

            long endTime = System.currentTimeMillis();
            log.info("MLOps API调用完成，耗时: {}ms", (endTime - startTime));

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();

                Object predict = responseBody.get("predict");
                log.info("MLOps预测结果: {}", responseBody);

                if (predict != null) {
                    // 解析预测结果
                    BigDecimal forecastValue = parseForecastValue(predict);
                    BigDecimal confidenceLevel = parseConfidenceLevel(responseBody);
                    String requestId = generateRequestId();

                    log.info("MLOps预测成功 - forecastValue: {}, confidenceLevel: {}", forecastValue, confidenceLevel);

                    return new ForecastResult(forecastValue, confidenceLevel, requestId);
                } else {
                    log.warn("MLOps API返回失败状态: {}", responseBody);
                }
            } else {
                log.warn("MLOps API调用失败: status={}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用MLOps API异常: {}", e.getMessage(), e);
            log.error("请求URL: {}", mlopsPredictUrl);
            log.error("请求数据: {}", inputData);
        }

        // 如果MLOps调用失败，返回默认值
        log.warn("MLOps调用失败，使用默认预测值");
        return new ForecastResult(
                new BigDecimal("100000.00"),
                new BigDecimal("0.75"),
                "mlops_request_" + System.currentTimeMillis());
    }

    /**
     * 解析预测值
     * 根据API文档，data字段是字符串类型，直接解析字符串即可
     */
    private BigDecimal parseForecastValue(Object data) {
        try {
            if (data == null) {
                return new BigDecimal("100000.00");
            }
            log.info("解析预测值: data={}", data);
            // 根据API文档，data字段是字符串类型，直接转换为字符串解析
            String dataStr = data.toString().trim();
            if (dataStr.isEmpty()) {
                return new BigDecimal("100000.00");
            }

            return new BigDecimal(dataStr);
        } catch (Exception e) {
            log.error("解析预测值失败: data={}, error={}", data, e.getMessage());
            return new BigDecimal("100000.00");
        }
    }

    /**
     * 解析置信度
     */
    private BigDecimal parseConfidenceLevel(Map<String, Object> responseBody) {
        try {
            Object confidence = responseBody.get("confidence_level") != null ? responseBody.get("confidence_level")
                    : responseBody.get("confidence");

            if (confidence instanceof Number) {
                return new BigDecimal(confidence.toString());
            } else if (confidence instanceof String) {
                return new BigDecimal((String) confidence);
            }
        } catch (Exception e) {
            log.error("解析置信度失败", e);
        }
        return new BigDecimal("0.75");
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "mlops_request_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 预测结果内部类
     */
    private static class ForecastResult {
        private final BigDecimal forecastValue;
        private final BigDecimal confidenceLevel;
        private final String requestId;

        public ForecastResult(BigDecimal forecastValue, BigDecimal confidenceLevel, String requestId) {
            this.forecastValue = forecastValue;
            this.confidenceLevel = confidenceLevel;
            this.requestId = requestId;
        }

        public BigDecimal getForecastValue() {
            return forecastValue;
        }

        public BigDecimal getConfidenceLevel() {
            return confidenceLevel;
        }

        public String getRequestId() {
            return requestId;
        }
    }

    @Override
    public List<Map<String, Object>> forecastRemainingMonthsByCategory(Long indicatorId, String period) {
        log.info("开始按类别预测剩余月份的办公费用 - indicatorId: {}, period: {}", indicatorId, period);

        List<Map<String, Object>> forecastResults = new ArrayList<>();
        List<ExpenseForecast> allForecasts = new ArrayList<>(); // 存储所有预测记录

        try {
            // 解析期间，获取年份和月份
            String[] periodParts = period.split("-");
            if (periodParts.length != 2) {
                throw new IllegalArgumentException("期间格式错误，应为YYYY-MM格式");
            }

            int currentYear = Integer.parseInt(periodParts[0]);
            int currentMonth = Integer.parseInt(periodParts[1]);

            log.info("解析期间: 年份={}, 月份={}", currentYear, currentMonth);

            // 获取所有二级分类
            List<String> categories = getSecondaryCategories(indicatorId);
            log.info("获取到二级分类: {}", categories);

            // 创建预测值缓存，用于存储每个分类的预测值
            Map<String, Map<String, Double>> predictionCache = new HashMap<>();

            // 为每个未来月份预测
            for (int month = currentMonth + 1; month <= 12; month++) {
                String yearMonth = currentYear + "-" + String.format("%02d", month);
                double totalPredicted = 0.0;

                // 为每个分类预测该月份的值
                for (String category : categories) {
                    try {
                        double categoryPrediction = predictCategoryForMonth(indicatorId, category, yearMonth,
                                predictionCache);
                        totalPredicted += categoryPrediction;

                        // 将预测值存入缓存，供后续月份使用
                        predictionCache.computeIfAbsent(category, k -> new HashMap<>()).put(yearMonth,
                                categoryPrediction);

                        log.debug("分类 {} 在 {} 的预测值: {}", category, yearMonth, categoryPrediction);

                    } catch (Exception e) {
                        log.warn("预测分类 {} 在 {} 失败: {}", category, yearMonth, e.getMessage());
                        // 使用历史平均值作为备选
                        double fallbackValue = getHistoricalAverage(indicatorId, category);
                        totalPredicted += fallbackValue;
                        predictionCache.computeIfAbsent(category, k -> new HashMap<>()).put(yearMonth, fallbackValue);
                    }
                }

                // 创建该月份的总体预测记录并保存到数据库
                ExpenseForecast monthTotalForecast = new ExpenseForecast();
                monthTotalForecast.setIndicatorId(indicatorId);
                monthTotalForecast.setForecastPeriod(yearMonth);
                monthTotalForecast.setForecastType("TOTAL"); // 总体预测标识
                monthTotalForecast.setForecastValue(new BigDecimal(Math.round(totalPredicted * 100.0) / 100.0));
                monthTotalForecast.setConfidenceLevel(new BigDecimal("0.85")); // 默认置信度
                monthTotalForecast.setMlopsRequestId("total_" + yearMonth + "_" + System.currentTimeMillis());
                monthTotalForecast.setInputData("按类别预测剩余月份办公费用总计，月份: " + yearMonth + ", 共" + categories.size() + "个分类");

                // 保存总体预测记录
                save(monthTotalForecast);
                allForecasts.add(monthTotalForecast);

                Map<String, Object> monthResult = new LinkedHashMap<>();
                monthResult.put("month", yearMonth);
                monthResult.put("value", Math.round(totalPredicted * 100.0) / 100.0);
                monthResult.put("totalForecastId", monthTotalForecast.getId());
                forecastResults.add(monthResult);

                log.info("{} 月份预测完成，总体预测值: {}, 分类预测记录数: {}, 总体预测记录ID: {}",
                        yearMonth, totalPredicted, categories.size(), monthTotalForecast.getId());
            }

        } catch (Exception e) {
            log.error("按类别预测失败", e);
            throw new RuntimeException("按类别预测失败: " + e.getMessage());
        }

        log.info("按类别预测完成，共预测 {} 个月，共保存 {} 条预测记录", forecastResults.size(), allForecasts.size());
        return forecastResults;
    }

    @Override
    public Map<String, Object> forecastNextMonthByCategory(Long indicatorId, String period) {
        log.info("开始预测下一个月的办公费用 - indicatorId: {}, period: {}", indicatorId, period);

        try {
            // 解析期间，获取年份和月份
            String[] periodParts = period.split("-");
            if (periodParts.length != 2) {
                throw new IllegalArgumentException("期间格式错误，应为YYYY-MM格式");
            }

            int currentYear = Integer.parseInt(periodParts[0]);
            int currentMonth = Integer.parseInt(periodParts[1]);

            log.info("解析期间: 年份={}, 月份={}", currentYear, currentMonth);

            // 计算下一个月
            int nextMonth = currentMonth + 1;
            int nextYear = currentYear;
            if (nextMonth > 12) {
                nextMonth = 1;
                nextYear++;
            }

            String nextYearMonth = nextYear + "-" + String.format("%02d", nextMonth);
            log.info("预测目标月份: {}", nextYearMonth);

            // 获取所有二级分类
            List<String> categories = getSecondaryCategories(indicatorId);
            log.info("获取到二级分类: {}", categories);

            // 创建预测值缓存，用于存储每个分类的预测值
            Map<String, Map<String, Double>> predictionCache = new HashMap<>();

            double totalPredicted = 0.0;

            // 为每个分类预测下个月的值
            for (String category : categories) {
                try {
                    double categoryPrediction = predictCategoryForMonth(indicatorId, category, nextYearMonth,
                            predictionCache);
                    totalPredicted += categoryPrediction;

                    // 将预测值存入缓存
                    predictionCache.computeIfAbsent(category, k -> new HashMap<>()).put(nextYearMonth,
                            categoryPrediction);

                    log.debug("分类 {} 在 {} 的预测值: {}", category, nextYearMonth, categoryPrediction);

                } catch (Exception e) {
                    log.warn("预测分类 {} 在 {} 失败: {}", category, nextYearMonth, e.getMessage());
                    // 使用历史平均值作为备选
                    double fallbackValue = getHistoricalAverage(indicatorId, category);
                    totalPredicted += fallbackValue;
                    predictionCache.computeIfAbsent(category, k -> new HashMap<>()).put(nextYearMonth, fallbackValue);
                }
            }

            // 创建总体预测记录并保存到数据库
            ExpenseForecast totalForecast = new ExpenseForecast();
            totalForecast.setIndicatorId(indicatorId);
            totalForecast.setForecastPeriod(nextYearMonth);
            totalForecast.setForecastType("TOTAL"); // 总体预测标识
            totalForecast.setForecastValue(new BigDecimal(Math.round(totalPredicted * 100.0) / 100.0));
            totalForecast.setConfidenceLevel(new BigDecimal("0.85")); // 默认置信度
            totalForecast.setMlopsRequestId("total_" + System.currentTimeMillis());
            totalForecast.setInputData("按类别预测下一个月办公费用总计，共" + categories.size() + "个分类");

            // 保存总体预测记录
            save(totalForecast);
            log.info("总体预测记录已保存，ID: {}, 预测总值: {}", totalForecast.getId(), totalPredicted);

            Map<String, Object> result = new LinkedHashMap<>();
            result.put("month", nextYearMonth);
            result.put("value", Math.round(totalPredicted * 100.0) / 100.0);
            result.put("categories", categories.size());
            result.put("predictionTime", LocalDateTime.now().toString());
            result.put("totalForecastId", totalForecast.getId());

            log.info("下一个月 {} 预测完成，总体预测值: {}, 分类预测记录数: {}", nextYearMonth, totalPredicted, categories.size());
            return result;

        } catch (Exception e) {
            log.error("预测下一个月失败", e);
            throw new RuntimeException("预测下一个月失败: " + e.getMessage());
        }
    }

    @Override
    public ExpenseForecast getLatestTotalForecast(Long indicatorId) {
        LambdaQueryWrapper<ExpenseForecast> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseForecast::getIndicatorId, indicatorId)
                .eq(ExpenseForecast::getForecastType, "TOTAL")
                .orderByDesc(ExpenseForecast::getCreateTime)
                .last("LIMIT 1");

        return getOne(queryWrapper);
    }

    @Override
    public List<ExpenseForecast> getCategoryForecastsByPeriod(Long indicatorId, String period) {
        LambdaQueryWrapper<ExpenseForecast> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpenseForecast::getIndicatorId, indicatorId)
                .eq(ExpenseForecast::getForecastPeriod, period)
                .ne(ExpenseForecast::getForecastType, "TOTAL") // 排除总体预测
                .orderByDesc(ExpenseForecast::getCreateTime);

        return list(queryWrapper);
    }

    @Override
    public List<ExpenseForecast> getLatestCategoryForecasts(Long indicatorId) {
        // 先获取最新的总体预测记录，确定预测期间
        ExpenseForecast latestTotal = getLatestTotalForecast(indicatorId);
        if (latestTotal == null) {
            log.warn("未找到总体预测记录，无法获取分类预测");
            return new ArrayList<>();
        }

        String latestPeriod = latestTotal.getForecastPeriod();
        log.info("获取最新期间 {} 的分类预测结果", latestPeriod);

        return getCategoryForecastsByPeriod(indicatorId, latestPeriod);
    }

    /**
     * 映射分类名称到标准化的分类名称
     * 用于MLOps API调用时的参数标准化
     */
    private String mapCategoryName(String originalCategory) {
        if (originalCategory == null || originalCategory.trim().isEmpty()) {
            log.debug("分类名称为空，映射为: 其他杂项");
            return "其他杂项";
        }

        String category = originalCategory.trim();
        String mappedCategory;

        // 分类名称映射规则
        if (category.contains("办公用品") || category.equals("办公用品")) {
            mappedCategory = "办公用品费";
        } else if (category.contains("邮寄") || category.equals("邮寄费")) {
            mappedCategory = "邮政快递";
        } else if (category.contains("印刷") || category.equals("印刷费")) {
            mappedCategory = "宣传印刷";
        } else if (category.contains("其他") || category.equals("其他杂项")) {
            mappedCategory = "其他杂项";
        } else {
            // 其他分类保持原样
            mappedCategory = category;
        }

        log.debug("分类名称映射: {} -> {}", originalCategory, mappedCategory);
        return mappedCategory;
    }

    /**
     * 获取二级分类列表
     * 只返回固定的4个主要办公费用分类
     */
    private List<String> getSecondaryCategories(Long indicatorId) {
        // 固定返回4个主要分类：办公用品、印刷费、邮寄费、其他
        List<String> categories = Arrays.asList(
                "办公用品",
                "计算机耗材",
                "印刷费",
                "邮寄费",
                "其他");

        log.info("使用固定的4个主要分类进行预测: {}", categories);
        return categories;
    }

    /**
     * 预测特定分类在特定月份的值，并立即存储预测记录
     */
    private double predictCategoryForMonth(Long indicatorId, String category, String yearMonth,
            Map<String, Map<String, Double>> predictionCache) {
        try {
            // 解析年月
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 构建MLOps API的输入数据（按照API文档要求）
            Map<String, String> inputData = new LinkedHashMap<>();

            // 基础参数
            inputData.put("job_id", mlopsJobId);
            inputData.put("model_id", mlopsModelId);
            // inputData.put("secondary_category", mapCategoryName(category));
            inputData.put("secondary_category", category);
            inputData.put("year", String.valueOf(year));
            inputData.put("month", String.valueOf(month));
            inputData.put("quarter", String.valueOf((month - 1) / 3 + 1));

            // 历史数据
            double prevMonthAmount = getHistoricalValue(indicatorId, category, -1, yearMonth, predictionCache);
            double prev2MonthAmount = getHistoricalValue(indicatorId, category, -2, yearMonth, predictionCache);
            double prev3MonthAmount = getHistoricalValue(indicatorId, category, -3, yearMonth, predictionCache);

            inputData.put("prev_month_total_amount", String.valueOf(prevMonthAmount));
            inputData.put("prev_2month_total_amount", String.valueOf(prev2MonthAmount));
            inputData.put("prev_3month_total_amount", String.valueOf(prev3MonthAmount));

            // 平均值
            inputData.put("prev_month_average_amount", String.valueOf(prevMonthAmount));
            inputData.put("prev_2month_average_amount", String.valueOf((prevMonthAmount + prev2MonthAmount) / 2));
            inputData.put("prev_3month_average_amount",
                    String.valueOf((prevMonthAmount + prev2MonthAmount + prev3MonthAmount) / 3));
            inputData.put("prev_3month_average_value",
                    String.valueOf((prevMonthAmount + prev2MonthAmount + prev3MonthAmount) / 3));

            // 去年同期数
            double samePeriodLastYear = getSamePeriodLastYear(indicatorId, category, yearMonth);
            inputData.put("same_period_last_year_total_amount", String.valueOf(samePeriodLastYear));
            inputData.put("same_period_last_year_average_amount", String.valueOf(samePeriodLastYear));

            // 当年累计
            double currentYearCumulative = getCurrentYearCumulative(indicatorId, category, yearMonth, predictionCache);
            inputData.put("current_year_cumulative_amount", String.valueOf(currentYearCumulative));

            // 预算相关（从预算表获取真实值）
            double annualBudget = getAnnualBudget(indicatorId, year);
            inputData.put("annual_budget", String.valueOf(annualBudget));
            inputData.put("budget_completion_rate",
                    String.valueOf(annualBudget > 0 ? currentYearCumulative / annualBudget : 0));
            inputData.put("budget_variance", String.valueOf(annualBudget - currentYearCumulative));

            // 时间特征
            inputData.put("days_in_month", String.valueOf(getDaysInMonth(year, month)));
            inputData.put("is_first_half_year", month <= 6 ? "1" : "0");
            inputData.put("is_month_start", month == 1 ? "1" : "0");
            inputData.put("is_month_end", month == 12 ? "1" : "0");
            inputData.put("is_quarter_start", (month == 1 || month == 4 || month == 7 || month == 10) ? "1" : "0");
            inputData.put("is_quarter_end", (month == 3 || month == 6 || month == 9 || month == 12) ? "1" : "0");

            // 斜率（3个月趋势）
            double slope = calculateSlope(prev3MonthAmount, prev2MonthAmount, prevMonthAmount);
            inputData.put("prev_3month_slope", String.valueOf(slope));
            // 理论进度
            double theoreticalProgress = (double) month / 12.0;
            inputData.put("theoretical_progress", String.valueOf(theoreticalProgress));

            // 交易次数（从数据库获取真实值）
            inputData.put("prev_month_transaction_count",
                    String.valueOf(getTransactionCount(indicatorId, category, -1, yearMonth, predictionCache)));
            inputData.put("prev_2month_transaction_count",
                    String.valueOf(getTransactionCount(indicatorId, category, -2, yearMonth, predictionCache)));
            inputData.put("prev_3month_transaction_count",
                    String.valueOf(getTransactionCount(indicatorId, category, -3, yearMonth, predictionCache)));
            inputData.put("same_period_last_year_transaction_count",
                    String.valueOf(getSamePeriodLastYearTransactionCount(indicatorId, category, yearMonth)));

            // 将inputData转换为JSON字符串，用于存储到数据库
            String inputDataJson;
            try {
                inputDataJson = objectMapper.writeValueAsString(inputData);
            } catch (Exception e) {
                log.error("转换inputData为JSON失败: {}", e.getMessage(), e);
                inputDataJson = inputData.toString();
            }

            // 调用MLOps API
            ForecastResult result = callMlopsApi(inputData);

            // 计算系数调整
            BigDecimal adjustedForecastValue = calculateAdjustedForecastValue(
                    result.getForecastValue(), indicatorId, year, month, category);
            inputDataJson += "------原预测值:" + result.getForecastValue() + "------调整后预测值:" + adjustedForecastValue;
            // 立即创建并保存预测记录
            ExpenseForecast forecast = new ExpenseForecast();
            forecast.setIndicatorId(indicatorId);
            forecast.setForecastPeriod(yearMonth);
            forecast.setForecastType(category); // 存储分类名称
            forecast.setForecastValue(adjustedForecastValue);
            forecast.setConfidenceLevel(result.getConfidenceLevel());
            forecast.setMlopsRequestId(result.getRequestId());
            forecast.setInputData(inputDataJson); // 存储完整的JSON输入数据

            // 保存预测记录
            save(forecast);
            log.info("分类 {} 在 {} 的预测记录已保存，ID: {}, 原始预测值: {}, 调整后预测值: {}, 置信度: {}",
                    category, yearMonth, forecast.getId(), result.getForecastValue(), adjustedForecastValue,
                    result.getConfidenceLevel());

            return adjustedForecastValue.doubleValue();

        } catch (Exception e) {
            log.error("调用MLOps API预测失败", e);
            // 返回历史平均值作为备选
            return getHistoricalAverage(indicatorId, category);
        }
    }

    /**
     * 获取历史值（负数表示往前推几个月）
     * 优先使用真实数据，只有在没有真实数据时才使用预测数据
     */
    private double getHistoricalValue(Long indicatorId, String category, int monthsBack, String currentYearMonth,
            Map<String, Map<String, Double>> predictionCache) {
        try {
            // 计算目标月份
            String[] parts = currentYearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 往前推monthsBack个月
            int targetMonth = month + monthsBack;
            int targetYear = year;

            while (targetMonth <= 0) {
                targetMonth += 12;
                targetYear--;
            }
            while (targetMonth > 12) {
                targetMonth -= 12;
                targetYear++;
            }

            String targetYearMonth = targetYear + "-" + String.format("%02d", targetMonth);

            // 1. 优先从数据库获取真实数据
            BigDecimal amount = indicatorValuesDetailService.sumAmountByIndicatorIdAndCategoryAndPeriod(indicatorId,
                    category, targetYearMonth);
            if (amount != null && amount.doubleValue() > 0) {
                log.debug("使用真实数据: 分类={}, 月份={}, 金额={}", category, targetYearMonth, amount.doubleValue());
                return amount.doubleValue();
            }

            // 2. 如果数据库没有真实数据，检查预测缓存
            Map<String, Double> categoryCache = predictionCache.get(category);
            if (categoryCache != null && categoryCache.containsKey(targetYearMonth)) {
                log.debug("使用预测缓存数据: 分类={}, 月份={}, 金额={}", category, targetYearMonth,
                        categoryCache.get(targetYearMonth));
                return categoryCache.get(targetYearMonth);
            }

            // 3. 如果缓存也没有，尝试从数据库获取已保存的预测数据
            try {
                List<ExpenseForecast> savedForecasts = baseMapper.getCategoryForecastsByPeriod(indicatorId,
                        targetYearMonth);
                if (savedForecasts != null && !savedForecasts.isEmpty()) {
                    for (ExpenseForecast forecast : savedForecasts) {
                        if (category.equals(forecast.getForecastType())) {
                            log.debug("使用已保存的预测数据: 分类={}, 月份={}, 金额={}", category, targetYearMonth,
                                    forecast.getForecastValue().doubleValue());
                            return forecast.getForecastValue().doubleValue();
                        }
                    }
                }
            } catch (Exception e) {
                log.debug("获取已保存预测数据失败: {}", e.getMessage());
            }

            // 如果都没有数据，返回历史平均值作为备选
            double fallbackValue = getHistoricalAverage(indicatorId, category);
            log.debug("使用历史平均值: 分类={}, 月份={}, 金额={}", category, targetYearMonth, fallbackValue);
            return fallbackValue;

        } catch (Exception e) {
            log.warn("获取历史值失败: indicatorId={}, category={}, monthsBack={}, error={}",
                    indicatorId, category, monthsBack, e.getMessage());
            return getHistoricalAverage(indicatorId, category);
        }
    }

    /**
     * 获取移动平均值
     */
    private double getMovingAverage(Long indicatorId, String category, int periods, String currentYearMonth,
            Map<String, Map<String, Double>> predictionCache) {
        try {
            double sum = 0.0;
            int validCount = 0;

            // 计算过去periods个月的平均值
            for (int i = 1; i <= periods; i++) {
                double value = getHistoricalValue(indicatorId, category, -i, currentYearMonth, predictionCache);
                if (value > 0) {
                    sum += value;
                    validCount++;
                }
            }

            if (validCount > 0) {
                return sum / validCount;
            } else {
                return getHistoricalAverage(indicatorId, category);
            }

        } catch (Exception e) {
            log.warn("计算移动平均值失败: indicatorId={}, category={}, periods={}, error={}",
                    indicatorId, category, periods, e.getMessage());
            return getHistoricalAverage(indicatorId, category);
        }
    }

    /**
     * 获取去年同期值
     */
    private double getSamePeriodLastYear(Long indicatorId, String category, String yearMonth) {
        try {
            // 计算去年同期的月份
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            String lastYearMonth = (year - 1) + "-" + String.format("%02d", month);

            // 从数据库获取去年同期的数据
            BigDecimal amount = indicatorValuesDetailService.sumAmountByIndicatorIdAndCategoryAndPeriod(indicatorId,
                    category, lastYearMonth);
            if (amount != null && amount.doubleValue() > 0) {
                return amount.doubleValue();
            }

            // 如果去年没有数据，返回历史平均值作为备选
            return getHistoricalAverage(indicatorId, category);

        } catch (Exception e) {
            log.warn("获取去年同期值失败: indicatorId={}, category={}, yearMonth={}, error={}",
                    indicatorId, category, yearMonth, e.getMessage());
            return getHistoricalAverage(indicatorId, category);
        }
    }

    /**
     * 获取当年累计值
     */
    private double getCurrentYearCumulative(Long indicatorId, String category, String currentYearMonth,
            Map<String, Map<String, Double>> predictionCache) {
        try {
            // 计算当年1月到当前月份（包括预测值）的累计值
            String[] parts = currentYearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            double cumulative = 0.0;

            // 累加1月到当前月份的数据
            for (int m = 1; m <= month; m++) {
                String yearMonth = year + "-" + String.format("%02d", m);

                // 1. 优先从数据库获取真实数据
                BigDecimal amount = indicatorValuesDetailService
                        .sumAmountByIndicatorIdAndCategoryAndPeriod(indicatorId, category, yearMonth);
                if (amount != null && amount.doubleValue() > 0) {
                    cumulative += amount.doubleValue();
                    log.debug("累计计算使用真实数据: 分类={}, 月份={}, 金额={}", category, yearMonth, amount.doubleValue());
                } else {
                    // 2. 如果数据库没有真实数据，检查预测缓存
                    Map<String, Double> categoryCache = predictionCache.get(category);
                    if (categoryCache != null && categoryCache.containsKey(yearMonth)) {
                        cumulative += categoryCache.get(yearMonth);
                        log.debug("累计计算使用预测缓存: 分类={}, 月份={}, 金额={}", category, yearMonth, categoryCache.get(yearMonth));
                    } else {
                        // 3. 如果缓存也没有，尝试从数据库获取已保存的预测数据
                        try {
                            List<ExpenseForecast> savedForecasts = baseMapper.getCategoryForecastsByPeriod(indicatorId,
                                    yearMonth);
                            if (savedForecasts != null && !savedForecasts.isEmpty()) {
                                for (ExpenseForecast forecast : savedForecasts) {
                                    if (category.equals(forecast.getForecastType())) {
                                        cumulative += forecast.getForecastValue().doubleValue();
                                        log.debug("累计计算使用已保存预测数据: 分类={}, 月份={}, 金额={}", category, yearMonth,
                                                forecast.getForecastValue().doubleValue());
                                        break;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.debug("获取已保存预测数据失败: {}", e.getMessage());
                        }
                    }
                }
            }

            return cumulative;

        } catch (Exception e) {
            log.warn("获取当年累计值失败: indicatorId={}, category={}, currentYearMonth={}, error={}",
                    indicatorId, category, currentYearMonth, e.getMessage());
            // 使用历史平均值估算，假设是6个月
            return getHistoricalAverage(indicatorId, category) * 6;
        }
    }

    /**
     * 获取历史平均值
     */
    private double getHistoricalAverage(Long indicatorId, String category) {
        try {
            // 获取过去12个月的数据计算平均值
            String[] parts = LocalDateTime.now().toString().split("-");
            int currentYear = Integer.parseInt(parts[0]);
            int currentMonth = Integer.parseInt(parts[1]);

            double sum = 0.0;
            int validCount = 0;

            // 计算过去12个月的平均值
            for (int i = 1; i <= 12; i++) {
                int targetMonth = currentMonth - i;
                int targetYear = currentYear;

                while (targetMonth <= 0) {
                    targetMonth += 12;
                    targetYear--;
                }

                String yearMonth = targetYear + "-" + String.format("%02d", targetMonth);
                BigDecimal amount = indicatorValuesDetailService.sumAmountByIndicatorIdAndCategoryAndPeriod(indicatorId,
                        category, yearMonth);

                if (amount != null && amount.doubleValue() > 0) {
                    sum += amount.doubleValue();
                    validCount++;
                }
            }

            if (validCount > 0) {
                return sum / validCount;
            } else {
                // 如果没有历史数据，返回一个默认值
                return 10000.0;
            }

        } catch (Exception e) {
            log.warn("计算历史平均值失败: indicatorId={}, category={}, error={}",
                    indicatorId, category, e.getMessage());
            return 10000.0; // 返回默认值
        }
    }

    /**
     * 获取指定月份的天数
     */
    private int getDaysInMonth(int year, int month) {
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                return 30;
            case 2:
                return (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) ? 29 : 28;
            default:
                return 30;
        }
    }

    /**
     * 计算斜率（3个月趋势）
     */
    private double calculateSlope(double value1, double value2, double value3) {
        // 简单的线性回归斜率计算
        if (value1 == 0 && value2 == 0 && value3 == 0) {
            return 0.0;
        }

        // 计算3个月的平均变化率
        double change1 = value2 - value1;
        double change2 = value3 - value2;

        return (change1 + change2) / 2.0;
    }

    /**
     * TODO 修改为从indicator_annual_budget表中获取年预算
     * 获取年预算（2个办公费指标的年预算相加）
     */
    private double getAnnualBudget(Long indicatorId, int year) {
        try {
            // 办公费销售费用和管理费用的指标ID
            final long OFFICE_SALES_ID = 1952675507458174978L;
            final long OFFICE_MGMT_ID = 1952675507458175077L;

            double totalBudget = 0.0;

            // 如果是办公费相关指标，获取两个办公费指标的年预算
            if (Objects.equals(indicatorId, OFFICE_SALES_ID) || Objects.equals(indicatorId, OFFICE_MGMT_ID)) {
                // 获取销售办公费的年预算
                BigDecimal salesBudget = indicatorValuesDetailService.sumAmountByIndicatorIdAndCategoryAndPeriodRange(
                        OFFICE_SALES_ID, "销售办公费", year + "-01-01", year + "-12-31");
                if (salesBudget != null) {
                    totalBudget += salesBudget.doubleValue();
                }

                // 获取管理办公费的年预算
                BigDecimal mgmtBudget = indicatorValuesDetailService.sumAmountByIndicatorIdAndCategoryAndPeriodRange(
                        OFFICE_MGMT_ID, "管理办公费", year + "-01-01", year + "-12-31");
                if (mgmtBudget != null) {
                    totalBudget += mgmtBudget.doubleValue();
                }
            } else {
                // 其他指标，获取当前指标的年预算
                BigDecimal budget = indicatorValuesDetailService.sumAmountByIndicatorIdAndCategoryAndPeriodRange(
                        indicatorId, "预算", year + "-01-01", year + "-12-31");
                if (budget != null) {
                    totalBudget = budget.doubleValue();
                }
            }

            // 如果没有预算数据，使用默认值
            if (totalBudget <= 0) {
                totalBudget = 1940000; // 默认100万
                log.warn("未找到年预算数据，使用默认值: {}", totalBudget);
            }

            log.info("获取年预算: indicatorId={}, year={}, budget={}", indicatorId, year, totalBudget);
            return totalBudget;

        } catch (Exception e) {
            log.error("获取年预算失败: indicatorId={}, year={}, error={}", indicatorId, year, e.getMessage());
            return 1000000; // 返回默认值
        }
    }

    /**
     * 获取交易次数
     */
    private int getTransactionCount(Long indicatorId, String category, int monthsBack, String currentYearMonth,
            Map<String, Map<String, Double>> predictionCache) {
        try {
            // 计算目标月份
            String[] parts = currentYearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 往前推monthsBack个月
            int targetMonth = month + monthsBack;
            int targetYear = year;

            while (targetMonth <= 0) {
                targetMonth += 12;
                targetYear--;
            }
            while (targetMonth > 12) {
                targetMonth -= 12;
                targetYear++;
            }

            String targetYearMonth = targetYear + "-" + String.format("%02d", targetMonth);

            // 检查是否是预测月（在预测缓存中）
            Map<String, Double> categoryCache = predictionCache.get(category);
            if (categoryCache != null && categoryCache.containsKey(targetYearMonth)) {
                // 如果是预测月，返回默认值5
                log.debug("目标月份 {} 是预测月，使用默认交易次数: 5", targetYearMonth);
                return 5;
            }

            // 从数据库查询该月份该分类的交易次数
            List<IndicatorValuesDetailVO> details = indicatorValuesDetailService
                    .selectByIndicatorIdAndPeriod(indicatorId, targetYearMonth);
            int count = 0;
            for (IndicatorValuesDetailVO detail : details) {
                if (category.equals(detail.getCategory())) {
                    count++;
                }
            }

            log.debug("获取交易次数: indicatorId={}, category={}, yearMonth={}, count={}",
                    indicatorId, category, targetYearMonth, count);
            return count;

        } catch (Exception e) {
            log.warn("获取交易次数失败: indicatorId={}, category={}, monthsBack={}, error={}",
                    indicatorId, category, monthsBack, e.getMessage());
            return 5; // 返回默认值
        }
    }

    /**
     * 获取去年同期的交易次数
     */
    private int getSamePeriodLastYearTransactionCount(Long indicatorId, String category, String yearMonth) {
        try {
            // 计算去年同期的月份
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            String lastYearMonth = (year - 1) + "-" + String.format("%02d", month);

            // 从数据库查询去年同期的交易次数
            List<IndicatorValuesDetailVO> details = indicatorValuesDetailService
                    .selectByIndicatorIdAndPeriod(indicatorId, lastYearMonth);
            int count = 0;
            for (IndicatorValuesDetailVO detail : details) {
                if (category.equals(detail.getCategory())) {
                    count++;
                }
            }

            log.debug("获取去年同期交易次数: indicatorId={}, category={}, yearMonth={}, count={}",
                    indicatorId, category, lastYearMonth, count);
            return count;

        } catch (Exception e) {
            log.warn("获取去年同期交易次数失败: indicatorId={}, category={}, yearMonth={}, error={}",
                    indicatorId, category, yearMonth, e.getMessage());
            return 5; // 返回默认值
        }
    }

    @Override
    public BigDecimal getAnnualBudget(Long indicatorId, Integer year) {
        try {
            log.info("获取年度预算: indicatorId={}, year={}", indicatorId, year);

            // 从indicator_annual_budget表获取预算数据
            IndicatorAnnualBudgetEntity budgetEntity = indicatorAnnualBudgetService.findByIndicatorAndYear(indicatorId,
                    year);
            if (budgetEntity != null) {
                // 优先使用当前使用数，如果没有则使用年初预算数
                BigDecimal budget = budgetEntity.getCurrentUsed();
                if (budget == null || budget.compareTo(BigDecimal.ZERO) <= 0) {
                    budget = budgetEntity.getInitialBudget();
                }
                if (budget == null || budget.compareTo(BigDecimal.ZERO) <= 0) {
                    budget = budgetEntity.getMidyearBudget();
                }

                if (budget != null && budget.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("获取到年度预算: indicatorId={}, year={}, budget={}", indicatorId, year, budget);
                    return budget;
                }
            }

            // 如果没有找到预算数据，使用默认值
            BigDecimal defaultBudget = new BigDecimal("1940000");
            log.warn("未找到年度预算数据，使用默认值: indicatorId={}, year={}, defaultBudget={}",
                    indicatorId, year, defaultBudget);
            return defaultBudget;

        } catch (Exception e) {
            log.error("获取年度预算失败: indicatorId={}, year={}, error={}", indicatorId, year, e.getMessage(), e);
            return new BigDecimal("1940000"); // 返回默认值
        }
    }

    @Override
    public BigDecimal getCurrentCumulative(Long indicatorId, Integer year, Integer month) {
        try {
            log.info("获取当前累计值: indicatorId={}, year={}, month={}", indicatorId, year, month);

            BigDecimal cumulative = BigDecimal.ZERO;

            // 累加1月到当前月份的真实数据
            for (int m = 1; m <= month; m++) {
                String yearMonth = year + "-" + String.format("%02d", m);

                // 获取该月份的总金额（所有分类）
                BigDecimal monthlyAmount = indicatorValuesDetailService.sumAmountByIndicatorIdAndPeriod(indicatorId,
                        yearMonth);
                if (monthlyAmount != null && monthlyAmount.compareTo(BigDecimal.ZERO) > 0) {
                    cumulative = cumulative.add(monthlyAmount);
                    log.debug("累计计算使用真实数据: 月份={}, 金额={}", yearMonth, monthlyAmount);
                }
            }

            log.info("获取当前累计值完成: indicatorId={}, year={}, month={}, cumulative={}",
                    indicatorId, year, month, cumulative);
            return cumulative;

        } catch (Exception e) {
            log.error("获取当前累计值失败: indicatorId={}, year={}, month={}, error={}",
                    indicatorId, year, month, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getForecastCumulative(Long indicatorId, Integer year, Integer month) {
        try {
            log.info("获取预测累计值: indicatorId={}, year={}, month={}", indicatorId, year, month);

            // 先获取当前累计值（真实数据，到当月之前）
            BigDecimal currentCumulative = getCurrentCumulative(indicatorId, year, month - 1);
            BigDecimal forecastCumulative = currentCumulative;

            // 获取分类预测结果来计算从当月到目标月的预测值
            try {
                List<ExpenseForecast> categoryForecasts = getLatestCategoryForecasts(indicatorId);
                if (categoryForecasts != null && !categoryForecasts.isEmpty()) {
                    log.info("找到分类预测数据，共{}条记录", categoryForecasts.size());

                    // 按月份分组预测数据
                    Map<String, BigDecimal> monthlyForecasts = new HashMap<>();
                    for (ExpenseForecast forecast : categoryForecasts) {
                        String forecastMonth = forecast.getForecastPeriod();
                        if (forecastMonth != null) {
                            BigDecimal existingAmount = monthlyForecasts.getOrDefault(forecastMonth, BigDecimal.ZERO);
                            monthlyForecasts.put(forecastMonth, existingAmount.add(forecast.getForecastValue()));
                            log.debug("添加预测数据: 月份={}, 类型={}, 金额={}",
                                    forecastMonth, forecast.getForecastType(), forecast.getForecastValue());
                        }
                    }

                    log.info("按月份分组的预测数据: {}", monthlyForecasts);

                    // 累加从当月到目标月的预测值
                    for (int targetMonth = month; targetMonth <= 12; targetMonth++) {
                        String targetMonthStr = String.format("%d-%02d", year, targetMonth);
                        BigDecimal monthForecast = monthlyForecasts.get(targetMonthStr);

                        if (monthForecast != null && monthForecast.compareTo(BigDecimal.ZERO) > 0) {
                            forecastCumulative = forecastCumulative.add(monthForecast);
                            log.info("累加预测值: 月份={}, 金额={}, 累计={}",
                                    targetMonthStr, monthForecast, forecastCumulative);
                        } else {
                            log.warn("未找到月份{}的预测数据", targetMonthStr);
                        }
                    }
                } else {
                    log.warn("未找到任何分类预测数据");
                }
            } catch (Exception e) {
                log.error("获取分类预测数据失败: error={}", e.getMessage(), e);
            }

            log.info("获取预测累计值完成: indicatorId={}, year={}, month={}, currentCumulative={}, forecastCumulative={}",
                    indicatorId, year, month, currentCumulative, forecastCumulative);
            return forecastCumulative;

        } catch (Exception e) {
            log.error("获取预测累计值失败: indicatorId={}, year={}, month={}, error={}",
                    indicatorId, year, month, e.getMessage(), e);
            return getCurrentCumulative(indicatorId, year, month); // 返回当前累计值作为备选
        }
    }

    @Override
    public ExpenseForecast getTotalForecastByPeriod(Long indicatorId, String period) {
        try {
            log.info("获取指定期间的总预测值: indicatorId={}, period={}", indicatorId, period);
            return baseMapper.getTotalForecastByPeriod(indicatorId, period);
        } catch (Exception e) {
            log.error("获取指定期间的总预测值失败: indicatorId={}, period={}, error={}",
                    indicatorId, period, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<ExpenseForecast> getCategoryForecastsByPeriodFromMapper(Long indicatorId, String period) {
        try {
            log.info("获取指定期间的分类预测值: indicatorId={}, period={}", indicatorId, period);
            return baseMapper.getCategoryForecastsByPeriod(indicatorId, period);
        } catch (Exception e) {
            log.error("获取指定期间的分类预测值失败: indicatorId={}, period={}, error={}",
                    indicatorId, period, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public BigDecimal getMonthlyForecastValue(Long indicatorId, Integer year, Integer month) {
        try {
            log.info("获取指定月份的预测值: indicatorId={}, year={}, month={}", indicatorId, year, month);

            String period = String.format("%d-%02d", year, month);

            // 直接获取该月 forecastType = "TOTAL" 的最新记录
            ExpenseForecast totalForecast = baseMapper.getTotalForecastByPeriod(indicatorId, period);

            if (totalForecast != null) {
                log.info("获取月份预测值完成: indicatorId={}, year={}, month={}, totalForecast={}",
                        indicatorId, year, month, totalForecast.getForecastValue());
                return totalForecast.getForecastValue();
            } else {
                log.warn("未找到指定月份的TOTAL预测数据: indicatorId={}, period={}", indicatorId, period);
                return BigDecimal.ZERO;
            }

        } catch (Exception e) {
            log.error("获取指定月份的预测值失败: indicatorId={}, year={}, month={}, error={}",
                    indicatorId, year, month, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getMonthlyForecastValueByCategory(Long indicatorId, Integer year, Integer month,
            String category) {
        try {
            log.info("获取指定月份的分类预测值: indicatorId={}, year={}, month={}, category={}", indicatorId, year, month,
                    category);
            String period = String.format("%d-%02d", year, month);
            // 由于baseMapper没有getCategoryForecastByPeriod方法，这里需要手动实现或调用正确的方法
            // 直接从 mapper 获取该月指定分类的预测值（若无此接口，退回0）
            ExpenseForecast forecast = baseMapper.getCategoryForecastByPeriod(indicatorId, period, category);
            if (forecast != null && forecast.getForecastValue() != null) {
                return forecast.getForecastValue();
            }
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取指定月份的分类预测值失败: indicatorId={}, year={}, month={}, category={}, error={}", indicatorId, year,
                    month, category, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Map<String, Object> getCategoryMonthlyActuals(Long indicatorId, Integer year, Integer month,
            List<String> categories) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (categories == null || categories.isEmpty()) {
                return result;
            }
            for (String category : categories) {
                List<BigDecimal> cumulative = new ArrayList<>();
                BigDecimal run = BigDecimal.ZERO;
                for (int m = 1; m <= 12; m++) {
                    if (m <= month) {
                        String period = String.format("%d-%02d", year, m);
                        BigDecimal monthly = indicatorValuesDetailService
                                .sumAmountByIndicatorIdAndCategoryAndPeriod(indicatorId, category, period);
                        if (monthly == null)
                            monthly = BigDecimal.ZERO;
                        run = run.add(monthly);
                        cumulative.add(run);
                    } else {
                        cumulative.add(null);
                    }
                }
                result.put(category, cumulative);
            }
            return result;
        } catch (Exception e) {
            log.error("获取分类实际累计失败: indicatorId={}, year={}, month={}, error={}", indicatorId, year, month,
                    e.getMessage(), e);
            return result;
        }
    }

    /**
     * 计算调整后的预测值
     * 根据预测线的角度和累计值来调整系数，确保预测值沿着合理的趋势线发展
     */
    private BigDecimal calculateAdjustedForecastValue(BigDecimal originalForecast, Long indicatorId,
            Integer year, Integer month, String category) {
        try {
            log.info("开始计算调整后的预测值: 原始值={}, 指标ID={}, 年份={}, 月份={}, 分类={}",
                    originalForecast, indicatorId, year, month, category);

            // 获取年度预算
            BigDecimal annualBudget = getAnnualBudget(indicatorId, year);
            BigDecimal monthlyBudget = annualBudget.divide(new BigDecimal("12"), 2, java.math.RoundingMode.HALF_UP);

            // 计算预算线的角度（每月预算值）
            BigDecimal budgetSlope = monthlyBudget;

            // 计算预测线的角度（基于历史数据）
            BigDecimal forecastSlope = calculateForecastSlope(indicatorId, year, month);

            // 计算当前累计预测值
            BigDecimal currentForecastCumulative = calculateCurrentForecastCumulative(indicatorId, year, month);

            // 计算预算累计值（到当前月）
            BigDecimal budgetCumulative = monthlyBudget.multiply(new BigDecimal(month));

            // 计算系数
            BigDecimal coefficient = BigDecimal.ONE;

            // 根据累计预测值与预算累计值的比较来调整系数，实现预算线上下波动
            if (currentForecastCumulative.compareTo(budgetCumulative) != 0) {
                // 计算累计预测值与预算累计值的比例
                BigDecimal ratio = currentForecastCumulative.divide(budgetCumulative, 4,
                        java.math.RoundingMode.HALF_UP);

                if (currentForecastCumulative.compareTo(budgetCumulative) < 0) {
                    // 累计预测值低于预算累计值：增加系数，因为还差得远，需要加大力度
                    BigDecimal deviation = BigDecimal.ONE.subtract(ratio);
                    BigDecimal increaseFactor;

                    // 根据偏差程度确定增加系数
                    if (deviation.compareTo(new BigDecimal("0.7")) >= 0) {
                        // 缺失超过70% → 增加100%
                        increaseFactor = new BigDecimal("1.8");
                    } else if (deviation.compareTo(new BigDecimal("0.5")) >= 0) {
                        // 缺失50% → 增加120%
                        increaseFactor = new BigDecimal("1.2");
                    } else if (deviation.compareTo(new BigDecimal("0.3")) >= 0) {
                        // 缺失30% → 增加70%
                        increaseFactor = new BigDecimal("0.7");
                    } else if (deviation.compareTo(new BigDecimal("0.1")) >= 0) {
                        // 缺失10% → 增加50%
                        increaseFactor = new BigDecimal("0.5");
                    } else {
                        // 缺失小于10% → 增加20%
                        increaseFactor = new BigDecimal("0.2");
                    }

                    coefficient = coefficient.add(increaseFactor);
                    log.info("累计预测值低于预算，增加系数: 预测累计={}, 预算累计={}, 比例={}, 偏差={}, 增加系数={}, 最终系数={}",
                            currentForecastCumulative, budgetCumulative, ratio, deviation, increaseFactor, coefficient);
                } else {
                    // 累计预测值高于预算累计值：减少系数，因为已经超了，需要控制
                    BigDecimal deviation = ratio.subtract(BigDecimal.ONE);
                    // 超得越多，减得越狠，再乘以3
                    BigDecimal reductionFactor;

                    if (deviation.compareTo(new BigDecimal("0.1")) <= 0) {
                        // 超出10%以内：轻微减少
                        reductionFactor = deviation.multiply(new BigDecimal("1.8")); // 0.6 * 3
                    } else if (deviation.compareTo(new BigDecimal("0.3")) <= 0) {
                        // 超出10%-30%：中等减少
                        reductionFactor = new BigDecimal("0.18") // 0.06 * 3
                                .add(deviation.subtract(new BigDecimal("0.1")).multiply(new BigDecimal("1.2"))); // 0.4
                                                                                                                 // * 3
                    } else {
                        // 超出30%以上：大幅减少
                        reductionFactor = new BigDecimal("0.42") // 0.14 * 3
                                .add(deviation.subtract(new BigDecimal("0.3")).multiply(new BigDecimal("1.8"))); // 0.6
                                                                                                                 // * 3
                    }

                    coefficient = coefficient.subtract(reductionFactor);
                    log.info("累计预测值高于预算，减少系数: 预测累计={}, 预算累计={}, 比例={}, 偏差={}, 减少系数={}, 最终系数={}",
                            currentForecastCumulative, budgetCumulative, ratio, deviation, reductionFactor,
                            coefficient);
                }
            }

            // 限制系数范围在0.5到1.5之间（允许更大的调整幅度）
            coefficient = coefficient.max(new BigDecimal("0.2")).min(new BigDecimal("1.9"));

            // 计算调整后的预测值
            BigDecimal adjustedForecast = originalForecast.multiply(coefficient);

            log.info("预测值调整完成: 原始值={}, 系数={}, 调整后值={}",
                    originalForecast, coefficient, adjustedForecast);

            return adjustedForecast;

        } catch (Exception e) {
            log.error("计算调整后的预测值失败: {}", e.getMessage(), e);
            return originalForecast; // 如果计算失败，返回原始值
        }
    }

    /**
     * 计算预测线的角度（基于历史数据）
     */
    private BigDecimal calculateForecastSlope(Long indicatorId, Integer year, Integer month) {
        try {
            // 获取最近3个月的历史数据来计算趋势
            BigDecimal totalAmount = BigDecimal.ZERO;
            int validMonths = 0;

            for (int i = 3; i >= 1; i--) {
                int targetMonth = month - i;
                if (targetMonth > 0) {
                    BigDecimal monthlyAmount = indicatorValuesDetailService.sumAmountByIndicatorIdAndPeriod(
                            indicatorId, String.format("%d-%02d", year, targetMonth));
                    if (monthlyAmount != null && monthlyAmount.compareTo(BigDecimal.ZERO) > 0) {
                        totalAmount = totalAmount.add(monthlyAmount);
                        validMonths++;
                    }
                }
            }

            if (validMonths > 0) {
                return totalAmount.divide(new BigDecimal(validMonths), 2, java.math.RoundingMode.HALF_UP);
            } else {
                // 如果没有历史数据，使用年度预算的1/12作为默认值
                BigDecimal annualBudget = getAnnualBudget(indicatorId, year);
                return annualBudget.divide(new BigDecimal("12"), 2, java.math.RoundingMode.HALF_UP);
            }
        } catch (Exception e) {
            log.error("计算预测线角度失败: {}", e.getMessage(), e);
            BigDecimal annualBudget = getAnnualBudget(indicatorId, year);
            return annualBudget.divide(new BigDecimal("12"), 2, java.math.RoundingMode.HALF_UP);
        }
    }

    /**
     * 计算当前累计预测值
     */
    private BigDecimal calculateCurrentForecastCumulative(Long indicatorId, Integer year, Integer month) {
        try {
            BigDecimal cumulative = BigDecimal.ZERO;

            // 累加从1月到当前月的预测值
            for (int m = 1; m <= month; m++) {
                String period = String.format("%d-%02d", year, m);

                // 直接获取该月 forecastType = "TOTAL" 的最新记录
                ExpenseForecast totalForecast = baseMapper.getTotalForecastByPeriod(indicatorId, period);

                if (totalForecast != null) {
                    cumulative = cumulative.add(totalForecast.getForecastValue());
                    log.debug("累计预测值计算: 月份={}, 预测值={}, 累计={}", period, totalForecast.getForecastValue(), cumulative);
                }
            }

            return cumulative;
        } catch (Exception e) {
            log.error("计算当前累计预测值失败: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
}
