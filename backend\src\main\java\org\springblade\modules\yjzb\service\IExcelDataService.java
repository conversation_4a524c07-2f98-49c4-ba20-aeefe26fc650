/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Excel数据解析服务接口
 *
 * <AUTHOR> Assistant
 */
public interface IExcelDataService {

    /**
     * 解析表样.xlsx文件，提取所有表格数据
     *
     * @param file Excel文件
     * @return 解析结果，包含各个表格的数据
     */
    Map<String, Object> parseExcelData(MultipartFile file);

    /**
     * 从Excel数据中获取主要经济指标数据
     *
     * @param excelData Excel解析数据
     * @param params 查询参数
     * @return 主要经济指标数据
     */
    List<Map<String, Object>> getMainEconomicIndicatorsFromExcel(Map<String, Object> excelData, Map<String, Object> params);

    /**
     * 从Excel数据中获取三项费用数据
     *
     * @param excelData Excel解析数据
     * @param params 查询参数
     * @return 三项费用数据
     */
    List<Map<String, Object>> getThreeExpensesFromExcel(Map<String, Object> excelData, Map<String, Object> params);

    /**
     * 从Excel数据中获取重点费用数据
     *
     * @param excelData Excel解析数据
     * @param params 查询参数
     * @return 重点费用数据
     */
    List<Map<String, Object>> getKeyExpensesFromExcel(Map<String, Object> excelData, Map<String, Object> params);

    /**
     * 从Excel数据中获取仅三项费用数据
     *
     * @param excelData Excel解析数据
     * @param params 查询参数
     * @return 仅三项费用数据
     */
    List<Map<String, Object>> getOnlyThreeExpensesFromExcel(Map<String, Object> excelData, Map<String, Object> params);

    /**
     * 从Excel数据中获取资本表数据
     *
     * @param excelData Excel解析数据
     * @param params 查询参数
     * @return 资本表数据
     */
    List<Map<String, Object>> getCapitalFromExcel(Map<String, Object> excelData, Map<String, Object> params);

    /**
     * 缓存Excel数据
     *
     * @param key 缓存键
     * @param data 数据
     */
    void cacheExcelData(String key, Map<String, Object> data);

    /**
     * 获取缓存的Excel数据
     *
     * @param key 缓存键
     * @return 缓存的数据
     */
    Map<String, Object> getCachedExcelData(String key);

    /**
     * 清除缓存的Excel数据
     *
     * @param key 缓存键
     */
    void clearCachedExcelData(String key);
}
