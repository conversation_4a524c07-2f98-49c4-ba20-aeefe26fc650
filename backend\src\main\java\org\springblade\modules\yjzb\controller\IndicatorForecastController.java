package org.springblade.modules.yjzb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.excel.IndicatorForecastExcel;
import org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorForecastVO;
import org.springblade.modules.yjzb.service.IIndicatorForecastService;
import org.springblade.modules.yjzb.wrapper.IndicatorForecastWrapper;
import org.springblade.modules.yjzb.pojo.dto.IndicatorPredictFeatureDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicatorForecast")
@Tag(name = "指标预测结果", description = "指标预测结果接口")
public class IndicatorForecastController extends BladeController {

    private final IIndicatorForecastService indicatorForecastService;

    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicatorForecast")
    public R<IndicatorForecastVO> detail(IndicatorForecastEntity indicatorForecast) {
        IndicatorForecastEntity detail = indicatorForecastService.getOne(Condition.getQueryWrapper(indicatorForecast));
        return R.data(IndicatorForecastWrapper.build().entityVO(detail));
    }

    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicatorForecast")
    public R<IPage<IndicatorForecastVO>> list(
            @Parameter(hidden = true) @RequestParam Map<String, Object> indicatorForecast,
            Query query) {
        IPage<IndicatorForecastEntity> pages = indicatorForecastService.page(Condition.getPage(query),
                Condition.getQueryWrapper(indicatorForecast, IndicatorForecastEntity.class));
        return R.data(IndicatorForecastWrapper.build().pageVO(pages));
    }

    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入indicatorForecast")
    public R<IPage<IndicatorForecastVO>> page(IndicatorForecastVO indicatorForecast, Query query) {
        IPage<IndicatorForecastVO> pages = indicatorForecastService.selectIndicatorForecastPage(
                Condition.getPage(query),
                indicatorForecast);
        return R.data(pages);
    }

    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入indicatorForecast")
    public R<Boolean> save(@Valid @RequestBody IndicatorForecastEntity indicatorForecast) {
        return R.status(indicatorForecastService.save(indicatorForecast));
    }

    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入indicatorForecast")
    public R<Boolean> update(@Valid @RequestBody IndicatorForecastEntity indicatorForecast) {
        return R.status(indicatorForecastService.updateById(indicatorForecast));
    }

    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入indicatorForecast")
    public R<Boolean> submit(@Valid @RequestBody IndicatorForecastEntity indicatorForecast) {
        return R.status(indicatorForecastService.saveOrUpdate(indicatorForecast));
    }

    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R<Boolean> remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(indicatorForecastService.deleteLogic(Func.toLongList(ids)));
    }

    @GetMapping("/getByIndicatorAndYear")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "按指标与年份获取预测结果", description = "参数：indicatorId, year")
    public R<IndicatorForecastVO> getByIndicatorAndYear(@RequestParam Long indicatorId, @RequestParam Integer year) {
        IndicatorForecastEntity entity = indicatorForecastService.findByIndicatorAndYear(indicatorId, year);
        if (entity == null) {
            return R.data(null);
        }
        return R.data(IndicatorForecastWrapper.build().entityVO(entity));
    }

    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    @GetMapping("/export-indicatorForecast")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "导出数据", description = "传入indicatorForecast")
    public void exportIndicatorForecast(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorForecast,
            BladeUser bladeUser, HttpServletResponse response) {
        QueryWrapper<IndicatorForecastEntity> queryWrapper = Condition.getQueryWrapper(indicatorForecast,
                IndicatorForecastEntity.class);
        List<IndicatorForecastExcel> list = indicatorForecastService.exportIndicatorForecast(queryWrapper);
        ExcelUtil.export(response, "指标预测结果数据" + DateUtil.time(), "指标预测结果数据表", list, IndicatorForecastExcel.class);
    }

    /**
     * 基于宽表字段的通用预测接口：直接传入与 费用指标_宽表.csv 对应的字段，返回预测值。
     */
    @PostMapping("/predictByFeatures")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "按宽表字段预测", description = "传入 indicatorId/period/current_value/lag1/lag2/lag3/ma3/去年同期/当年累计/预算")
    public R<java.math.BigDecimal> predictByFeatures(@Valid @RequestBody IndicatorPredictFeatureDTO feature) {
        return R.data(indicatorForecastService.predictMonthlyValue(feature));
    }

    /**
     * 测试接口：传入indicatorId、year、month，直接委托Service从数据库组装特征并预测。
     */
    @GetMapping("/predict/test")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "测试：传入年月与指标ID，返回预测值", description = "示例：indicatorId=1001, year=2024, month=10")
    public R<java.math.BigDecimal> testPredict(@RequestParam Long indicatorId,
            @RequestParam Integer year,
            @RequestParam Integer month) {
        String period = year + "-" + String.format("%02d", month);
        return R.data(indicatorForecastService.predictByIndicatorAndPeriod(indicatorId, period));
    }

    @PostMapping("/predict/yearly")
    @ApiOperationSupport(order = 12)
    @Operation(summary = "按指标预测当年2-12月并入库", description = "传入indicatorId，服务层会逐月递推并将结果写入预测结果表")
    public R<IndicatorForecastVO> predictYearly(@RequestParam Long indicatorId) {
        var entity = indicatorForecastService.predictCurrentYearForIndicator(indicatorId);
        return R.data(IndicatorForecastWrapper.build().entityVO(entity));
    }

    /**
     * 新：按指标ID+期间(YYYY-MM)触发预测并落库（仅该月），返回预测值
     */
    @PostMapping("/predict/monthly")
    @ApiOperationSupport(order = 13)
    @Operation(summary = "按指标与月份预测并入库", description = "参数：indicatorId, period(YYYY-MM)")
    public R<java.math.BigDecimal> predictMonthly(@RequestParam Long indicatorId, @RequestParam String period) {
        java.math.BigDecimal v = indicatorForecastService.predictByIndicatorAndPeriod(indicatorId, period);
        indicatorForecastService.upsertMonthlyForecast(indicatorId, period, v);
        return R.data(v);
    }
}
