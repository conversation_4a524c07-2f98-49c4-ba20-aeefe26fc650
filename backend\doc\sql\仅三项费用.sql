WITH base_data AS (
    SELECT
        i.id,
        i.code,
        i.name AS indicator_name,
        EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) AS year,
        EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) AS month,
        iv.value
    FROM yjzb_indicator i
    JOIN yjzb_indicator_values iv ON i.id = iv.indicator_id
    WHERE EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) IN (2024, 2025)
      AND EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) BETWEEN 1 AND 5
),

-- 所有费用项目
all_fees AS (
    SELECT
        indicator_name AS item_name,
        year,
        SUM(value) AS value
    FROM base_data
    WHERE indicator_name in ('财务费用', '销售费用', '管理费用', '研发费用')
    GROUP BY year, indicator_name
),

-- 透视数据
pivot_data AS (
    SELECT
        item_name,
        MAX(CASE WHEN year = 2025 THEN value END) AS v_2025_actual,
        MAX(CASE WHEN year = 2024 THEN value END) AS v_2024_actual
    FROM all_fees
    GROUP BY item_name
),

-- 获取预算数据 - 从年度预算表获取
budget_data AS (
    SELECT
        i.name AS item_name,
        SUM(COALESCE(iab.current_used, 0)) AS budget_value
    FROM yjzb_indicator i
    LEFT JOIN yjzb_indicator_annual_budget iab ON i.id = iab.indicator_id
        AND iab.year = 2025
        AND iab.is_deleted = 0
    WHERE i.is_deleted = 0
      AND i.name in ('财务费用', '销售费用', '管理费用', '研发费用')
    GROUP BY
        i.name
),

-- 合并实际值和预算值
final_data AS (
    SELECT
        p.item_name,
        p.v_2025_actual,
        p.v_2024_actual,
        COALESCE(p.v_2025_actual, 0) - COALESCE(p.v_2024_actual, 0) AS diff_amount,
        CASE
            WHEN COALESCE(p.v_2024_actual, 0) = 0 THEN NULL
            ELSE (COALESCE(p.v_2025_actual, 0) - COALESCE(p.v_2024_actual, 0)) / COALESCE(p.v_2024_actual, 0) * 100
        END AS diff_percent,
        COALESCE(b.budget_value, 0) AS budget_value,
        COALESCE(b.budget_value, 0) - COALESCE(p.v_2025_actual, 0) AS budget_balance,
        CASE
            WHEN COALESCE(b.budget_value, 0) = 0 THEN NULL
            ELSE COALESCE(p.v_2025_actual, 0) / COALESCE(b.budget_value, 0) * 100
        END AS budget_execution_rate
    FROM pivot_data p
    LEFT JOIN budget_data b ON p.item_name = b.item_name
),

-- 计算合计行
total_row AS (
    SELECT
        '合计' AS item_name,
        SUM(v_2025_actual) AS v_2025_actual,
        SUM(v_2024_actual) AS v_2024_actual,
        SUM(diff_amount) AS diff_amount,
        CASE
            WHEN SUM(v_2024_actual) = 0 THEN NULL
            ELSE SUM(diff_amount) / SUM(v_2024_actual) * 100
        END AS diff_percent,
        SUM(budget_value) AS budget_value,
        SUM(budget_balance) AS budget_balance,
        CASE
            WHEN SUM(budget_value) = 0 THEN NULL
            ELSE SUM(v_2025_actual) / SUM(budget_value) * 100
        END AS budget_execution_rate
    FROM final_data
),

-- 合并所有数据
complete_data AS (
    SELECT * FROM total_row
    UNION ALL
    SELECT * FROM final_data
)

-- 最终输出
SELECT
    item_name AS "预算项目",
    CASE
        WHEN item_name = '合计' THEN 2
        ELSE ROW_NUMBER() OVER (ORDER BY diff_amount DESC) + 2
    END AS "行次",
    TO_CHAR(ROUND(v_2025_actual / 10000, 2), 'FM999,999,990.00') AS "2025年1-5月",
    TO_CHAR(ROUND(v_2024_actual / 10000, 2), 'FM999,999,990.00') AS "2024年1-5月",
    TO_CHAR(ROUND(diff_amount / 10000, 2), 'FM999,999,990.00') AS "增减额",
    CASE
        WHEN diff_percent IS NULL THEN ''
        ELSE TO_CHAR(ROUND(diff_percent, 2), 'FM9990.00')
    END AS "增减比率%",
    TO_CHAR(ROUND(budget_value / 10000, 2), 'FM999,999,990.00') AS "2025年预算数",
    TO_CHAR(ROUND(budget_balance / 10000, 2), 'FM999,999,990.00') AS "预算余额",
    CASE
        WHEN budget_execution_rate IS NULL THEN ''
        ELSE TO_CHAR(ROUND(budget_execution_rate, 2), 'FM9990.00')
    END AS "预算执行率%"
FROM complete_data
ORDER BY
    CASE WHEN item_name = '合计' THEN 0 ELSE 1 END,
    diff_amount DESC,
    diff_percent DESC;