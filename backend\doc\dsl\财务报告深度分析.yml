app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 财务报告深度分析
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tongyi:0.0.30@c8acdf4467408345fc51d3d9e45484614295b9f7c4d20ff0ce03940368bf286a
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1756083788401-source-1756084012189-target
      source: '1756083788401'
      sourceHandle: source
      target: '1756084012189'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1756082617037-source-1756084023442-target
      source: '1756082617037'
      sourceHandle: source
      target: '1756084023442'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: if-else
      id: 1756082612002-source-1756435401071-target
      source: '1756082612002'
      sourceHandle: source
      target: '1756435401071'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1756435401071-true-1756082617037-target
      source: '1756435401071'
      sourceHandle: 'true'
      target: '1756082617037'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1756435401071-29785211-9040-494e-b613-e2708e7d839e-1756083788401-target
      source: '1756435401071'
      sourceHandle: 29785211-9040-494e-b613-e2708e7d839e
      target: '1756083788401'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1756435401071-70b19052-65de-405b-adf6-16954171d924-1756507918427-target
      source: '1756435401071'
      sourceHandle: 70b19052-65de-405b-adf6-16954171d924
      target: '1756507918427'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1756507918427-source-1756508077226-target
      source: '1756507918427'
      sourceHandle: source
      target: '1756508077226'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1756508020583-source-1756508083047-target
      source: '1756508020583'
      sourceHandle: source
      target: '1756508083047'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1756435401071-3f0013e7-805a-45f0-8169-58df1fcd1a52-1756508020583-target
      source: '1756435401071'
      sourceHandle: 3f0013e7-805a-45f0-8169-58df1fcd1a52
      target: '1756508020583'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1756435401071-false-1758770061491-target
      source: '1756435401071'
      sourceHandle: 'false'
      target: '1758770061491'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1758770061491-source-1758770085829-target
      source: '1758770061491'
      sourceHandle: source
      target: '1758770085829'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: dataList
          max_length: 50000
          options: []
          required: true
          type: paragraph
          variable: dataList
        - label: analysisType
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: analysisType
        - label: calculatedValues
          max_length: 5000
          options: []
          required: true
          type: paragraph
          variable: calculatedValues
      height: 142
      id: '1756082612002'
      position:
        x: 30
        y: 311
      positionAbsolute:
        x: 30
        y: 311
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 实现税利情况分析
        model:
          completion_params: {}
          mode: chat
          name: qwen3-32b
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: fd888800-d24f-455a-b22b-6336b4cd798c
          role: system
          text: '<主要经济指标>

            单位：箱、万元

            {{#1756082612002.dataList#}}

            </主要经济指标>

            <计算值>

            {{#1756082612002.calculatedValues#}}

            </计算值>'
        - id: d7ee89f0-eb28-4d53-866a-40a8905b3619
          role: user
          text: '基于<主要经济指标>和<计算值>，生成<result>内容（含<result>），如下：

            <result>

            [当前时期]，阳江市市烟草系统实现税利[税利总额本年值]万元，同比增加[税利同比增加额]万元，增长了[税利同比增幅]%，其中：实现利润总额[利润总额本年值]万元，同比增长[利润总额同比增幅]%；应交税金（不含企业所得税）合计[税费本年值]万元，同比增长[税费同比增幅]%。

            我公司全年税利预算数为[全年税利预算数]万元，截至[当前时期末]月[当前时期末最后一天]日，实际执行进度为[税利预算执行进度]%，比时间进度[时间进度要求]%超出[进度差额]个百分点。

            </result>


            要求：

            1.百分比保留两位小数，单位统一为“万元”。

            2.自动计算并填入“税利同比增加额”和“进度差额”。

            3.如果计算到数值为减少，请改为减少描述。

            4.保持语句通顺，符合财务分析报告的专业风格。

            5.[xxx]为待替换值，要将[]去掉。'
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: '1756082617037'
      position:
        x: 638
        y: 311
      positionAbsolute:
        x: 638
        y: 311
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 卷烟经营情况分析
        model:
          completion_params: {}
          mode: chat
          name: qwen3-32b
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: c8860db6-74a1-48dc-a648-3d013cfcda36
          role: system
          text: '<主要经济指标>

            单位：箱、万元

            {{#1756082612002.dataList#}}

            </主要经济指标>

            <计算值>

            {{#1756082612002.calculatedValues#}}

            </计算值>'
        - id: d60ae032-c808-4adc-99ea-87529614cbc1
          role: user
          text: "根据<主要经济指标>和<计算值>，生成<result>内容（含<result>），如下：\n<result>\n（一）卷烟销售收入和卷烟销量\n\
            [当前时期]，阳江市烟草系统实现卷烟销售收入（不含税）[卷烟销售收入本年值]万元，同比增加[卷烟销售收入同比增加额]万元，增长[卷烟销售收入同比增幅]%。累计实现卷烟销量[卷烟销售数量本年值]箱，同比增加[卷烟销售数量同比增加额]箱，增长[卷烟销售数量同比增幅]%。\n\
            （二）卷烟结构\n[当前时期]，阳江市烟草系统单箱收入（含税）[单箱含税收入计算值]元/箱，同比提高[单箱收入同比增加额]元/箱，增长[单箱收入同比增幅]%，比公司年度预算目标[年度单箱收入预算值]元/箱高[单箱收入超预算额]元/箱。\n\
            （三）卷烟销售毛利和毛利率\n[当前时期]，阳江市烟草系统实现卷烟销售毛利[毛利额本年值]万元，同比增加[毛利额同比增加额]万元，增长[毛利额同比增幅]%。实现平均卷烟毛利率[毛利率本年值]%，同比[毛利率同比变化方向][毛利率同比变化绝对值]个百分点，[与预算比较情况描述]。\n\
            </result>\n\n要求：\n1. 百分比保留两位小数，单位统一为“万元”。\n2. 销量单位为“箱”\n3. 百分比和百分点变化需准确区分表述\n\
            4. 同比变化需计算绝对值和相对百分比\n5. 与预算比较需明确“超过”或“低于”预算的幅度\n6. 如果计算到数值为减少，请改为减少描述。\n\
            7. 保持语句通顺，符合财务分析报告的专业风格。\n8. [xxx]为待替换值，要将[]去掉。\n9.不要markdown格式\n\n\n\
            填充说明（请根据实际数据替换方括号内内容）：\n\t[卷烟销售收入本年值]：填入卷烟销售收入实际值\n\t[卷烟销售收入同比增加额]：填入同比增加金额（自动计算：本年值\
            \ - 上年值）\n\t[卷烟销售收入同比增幅]：填入同比增幅百分比\n\t[卷烟销售数量本年值]：填入卷烟销售数量实际值\n\t[卷烟销售数量同比增加额]：填入同比增加箱数（自动计算）\n\
            \t[卷烟销售数量同比增幅]：填入同比增幅百分比\n\t[单箱含税收入计算值]：计算填入（卷烟销售收入本年值 × 1.13 × 10000\
            \ ÷ 卷烟销售数量本年值，假设增值税率13%）\n\t[单箱收入同比增加额]：计算填入（本年单箱收入 - 上年单箱收入）\n\t[单箱收入同比增幅]：计算填入（单箱收入同比增加额\
            \ ÷ 上年单箱收入 × 100%）\n\t[年度单箱收入预算值]：填入年度预算目标值\n\t[单箱收入超预算额]：计算填入（本年单箱收入\
            \ - 年度预算目标值）\n\t[毛利额本年值]：填入毛利额实际值\n\t[毛利额同比增加额]：计算填入（本年毛利额 - 上年毛利额）\n\t\
            [毛利额同比增幅]：填入同比增幅百分比\n\t[毛利率本年值]：填入毛利率实际值\n\t[毛利率同比变化方向]：根据变化选择“提高”或“下降”\n\
            \t[毛利率同比变化绝对值]：计算填入（本年毛利率 - 上年毛利率的绝对值）\n\t[与预算比较情况描述]：根据与预算比较结果填写（如：“超过毛利率预算数[毛利率预算值]%”或“低于预算[低预算幅度]个百分点”）\n"
        selected: false
        title: LLM 2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: '1756083788401'
      position:
        x: 638
        y: 469
      positionAbsolute:
        x: 638
        y: 469
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1756083788401'
          - text
          variable: text
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1756084012189'
      position:
        x: 942
        y: 469
      positionAbsolute:
        x: 942
        y: 469
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1756082617037'
          - text
          variable: text
        selected: false
        title: 结束 2
        type: end
      height: 90
      id: '1756084023442'
      position:
        x: 942
        y: 311
      positionAbsolute:
        x: 942
        y: 311
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: c8defdd3-d12c-4176-bb12-f92741340f7f
            value: 实现税利情况分析
            varType: string
            variable_selector:
            - '1756082612002'
            - analysisType
          id: 'true'
          logical_operator: and
        - case_id: 29785211-9040-494e-b613-e2708e7d839e
          conditions:
          - comparison_operator: contains
            id: 99666b51-46d7-4f07-a390-3090783a03d9
            value: 卷烟经营情况分析
            varType: string
            variable_selector:
            - '1756082612002'
            - analysisType
          id: 29785211-9040-494e-b613-e2708e7d839e
          logical_operator: and
        - case_id: 70b19052-65de-405b-adf6-16954171d924
          conditions:
          - comparison_operator: contains
            id: 6c58a00b-c100-4e9e-aac3-c19164d1eefa
            value: 三项费用支出总体情况分析
            varType: string
            variable_selector:
            - '1756082612002'
            - analysisType
          id: 70b19052-65de-405b-adf6-16954171d924
          logical_operator: and
        - case_id: 3f0013e7-805a-45f0-8169-58df1fcd1a52
          conditions:
          - comparison_operator: contains
            id: 1d5379a9-f10d-4036-8a44-6debd0dcca9f
            value: 重点费用支出情况分析
            varType: string
            variable_selector:
            - '1756082612002'
            - analysisType
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支
        type: if-else
      height: 270
      id: '1756435401071'
      position:
        x: 332.851301645003
        y: 311
      positionAbsolute:
        x: 332.851301645003
        y: 311
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 三项费用支出总体情况分析
        model:
          completion_params: {}
          mode: chat
          name: qwen3-32b
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 2dc987f5-ac7b-44f8-985f-ef64ce381aeb
          role: system
          text: '<三项费用支出总体情况>

            单位：箱、万元

            {{#1756082612002.dataList#}}

            </三项费用支出总体情况>

            <计算值>

            {{#1756082612002.calculatedValues#}}

            </计算值>'
        - id: 5563d657-e78c-445b-8553-fc1d30ad37f9
          role: user
          text: '基于<三项费用支出总体情况>和<计算值>，生成<result>内容（含<result>），如下：

            <result>

            [当前时期]，全市烟草系统三项费用支出合计[三项费用支出合计本年值]万元，同比[三项费用同比变化方向][三项费用同比增减额]万元，[三项费用同比变化方向][三项费用同比增减幅]%。其中：销售费用[销售费用本年值]万元，管理费用[管理费用本年值]万元，财务费用[财务费用本年值]万元。三项费用率[三项费用率]%，比上年同期[三项费用率变化方向][三项费用率同比变化]个百分点（详见表2）。

            </result>



            要求：

            1. 百分比保留两位小数，单位统一为"万元"。

            2. 同比变化需准确区分"增加"和"减少"的表述

            3. 费用率变化需准确区分"增长"、"下降"和"持平"的表述

            4. 如果计算到数值为减少，请改为减少描述。

            5. 保持语句通顺，符合财务分析报告的专业风格。

            6. [xxx]为待替换值，要将[]去掉。



            填充说明（请根据实际数据替换方括号内内容）：

            [当前时期]：填入查询时期，如"2025年1-5月"

            [三项费用支出合计本年值]：填入三项费用合计本年实际值（万元）

            [三项费用同比变化方向]：根据增减额正负填入"增加"或"减少"

            [三项费用同比增减额]：填入同比增减的绝对值（万元）

            [三项费用同比增减幅]：填入同比增减幅度的绝对值（保留两位小数）

            [销售费用本年值]：填入销售费用本年实际值（万元）

            [管理费用本年值]：填入管理费用本年实际值（万元）

            [财务费用本年值]：填入财务费用本年实际值（万元，可为负数）

            [三项费用率]：填入三项费用率（保留两位小数）

            [三项费用率变化方向]：根据费用率变化填入"增长"、"下降"或"持平"

            [三项费用率同比变化]：填入费用率同比变化的绝对值（保留两位小数）'
        selected: true
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: '1756507918427'
      position:
        x: 638
        y: 627
      positionAbsolute:
        x: 638
        y: 627
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 重点费用支出情况分析
        model:
          completion_params: {}
          mode: chat
          name: qwen3-32b
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 2428f930-ac54-4cf3-a01b-c4ef42acc947
          role: system
          text: '<重点费用支出情况>

            单位：箱、万元

            {{#1756082612002.dataList#}}

            </重点费用支出情况>

            <计算值>

            {{#1756082612002.calculatedValues#}}

            </计算值>'
        - id: 3f7fb625-80d8-4de3-9734-86091a4f2bc1
          role: user
          text: '基于<重点费用支出情况>，生成<result>内容（含<result>），如下：


            <result>

            截至[截至日期]，需要提醒各单位、各部门注意的事项金额变动较大支出项目情况

            1、人事科

            （1）劳务费：预算[劳务费预算值]万元，[劳务费执行描述]。同比[劳务费变化方向][劳务费同比变化额]万元。

            （2）职工薪酬：预算[职工薪酬预算值]万元，[职工薪酬执行描述]。其中：实发工资[实发工资本年值]万元，同比[实发工资同比变化方向][实发工资同比变化额]万元，[实发工资同比变化方向][实发工资同比变化幅度]%。员工福利费预算[福利费预算值]万元，实际执行[福利费本年值]万元，执行进度[福利费执行进度]%。

            2、专卖办

            （1）专卖打假经费：预算[专卖打假经费预算值]万元，[当前月份]实际执行[专卖打假经费本年值]万元，执行进度[专卖打假经费执行进度]%。

            （2）专卖打私经费：预算[专卖打私经费预算值]万元，[当前月份]实际执行[专卖打私经费本年值]万元，执行进度[专卖打私经费执行进度]%。

            （3）专卖管理经费：预算[专卖管理经费预算值]万元，[当前月份]实际执行[专卖管理经费本年值]万元，执行进度[专卖管理经费执行进度]%。

            3、信息中心

            信息系统维护费：预算[信息系统维护费预算值]万元，[信息系统维护费执行描述]。

            4、党建科

            （1）企业文化建设费：预算[企业文化建设费预算值]万元，[当前月份]实际执行[企业文化建设费本年值]万元，执行进度[企业文化建设费执行进度]%。

            （2）党组织经费：预算[党组织经费预算值]万元，[党组织经费执行描述]，主要是根据上年工资比例计提。

            5、营销管理中心

            （1）卷烟经营进销存方面：主营业务收入和成本执行进度约[主营业务执行进度]%左右。

            （2）零售终端建设费（重点费用）：预算[零售终端建设费预算值]万元，[零售终端建设费执行描述]。

            （3）交易手续费：年度预算[交易手续费预算值]万元，[交易手续费执行描述]。

            6、物流配送中心

            包装费：预算[包装费预算值]万元，[当前月份]实际执行[包装费本年值]万元，执行进度[包装费执行进度]%。

            7、安全管理科

            警卫消防费：预算[警卫消防费预算值]万元，[当前月份]实际执行[警卫消防费本年值]万元，执行进度[警卫消防费执行进度]%。

            8、办公室（除个别特殊项目外，以本部费用为主）

            （1）办公费（本部）：预算[办公费预算值]万元，[当前月份]实际支出[办公费本年值]万元，执行进度[办公费执行进度]%。

            （2）业务招待费（全市）：预算[业务招待费预算值]万元，[当前月份]实际支出[业务招待费本年值]万元，执行进度[业务招待费执行进度]%。

            （3）会议费（全市）：预算[会议费预算值]万元，[当前月份]实际支出[会议费本年值]万元，执行进度[会议费执行进度]%。

            （4）保险费（全市）：预算[保险费预算值]万元，[当前月份]实际执行[保险费本年值]万元，执行进度[保险费执行进度]%。

            （5）修理费（全市）：预算[修理费预算值]万元，[当前月份]实际执行[修理费本年值]万元，执行进度[修理费执行进度]%。

            （6）差旅费（市局）：预算[差旅费预算值]万元，[当前月份]执行[差旅费本年值]万元，执行进度[差旅费执行进度]%。

            （7）低值易耗品摊销（市局）：预算[低值易耗品摊销预算值]万元，[当前月份]实际执行[低值易耗品摊销本年值]万元，执行进度[低值易耗品摊销执行进度]%。

            （8）燃料费（本部）：预算[燃料费预算值]万元，[当前月份]实际执行[燃料费本年值]万元，执行进度[燃料费执行进度]%。

            （9）企业研发费用（全市）：预算[研发费预算值]万，[当前月份]实际执行[研发费本年值]万元，执行进度[研发费执行进度]%。

            </result>


            要求：

            1. 百分比保留两位小数，所有金额保留两位小数，单位统一为"万元"。

            2. 执行进度需要根据实际情况判断是否需要提醒

            3. 同比变化需准确区分"增加"和"减少"的表述

            4. 对于执行进度为0的项目，描述为"尚未执行"

            5. 保持语句通顺，符合财务分析报告的专业风格。

            6. [xxx]为待替换值，要将[]去掉。

            '
        selected: false
        title: LLM 4
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: '1756508020583'
      position:
        x: 638
        y: 785
      positionAbsolute:
        x: 638
        y: 785
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1756507918427'
          - text
          variable: text
        selected: false
        title: 结束 3
        type: end
      height: 90
      id: '1756508077226'
      position:
        x: 942
        y: 627
      positionAbsolute:
        x: 942
        y: 627
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1756508020583'
          - text
          variable: text
        selected: false
        title: 结束 4
        type: end
      height: 90
      id: '1756508083047'
      position:
        x: 942
        y: 785
      positionAbsolute:
        x: 942
        y: 785
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 资本表分析
        model:
          completion_params: {}
          mode: chat
          name: qwen3-32b
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: e1bbec47-62e3-4d7d-96c9-9443fe735c5e
          role: system
          text: '<资本支出情况>

            单位：箱、万元

            {{#1756082612002.dataList#}}

            </资本支出情况>

            <计算值>

            {{#1756082612002.calculatedValues#}}

            </计算值>'
        - id: 67c4af4f-f85b-45a5-b4d4-c367fcaa4595
          role: user
          text: '基于<资本支出情况>和<计算值>，生成<result>内容（含<result>），如下：

            <result>

            [当前时期]，全市烟草系统资本支出合计[资本支出合计本年值]万元，执行率[执行率]%。其中项目管理类[项目管理类本年值]万元，执行率[项目管理类执行率]%，非项目管理类[非项目管理类本年值]万元，执行率[非项目管理类执行率]%。

            </result>



            要求：

            1. 百分比保留两位小数，所有金额取整，单位统一为"万元"。

            2. 保持语句通顺，符合财务分析报告的专业风格。

            3. [xxx]为待替换值，要将[]去掉。'
        selected: false
        title: LLM 5
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: '1758770061491'
      position:
        x: 638
        y: 942
      positionAbsolute:
        x: 638
        y: 942
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1758770061491'
          - text
          variable: text
        selected: false
        title: 结束 5
        type: end
      height: 90
      id: '1758770085829'
      position:
        x: 942
        y: 942
      positionAbsolute:
        x: 942
        y: 942
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -25.51459489635863
      y: 132.27187007369253
      zoom: 0.8705505632961252
