package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO;

import java.util.List;
import java.util.Map;

/**
 * 财务分析服务接口
 *
 * <AUTHOR>
 */
public interface IFinanceAnalysisService extends BaseService<FinanceAnalysisEntity> {

    /**
     * 获取主要经济指标分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 主要经济指标分析数据
     */
    List<Map<String, Object>> selectMainEconomicIndicators(Map<String, Object> params);

    /**
     * 获取三项费用分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 三项费用分析数据
     */
    List<Map<String, Object>> selectThreeExpenses(Map<String, Object> params);

    /**
     * 获取重点费用支出情况分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 重点费用支出情况分析数据
     */
    List<Map<String, Object>> selectKeyExpenses(Map<String, Object> params);

    /**
     * 获取仅三项费用分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 销售费用汇总数据
     */
    List<Map<String, Object>> selectOnlyThreeExpenses(Map<String, Object> params);

    /**
     * 获取资本表数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 资本表数据
     */
    List<Map<String, Object>> selectCapital(Map<String, Object> params);

    /**
     * 分析三项费用并保存结果
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 工作流运行ID
     */
    String analyzeThreeExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) throws JsonProcessingException;

    /**
     * 分析三项费用并保存结果
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @param reportId 报告ID
     * @return 工作流运行ID
     */
    String analyzeThreeExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId) throws JsonProcessingException;

    /**
     * 分析三项费用并保存结果（支持Excel数据）
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @param reportId 报告ID
     * @param excelDataKey Excel数据缓存键
     * @return 工作流运行ID
     */
    String analyzeThreeExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId, String excelDataKey) throws JsonProcessingException;

    /**
     * 分析重点费用支出情况并保存结果
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 工作流运行ID
     */
    String analyzeKeyExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) throws JsonProcessingException;

    /**
     * 分析重点费用支出情况并保存结果
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @param reportId 报告ID
     * @return 工作流运行ID
     */
    String analyzeKeyExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId) throws JsonProcessingException;

    /**
     * 分析重点费用支出情况并保存结果（支持Excel数据）
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @param reportId 报告ID
     * @param excelDataKey Excel数据缓存键
     * @return 工作流运行ID
     */
    String analyzeKeyExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId, String excelDataKey) throws JsonProcessingException;

    /**
     * 分析资本表并保存结果
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 工作流运行ID
     */
    String analyzeCapital(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) throws JsonProcessingException;

    /**
     * 分析资本表并保存结果
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @param reportId 报告ID
     * @return 工作流运行ID
     */
    String analyzeCapital(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId) throws JsonProcessingException;

    /**
     * 分析资本表并保存结果（支持Excel数据）
     *
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @param reportId 报告ID
     * @param excelDataKey Excel数据缓存键
     * @return 工作流运行ID
     */
    String analyzeCapital(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId, String excelDataKey) throws JsonProcessingException;

    /**
     * 分页查询财务分析数据
     *
     * @param page            分页参数
     * @param financeAnalysis 查询条件
     * @return 分页数据
     */
    IPage<FinanceAnalysisVO> selectFinanceAnalysisPage(IPage<FinanceAnalysisVO> page, FinanceAnalysisVO financeAnalysis);

    /**
     * 保存财务分析
     *
     * @param financeAnalysis 财务分析实体
     * @return 是否成功
     */
    boolean saveFinanceAnalysis(FinanceAnalysisEntity financeAnalysis);

    /**
     * 更新财务分析
     *
     * @param financeAnalysis 财务分析实体
     * @return 是否成功
     */
    boolean updateFinanceAnalysis(FinanceAnalysisEntity financeAnalysis);

    /**
     * 删除财务分析
     *
     * @param ids 主键集合
     * @return 是否成功
     */
    boolean removeFinanceAnalysis(String ids);

    /**
     * 获取财务分析详情
     *
     * @param id 主键
     * @return 财务分析详情
     */
    FinanceAnalysisVO getFinanceAnalysisDetail(Long id);


    /**
     * 根据组合主键查询唯一记录
     *
     * @param name        分析名称
     * @param queryYear   查询年份
     * @param compareYear 对比年份
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @return 财务分析记录
     */
    FinanceAnalysisEntity getOneByCompositeKey(String name, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth);

    /**
     * 根据组合主键查询列表
     *
     * @param name        分析名称
     * @param queryYear   查询年份
     * @param compareYear 对比年份
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @return 财务分析记录列表
     */
    List<FinanceAnalysisVO> listByCompositeKey(String name, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth);

    /**
     * 分析主要经济指标并保存结果
     *
     * @param queryYear   查询年份
     * @param compareYear 对比年份
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @return 工作流运行ID
     */
    String analyzeMainEconomicIndicators(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth);

    /**
     * 分析主要经济指标并保存结果
     *
     * @param queryYear   查询年份
     * @param compareYear 对比年份
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @param reportId    报告ID
     * @return 工作流运行ID
     */
    String analyzeMainEconomicIndicators(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId);

    /**
     * 分析主要经济指标并保存结果（支持Excel数据）
     *
     * @param queryYear   查询年份
     * @param compareYear 对比年份
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @param reportId    报告ID
     * @param excelDataKey Excel数据缓存键
     * @return 工作流运行ID
     */
    String analyzeMainEconomicIndicators(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId, String excelDataKey);

    /**
     * 根据workflowRunId查询唯一记录
     *
     * @param workflowRunId   workflowRunId
     * @return 财务分析记录
     */
    FinanceAnalysisEntity getOneByWorkflowRunId(String workflowRunId);

    String analyzeTaxAndProfit(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, List<Map<String, Object>> indicatorsData) throws JsonProcessingException;

    /**
     * 根据workflowRunId更新执行结果
     *
     * @param workflowRunId   workflowRunId
     * @return 财务分析记录
     */
    FinanceAnalysisVO updateAnalysisResultByWorkflowRunId(String workflowRunId);

    String analyzeCigaretteOperation(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, List<Map<String, Object>> indicatorsData) throws JsonProcessingException;
}