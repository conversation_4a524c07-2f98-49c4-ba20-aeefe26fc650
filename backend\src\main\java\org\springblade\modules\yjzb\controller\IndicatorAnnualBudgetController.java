/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.IndicatorAnnualBudgetEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorAnnualBudgetVO;
import org.springblade.modules.yjzb.excel.IndicatorAnnualBudgetExcel;
import org.springblade.modules.yjzb.wrapper.IndicatorAnnualBudgetWrapper;
import org.springblade.modules.yjzb.service.IIndicatorAnnualBudgetService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 指标年度预算 控制器
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicatorAnnualBudget")
@Tag(name = "指标年度预算", description = "指标年度预算接口")
public class IndicatorAnnualBudgetController extends BladeController {

    private final IIndicatorAnnualBudgetService indicatorAnnualBudgetService;

    /**
     * 获取某个指标某一年度的预算
     */
    @GetMapping("/getByIndicatorAndYear")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "按指标与年份获取预算", description = "参数：indicatorId, year")
    public R<IndicatorAnnualBudgetVO> getByIndicatorAndYear(@RequestParam Long indicatorId,
            @RequestParam Integer year) {
        try {
            IndicatorAnnualBudgetEntity entity = indicatorAnnualBudgetService.findByIndicatorAndYear(indicatorId, year);
            if (entity == null) {
                // 如果查不到预算数据，返回null而不是报错
                return R.data(null);
            }
            return R.data(IndicatorAnnualBudgetWrapper.build().entityVO(entity));
        } catch (Exception e) {
            // 记录日志但不抛出异常，返回null
            log.warn("查询指标预算失败: indicatorId={}, year={}, error={}", indicatorId, year, e.getMessage());
            return R.data(null);
        }
    }

    /**
     * 获取某个指标类型某一年度的预算总和
     */
    @GetMapping("/sumByTypeAndYear")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "按指标类型与年份汇总预算", description = "参数：indicatorTypeId, year")
    public R<java.math.BigDecimal> sumByTypeAndYear(@RequestParam Long indicatorTypeId, @RequestParam Integer year) {
        return R.data(indicatorAnnualBudgetService.sumBudgetByTypeAndYear(indicatorTypeId, year));
    }

    /**
     * 指标年度预算 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicatorAnnualBudget")
    public R<IndicatorAnnualBudgetVO> detail(IndicatorAnnualBudgetEntity indicatorAnnualBudget) {
        IndicatorAnnualBudgetEntity detail = indicatorAnnualBudgetService
                .getOne(Condition.getQueryWrapper(indicatorAnnualBudget));
        return R.data(IndicatorAnnualBudgetWrapper.build().entityVO(detail));
    }

    /**
     * 指标年度预算 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicatorAnnualBudget")
    public R<IPage<IndicatorAnnualBudgetVO>> list(
            @Parameter(hidden = true) @RequestParam Map<String, Object> indicatorAnnualBudget, Query query) {
        IPage<IndicatorAnnualBudgetEntity> pages = indicatorAnnualBudgetService.page(Condition.getPage(query),
                Condition.getQueryWrapper(indicatorAnnualBudget, IndicatorAnnualBudgetEntity.class));
        return R.data(IndicatorAnnualBudgetWrapper.build().pageVO(pages));
    }

    /**
     * 指标年度预算 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入indicatorAnnualBudget")
    public R<IPage<IndicatorAnnualBudgetVO>> page(IndicatorAnnualBudgetVO indicatorAnnualBudget, Query query) {
        IPage<IndicatorAnnualBudgetVO> pages = indicatorAnnualBudgetService
                .selectIndicatorAnnualBudgetPage(Condition.getPage(query), indicatorAnnualBudget);
        return R.data(pages);
    }

    /**
     * 指标年度预算 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入indicatorAnnualBudget")
    public R save(@Valid @RequestBody IndicatorAnnualBudgetEntity indicatorAnnualBudget) {
        return R.status(indicatorAnnualBudgetService.save(indicatorAnnualBudget));
    }

    /**
     * 指标年度预算 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入indicatorAnnualBudget")
    public R update(@Valid @RequestBody IndicatorAnnualBudgetEntity indicatorAnnualBudget) {
        return R.status(indicatorAnnualBudgetService.updateById(indicatorAnnualBudget));
    }

    /**
     * 指标年度预算 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入indicatorAnnualBudget")
    public R submit(@Valid @RequestBody IndicatorAnnualBudgetEntity indicatorAnnualBudget) {
        return R.status(indicatorAnnualBudgetService.saveOrUpdate(indicatorAnnualBudget));
    }

    /**
     * 指标年度预算 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(indicatorAnnualBudgetService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 导出数据
     */
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    @GetMapping("/export-indicatorAnnualBudget")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "导出数据", description = "传入indicatorAnnualBudget")
    public void exportIndicatorAnnualBudget(
            @Parameter(hidden = true) @RequestParam Map<String, Object> indicatorAnnualBudget, BladeUser bladeUser,
            HttpServletResponse response) {
        QueryWrapper<IndicatorAnnualBudgetEntity> queryWrapper = Condition.getQueryWrapper(indicatorAnnualBudget,
                IndicatorAnnualBudgetEntity.class);
        // if (!AuthUtil.isAdministrator()) {
        // queryWrapper.lambda().eq(IndicatorAnnualBudget::getTenantId,
        // bladeUser.getTenantId());
        // }
        // queryWrapper.lambda().eq(IndicatorAnnualBudgetEntity::getIsDeleted,
        // BladeConstant.DB_NOT_DELETED);
        List<IndicatorAnnualBudgetExcel> list = indicatorAnnualBudgetService.exportIndicatorAnnualBudget(queryWrapper);
        ExcelUtil.export(response, "指标年度预算数据" + DateUtil.time(), "指标年度预算数据表", list, IndicatorAnnualBudgetExcel.class);
    }

}
