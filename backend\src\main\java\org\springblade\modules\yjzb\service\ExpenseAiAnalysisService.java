package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.entity.ExpenseAiAnalysis;

/**
 * 办公费用AI解读分析服务接口
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
public interface ExpenseAiAnalysisService extends IService<ExpenseAiAnalysis> {

    /**
     * 执行AI分析
     *
     * @param indicatorId  指标ID
     * @param period       期间
     * @param analysisType 分析类型
     * @param inputParams  输入参数
     * @return 分析结果
     */
    String executeAnalysis(Long indicatorId, String period, String analysisType, String inputParams, boolean force);

    /**
     * 构建并返回指定分析类型的完整提示词（包含业务规则替换后内容），供前端预览/编辑
     */
    String buildPromptPreview(Long indicatorId, String analysisType);

    /**
     * 获取指标关联的业务知识列表
     * 
     * @param indicatorId 指标ID
     * @return 业务知识条目列表
     */
    java.util.List<String> getBusinessRulesList(Long indicatorId);

    /**
     * 获取AI分析结果
     *
     * @param indicatorId  指标ID
     * @param period       期间
     * @param analysisType 分析类型
     * @return 分析结果
     */
    ExpenseAiAnalysis getAnalysisResult(Long indicatorId, String period, String analysisType);

    /**
     * 检查是否有缓存的分析结果
     *
     * @param indicatorId  指标ID
     * @param period       期间
     * @param analysisType 分析类型
     * @return 是否有缓存
     */
    boolean hasCachedResult(Long indicatorId, String period, String analysisType);
}
