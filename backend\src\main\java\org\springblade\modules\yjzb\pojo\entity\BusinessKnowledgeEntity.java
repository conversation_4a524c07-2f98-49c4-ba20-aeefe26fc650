/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import java.io.Serial;

/**
 * 业务知识 实体类
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@Data
@TableName("yjzb_business_knowledge")
@Schema(description = "BusinessKnowledge对象")
@EqualsAndHashCode(callSuper = true)
public class BusinessKnowledgeEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 知识标题
     */
    @Schema(description = "知识标题")
    private String knowledgeTitle;
    /**
     * 应用指标
     */
    @Schema(description = "应用指标")
    private String applicableIndicators;
    /**
     * 知识类型
     */
    @Schema(description = "知识类型")
    private Integer knowledgeType;
    /**
     * 知识内容
     */
    @Schema(description = "知识内容")
    private String knowledgeContent;

    /**
     * 财务分类ID（关联财务分类表主键）
     */
    @Schema(description = "财务分类ID（关联财务分类表主键）")
    private Long knowledgeCategory;

}
