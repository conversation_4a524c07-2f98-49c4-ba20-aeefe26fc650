import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/yjzb/businessKnowledge/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/yjzb/businessKnowledge/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/yjzb/businessKnowledge/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/yjzb/businessKnowledge/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/yjzb/businessKnowledge/submit',
    method: 'post',
    data: row
  })
}

