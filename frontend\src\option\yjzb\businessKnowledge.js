export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键ID",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "知识标题",
      prop: "knowledgeTitle",
      type: "input",
      search: true,
      searchSpan: 6,
      placeholder: '请输入知识标题',
    },
    {
      label: "适用指标",
      prop: "applicableIndicators",
      type: "input",
      formslot: true,
      slot: true,
      placeholder: '点击选择指标',
    },
    {
      label: "知识类型",
      prop: "knowledgeType",
      type: "select",
      dicData: [
        { label: '预测知识', value: 1 },
        { label: '归因知识', value: 2 },
        { label: '预警知识', value: 3 },
      ],
      search: true,
      searchSpan: 6,
      placeholder: '请选择知识类型',
    },
    {
      label: "知识内容",
      prop: "knowledgeContent",
      type: "textarea",
      minRows: 4,
      span: 24,
      search: true,
      searchSpan: 6,
      placeholder: '请输入知识内容',
    },
    {
      label: "",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
