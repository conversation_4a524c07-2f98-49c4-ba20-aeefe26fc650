/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity;
import org.springblade.modules.yjzb.pojo.vo.BusinessKnowledgeVO;
import org.springblade.modules.yjzb.service.IFinanceCategoryService;
import org.springblade.core.tool.utils.SpringUtil;
import java.util.Objects;

/**
 * 业务知识 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
public class BusinessKnowledgeWrapper extends BaseEntityWrapper<BusinessKnowledgeEntity, BusinessKnowledgeVO> {

    public static BusinessKnowledgeWrapper build() {
        return new BusinessKnowledgeWrapper();
    }

    @Override
    public BusinessKnowledgeVO entityVO(BusinessKnowledgeEntity businessKnowledge) {
        BusinessKnowledgeVO businessKnowledgeVO = Objects
                .requireNonNull(BeanUtil.copyProperties(businessKnowledge, BusinessKnowledgeVO.class));

        // 设置财务分类信息
        if (businessKnowledge.getKnowledgeCategory() != null) {
            try {
                IFinanceCategoryService financeCategoryService = SpringUtil.getBean(IFinanceCategoryService.class);
                FinanceCategoryEntity category = financeCategoryService
                        .getById(businessKnowledge.getKnowledgeCategory());
                if (category != null) {
                    businessKnowledgeVO.setFinanceCategoryName(category.getCategoryName());
                    businessKnowledgeVO.setFinanceCategoryPath(category.getFullPath());
                }
            } catch (Exception e) {
                // 忽略异常，保持原有逻辑
            }
        }

        // User createUser = UserCache.getUser(businessKnowledge.getCreateUser());
        // User updateUser = UserCache.getUser(businessKnowledge.getUpdateUser());
        // businessKnowledgeVO.setCreateUserName(createUser.getName());
        // businessKnowledgeVO.setUpdateUserName(updateUser.getName());

        return businessKnowledgeVO;
    }

}
