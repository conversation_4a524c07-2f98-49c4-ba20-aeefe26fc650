package org.springblade.modules.yjzb.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.xmlbeans.XmlCursor;
import org.springblade.modules.yjzb.service.impl.ExcelDataServiceImpl;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Word模板工具类
 */
@Slf4j
public class WordTemplateUtil {
    private static class RunInfo {
        String text;
        boolean bold;
        boolean italic;
        String color;
        int fontSize;
        String fontFamily;
        UnderlinePatterns underline;
        boolean strike;
        VerticalAlign subscript;
    }

    /**
     * 加载Word模板
     *
     * @param templatePath 模板路径（相对于classpath）
     * @return Word文档对象
     * @throws IOException 如果模板加载失败
     */
    public static XWPFDocument loadTemplate(String templatePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(templatePath);
        InputStream inputStream = resource.getInputStream();
        XWPFDocument document = new XWPFDocument(inputStream);
        inputStream.close();
        return document;
    }

    /**
     * 替换文档中的占位符
     *
     * @param document     Word文档对象
     * @param placeholders 占位符映射
     */
    public static void replacePlaceholders(XWPFDocument document, Map<String, String> placeholders) {
        // 替换段落中的占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, placeholders);
        }

        // 替换表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceInParagraph(paragraph, placeholders);
                    }
                }
            }
        }
    }

    /**
     * 替换段落中的占位符并保留原有样式
     *
     * @param paragraph    段落对象
     * @param placeholders 占位符映射
     */
    /**
     * 替换段落中的占位符并保留每个 Run 的原始样式
     *
     * @param paragraph    段落对象
     * @param placeholders 占位符映射
     */
    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> placeholders) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        // 存储每个 Run 的原始样式信息和原始文本
        List<RunInfo> runInfos = new ArrayList<>();

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null) continue;

            RunInfo info = new RunInfo();
            info.text = text;
            info.bold = run.isBold();
            info.italic = run.isItalic();
            info.color = run.getColor();
            info.fontSize = run.getFontSize();
            info.fontFamily = run.getFontFamily();
            info.underline = run.getUnderline();
            info.strike = run.isStrike();
            info.subscript = run.getSubscript();

            runInfos.add(info);
        }

        // 合并段落文本用于替换占位符
        StringBuilder fullText = new StringBuilder();
        for (RunInfo info : runInfos) {
            fullText.append(info.text);
        }

        // 如果段落内没有任意占位符，直接返回，避免破坏原有格式（空格/制表位/Leader等）
        String full = fullText.toString();
        boolean hasPlaceholder = false;
        for (String key : placeholders.keySet()) {
            if (full.contains("${" + key + "}")) { hasPlaceholder = true; break; }
        }
        if (!hasPlaceholder) {
            return;
        }

        // 替换所有占位符
        String replacedText = full;
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue();
            if (value == null) value = "";
            replacedText = replacedText.replace(placeholder, value);
        }

        // 清除原有 Runs
        while (!paragraph.getRuns().isEmpty()) {
            paragraph.removeRun(0);
        }

        // 检查替换后的文本是否包含换行符
        if (replacedText.contains("\n") || replacedText.contains("\r")) {
            // 如果包含换行符，创建一个新的Run来处理整个文本
            XWPFRun newRun = paragraph.createRun();

            // 使用第一个Run的样式作为默认样式
            if (!runInfos.isEmpty()) {
                RunInfo defaultInfo = runInfos.get(0);
                newRun.setBold(defaultInfo.bold);
                newRun.setItalic(defaultInfo.italic);
                if (defaultInfo.color != null) newRun.setColor(defaultInfo.color);
                if (defaultInfo.fontSize > 0) newRun.setFontSize(defaultInfo.fontSize);
                if (defaultInfo.fontFamily != null) newRun.setFontFamily(defaultInfo.fontFamily);
                if (defaultInfo.underline != null && defaultInfo.underline != UnderlinePatterns.NONE) {
                    newRun.setUnderline(defaultInfo.underline);
                } else {
                    newRun.setUnderline(UnderlinePatterns.NONE);
                }
                newRun.setStrike(defaultInfo.strike);
                newRun.setSubscript(defaultInfo.subscript);
            }

            // 处理包含换行符的文本
            setTextWithLineBreaks(newRun, replacedText);
        } else {
            // 如果不包含换行符，按原来的方式分割插入
            int remainingLength = replacedText.length();
            int offset = 0;

            for (RunInfo info : runInfos) {
                int lengthToUse = Math.min(info.text.length(), remainingLength);
                if (lengthToUse <= 0) continue;

                String newText = replacedText.substring(offset, offset + lengthToUse);

                XWPFRun newRun = paragraph.createRun();
                newRun.setBold(info.bold);
                newRun.setItalic(info.italic);
                if (info.color != null) newRun.setColor(info.color);
                if (info.fontSize > 0) newRun.setFontSize(info.fontSize);
                if (info.fontFamily != null) newRun.setFontFamily(info.fontFamily);
                if (info.underline != null && info.underline != UnderlinePatterns.NONE) {
                    newRun.setUnderline(info.underline);
                } else {
                    newRun.setUnderline(UnderlinePatterns.NONE);
                }
                newRun.setStrike(info.strike);
                newRun.setSubscript(info.subscript);

                newRun.setText(newText);

                offset += lengthToUse;
                remainingLength -= lengthToUse;
            }

            // 如果还有剩余文本，创建一个新 Run 插入
            if (remainingLength > 0) {
                String remainingText = replacedText.substring(offset);

                XWPFRun fallbackRun = paragraph.createRun();
                fallbackRun.setText(remainingText);

                // 使用第一个 Run 的样式作为默认样式
                if (!runInfos.isEmpty()) {
                    RunInfo defaultInfo = runInfos.get(0);
                    fallbackRun.setBold(defaultInfo.bold);
                    fallbackRun.setItalic(defaultInfo.italic);
                    if (defaultInfo.color != null) fallbackRun.setColor(defaultInfo.color);
                    if (defaultInfo.fontSize > 0) fallbackRun.setFontSize(defaultInfo.fontSize);
                    if (defaultInfo.fontFamily != null) fallbackRun.setFontFamily(defaultInfo.fontFamily);
                    fallbackRun.setUnderline(defaultInfo.underline);
                    fallbackRun.setStrike(defaultInfo.strike);
                    fallbackRun.setSubscript(defaultInfo.subscript);
                }
            }
        }
    }


    /**
     * 设置文本并处理换行符
     *
     * @param run  XWPFRun对象
     * @param text 要设置的文本
     */
    private static void setTextWithLineBreaks(XWPFRun run, String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        // 处理不同类型的换行符
        String[] lines = text.split("\\r?\\n");

        // 设置第一行文本
        if (lines.length > 0) {
            run.setText(lines[0], 0); // 使用setText(text, 0)设置第一行
        }

        // 处理后续行，每行前添加换行符
        for (int i = 1; i < lines.length; i++) {
            run.addBreak(); // 添加换行符
            run.setText(lines[i]); // 追加文本
        }
    }

    /**
     * 替换表格内容，支持JSON格式数据
     *
     * @param document        Word文档对象
     * @param searchText      要搜索的文本
     * @param jsonReplacement JSON格式的表格数据
     */
    public static void replaceTableContent(XWPFDocument document, String searchText, String jsonReplacement, List<String> columnNames) {
        try {
            // 解析JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> tableData = objectMapper.readValue(jsonReplacement,
                new TypeReference<List<Map<String, Object>>>() {});

            // 在占位符位置创建新表格
            createNewTableWithData(document, searchText, tableData, columnNames);

        } catch (Exception e) {
            log.error("解析表格JSON数据失败: {}", jsonReplacement, e);
            // 如果JSON解析失败，按普通文本处理
            Map<String, String> placeholder = new java.util.HashMap<>();
            placeholder.put(searchText.replace("${", "").replace("}", ""), jsonReplacement);
            replacePlaceholders(document, placeholder);
        }
    }

    /**
     * 替换表格内容，支持Excel格式信息
     *
     * @param document        Word文档对象
     * @param searchText      要搜索的文本
     * @param jsonReplacement JSON格式的表格数据
     * @param columnNames     列名列表
     * @param formatInfo      Excel格式信息
     */
    public static void replaceTableContentWithFormat(XWPFDocument document, String searchText,
                                                    String jsonReplacement, List<String> columnNames,
                                                    ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        try {
            // 解析JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> tableData = objectMapper.readValue(jsonReplacement,
                new TypeReference<List<Map<String, Object>>>() {});

            // 在占位符位置创建带格式的新表格
            createFormattedTableWithData(document, searchText, tableData, columnNames, formatInfo);

        } catch (Exception e) {
            log.error("解析表格JSON数据失败: {}", jsonReplacement, e);
            // 如果JSON解析失败，回退到普通表格
            replaceTableContent(document, searchText, jsonReplacement, columnNames);
        }
    }

    /**
     * 在占位符位置创建新表格
     */
    private static void createNewTableWithData(XWPFDocument document, String searchText, List<Map<String, Object>> tableData, List<String> columnNames) {
        if (tableData.isEmpty()) {
            return;
        }

        // 获取列名（从第一行数据中获取）
        Map<String, Object> firstRow = tableData.get(0);

        // 查找包含占位符的段落
        XWPFParagraph targetParagraph = null;

        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String paragraphText = paragraph.getText();
            if (paragraphText != null && paragraphText.contains(searchText)) {
                targetParagraph = paragraph;
                break;
            }
        }

        // 如果找到了包含占位符的段落，在其位置插入表格
        if (targetParagraph != null) {
            // 清空占位符段落的内容
            while (!targetParagraph.getRuns().isEmpty()) {
                targetParagraph.removeRun(0);
            }

            // 使用底层API在指定位置插入表格
            XmlCursor cursor = targetParagraph.getCTP().newCursor();
            // cursor.toNextSibling(); // 移动到段落后面

            // 创建表格
            XWPFTable table = document.insertNewTbl(cursor);

            // 创建表头
            XWPFTableRow headerRow = table.getRow(0);
            // 确保表头有足够的单元格
            while (headerRow.getTableCells().size() < columnNames.size()) {
                headerRow.addNewTableCell();
            }

            // 填充表头
            for (int j = 0; j < columnNames.size() && j < headerRow.getTableCells().size(); j++) {
                XWPFTableCell cell = headerRow.getTableCells().get(j);
                // cell.setText(columnNames.get(j));

                // 设置表头样式
                XWPFParagraph paragraph = cell.getParagraphs().get(0);
                XWPFRun run = paragraph.createRun();
                run.setText(columnNames.get(j));
                run.setBold(true);
                run.setFontSize(10);
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            }

            // 填充数据行
            for (int i = 0; i < tableData.size(); i++) {
                Map<String, Object> rowData = tableData.get(i);
                XWPFTableRow row = table.createRow();

                // 确保行有足够的单元格
                while (row.getTableCells().size() < columnNames.size()) {
                    row.addNewTableCell();
                }

                // 填充单元格数据
                for (int j = 0; j < columnNames.size() && j < row.getTableCells().size(); j++) {
                    XWPFTableCell cell = row.getTableCells().get(j);
                    Object value = rowData.get(columnNames.get(j));
                    cell.setText(value != null ? value.toString() : "");

                    XWPFParagraph paragraph = cell.getParagraphs().get(0);
                    paragraph.setAlignment(ParagraphAlignment.RIGHT);
                    cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

                    if("项目".equals(columnNames.get(j)) || "预算项目".equals(columnNames.get(j))) {
                        paragraph.setAlignment(ParagraphAlignment.LEFT);
                    }
                    if("行次".equals(columnNames.get(j))) {
                        paragraph.setAlignment(ParagraphAlignment.CENTER);
                    }

                    if (paragraph.getRuns() != null && !paragraph.getRuns().isEmpty()) {
                        XWPFRun run = paragraph.getRuns().get(0);
                        run.setFontSize(11);
                        run.setFontFamily("宋体");
                    }
                }
            }
        }
    }

    /**
     * 创建带格式的表格
     */
    private static void createFormattedTableWithData(XWPFDocument document, String searchText,
                                                   List<Map<String, Object>> tableData,
                                                   List<String> columnNames,
                                                   ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        if (tableData.isEmpty() || formatInfo == null) {
            // 如果没有格式信息，回退到普通表格
            createNewTableWithData(document, searchText, tableData, columnNames);
            return;
        }

        // 查找包含占位符的段落
        XWPFParagraph targetParagraph = null;
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String paragraphText = paragraph.getText();
            if (paragraphText != null && paragraphText.contains(searchText)) {
                targetParagraph = paragraph;
                break;
            }
        }

        if (targetParagraph != null) {
            // 清空占位符段落的内容
            while (!targetParagraph.getRuns().isEmpty()) {
                targetParagraph.removeRun(0);
            }

            // 创建表格
            XmlCursor cursor = targetParagraph.getCTP().newCursor();
            XWPFTable table = document.insertNewTbl(cursor);

            // 创建多级表头
            createFormattedHeaders(table, formatInfo);

            // 创建数据行
            createFormattedDataRows(table, tableData, columnNames, formatInfo);
        }
    }

    /**
     * 创建带格式的表头
     */
    private static void createFormattedHeaders(XWPFTable table, ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        List<List<ExcelDataServiceImpl.CellFormatInfo>> headerFormats = formatInfo.getHeaderFormats();
        List<String> columnNames = formatInfo.getColumnNames();

        if (headerFormats.isEmpty() || columnNames.isEmpty()) {
            return;
        }

        int headerRowCount = formatInfo.getHeaderRowCount();

        // 创建所有表头行
        List<XWPFTableRow> headerRows = new ArrayList<>();
        for (int headerRowIndex = 0; headerRowIndex < headerRowCount; headerRowIndex++) {
            XWPFTableRow headerRow;
            if (headerRowIndex == 0) {
                headerRow = table.getRow(0); // 使用默认创建的第一行
            } else {
                headerRow = table.createRow(); // 创建新行
            }

            // 确保表头有足够的单元格
            while (headerRow.getTableCells().size() < columnNames.size()) {
                headerRow.addNewTableCell();
            }

            headerRows.add(headerRow);
        }

        // 处理表头内容和合并
        if (headerRowCount == 1) {
            // 单级表头
            createSingleLevelHeaders(headerRows.get(0), headerFormats.get(0), formatInfo);
        } else {
            // 多级表头
            createMultiLevelHeaders(headerRows, headerFormats, formatInfo);
        }
    }

    /**
     * 创建单级表头
     */
    private static void createSingleLevelHeaders(XWPFTableRow headerRow,
                                               List<ExcelDataServiceImpl.CellFormatInfo> rowFormats,
                                               ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        List<String> columnNames = formatInfo.getColumnNames();
        List<String> originalTexts = formatInfo.getOriginalFirstHeaderTexts();

        for (int colIndex = 0; colIndex < columnNames.size() && colIndex < headerRow.getTableCells().size(); colIndex++) {
            XWPFTableCell cell = headerRow.getTableCells().get(colIndex);
            ExcelDataServiceImpl.CellFormatInfo cellFormat = colIndex < rowFormats.size() ?
                rowFormats.get(colIndex) : new ExcelDataServiceImpl.CellFormatInfo();

            // 获取原始表头文本
            String headerText = "";
            if (originalTexts != null && colIndex < originalTexts.size()) {
                headerText = originalTexts.get(colIndex);
            }
            if (headerText == null || headerText.trim().isEmpty()) {
                headerText = columnNames.get(colIndex);
            }

            setCellContent(cell, headerText, cellFormat, true);
        }
    }

    /**
     * 创建多级表头
     */
    private static void createMultiLevelHeaders(List<XWPFTableRow> headerRows,
                                              List<List<ExcelDataServiceImpl.CellFormatInfo>> headerFormats,
                                              ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        List<String> columnNames = formatInfo.getColumnNames();

        // 跟踪需要垂直合并的单元格
        boolean[][] verticalMergeNeeded = new boolean[headerRows.size()][columnNames.size()];

        // 分析哪些单元格需要垂直合并
        analyzeVerticalMergeNeeds(verticalMergeNeeded, formatInfo);

        // 处理每一行
        for (int rowIndex = 0; rowIndex < headerRows.size(); rowIndex++) {
            XWPFTableRow headerRow = headerRows.get(rowIndex);
            List<ExcelDataServiceImpl.CellFormatInfo> rowFormats = headerFormats.get(rowIndex);

            createHeaderRowWithMergedCells(headerRow, rowFormats, rowIndex, formatInfo);
        }

        // 应用垂直合并
        applyVerticalMerges(headerRows, verticalMergeNeeded);
    }

    /**
     * 分析垂直合并需求（基于Excel实际合并信息）
     */
    private static void analyzeVerticalMergeNeeds(boolean[][] verticalMergeNeeded,
                                                ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        List<List<ExcelDataServiceImpl.CellFormatInfo>> headerFormats = formatInfo.getHeaderFormats();

        if (headerFormats.isEmpty() || headerFormats.size() < 2) {
            return;
        }

        // 获取第一行的格式信息
        List<ExcelDataServiceImpl.CellFormatInfo> firstRowFormats = headerFormats.get(0);

        for (int colIndex = 0; colIndex < firstRowFormats.size(); colIndex++) {
            ExcelDataServiceImpl.CellFormatInfo cellFormat = firstRowFormats.get(colIndex);

            // 直接根据Excel中的垂直合并信息判断
            if (cellFormat.isMerged() && cellFormat.getMergedRowSpan() > 1) {
                verticalMergeNeeded[0][colIndex] = true;
            } else {
                verticalMergeNeeded[0][colIndex] = false;
            }
        }
    }



    /**
     * 应用垂直合并
     */
    private static void applyVerticalMerges(List<XWPFTableRow> headerRows, boolean[][] verticalMergeNeeded) {
        if (headerRows.size() < 2) {
            return;
        }

        for (int colIndex = 0; colIndex < verticalMergeNeeded[0].length; colIndex++) {
            if (verticalMergeNeeded[0][colIndex]) {
                try {
                    // 设置第一行单元格为垂直合并开始
                    XWPFTableCell firstCell = headerRows.get(0).getTableCells().get(colIndex);
                    if (firstCell.getCTTc().getTcPr() == null) {
                        firstCell.getCTTc().addNewTcPr();
                    }
                    firstCell.getCTTc().getTcPr().addNewVMerge().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);

                    // 设置第二行单元格为垂直合并继续
                    XWPFTableCell secondCell = headerRows.get(1).getTableCells().get(colIndex);
                    if (secondCell.getCTTc().getTcPr() == null) {
                        secondCell.getCTTc().addNewTcPr();
                    }
                    secondCell.getCTTc().getTcPr().addNewVMerge().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

                    // 清空第二行单元格内容
                    secondCell.removeParagraph(0);
                    secondCell.addParagraph();

                } catch (Exception e) {
                    log.warn("垂直合并单元格失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 创建带合并单元格的表头行
     */
    private static void createHeaderRowWithMergedCells(XWPFTableRow headerRow,
                                                     List<ExcelDataServiceImpl.CellFormatInfo> rowFormats,
                                                     int headerRowIndex,
                                                     ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        List<String> columnNames = formatInfo.getColumnNames();

        // 跟踪已处理的列
        boolean[] processedCols = new boolean[columnNames.size()];

        for (int colIndex = 0; colIndex < columnNames.size() && colIndex < headerRow.getTableCells().size(); colIndex++) {
            if (processedCols[colIndex]) {
                continue; // 已经作为合并单元格处理过了
            }

            XWPFTableCell cell = headerRow.getTableCells().get(colIndex);
            ExcelDataServiceImpl.CellFormatInfo cellFormat = colIndex < rowFormats.size() ?
                rowFormats.get(colIndex) : new ExcelDataServiceImpl.CellFormatInfo();

            // 获取原始表头文本
            String headerText = getOriginalHeaderText(colIndex, headerRowIndex, formatInfo);

            // 检查是否需要合并单元格
            if (cellFormat.isMerged() && cellFormat.getMergedColSpan() > 1) {
                // 处理合并单元格
                int mergeSpan = Math.min(cellFormat.getMergedColSpan(), columnNames.size() - colIndex);

                // 设置主单元格内容
                setCellContent(cell, headerText, cellFormat, true);

                // 标记相关列为已处理
                for (int i = 0; i < mergeSpan; i++) {
                    if (colIndex + i < processedCols.length) {
                        processedCols[colIndex + i] = true;
                    }
                }

                // 在Word中实现单元格合并（水平合并）
                try {
                    mergeCellsHorizontally(headerRow, colIndex, colIndex + mergeSpan - 1);
                } catch (Exception e) {
                    log.warn("合并单元格失败: {}", e.getMessage());
                }

            } else {
                // 普通单元格
                setCellContent(cell, headerText, cellFormat, true);
                processedCols[colIndex] = true;
            }
        }
    }

    /**
     * 获取原始表头文本
     */
    private static String getOriginalHeaderText(int colIndex, int headerRowIndex, ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        try {
            if (headerRowIndex == 0) {
                // 第一级表头
                List<String> firstHeaderTexts = formatInfo.getOriginalFirstHeaderTexts();
                if (firstHeaderTexts != null && colIndex < firstHeaderTexts.size()) {
                    String text = firstHeaderTexts.get(colIndex);
                    return text != null ? text.trim() : "";
                }
            } else if (headerRowIndex == 1) {
                // 第二级表头
                List<String> secondHeaderTexts = formatInfo.getOriginalSecondHeaderTexts();
                if (secondHeaderTexts != null && colIndex < secondHeaderTexts.size()) {
                    String text = secondHeaderTexts.get(colIndex);
                    return text != null ? text.trim() : "";
                }
            }
        } catch (Exception e) {
            log.warn("获取原始表头文本失败: {}", e.getMessage());
        }

        return "";
    }

    /**
     * 水平合并单元格
     */
    private static void mergeCellsHorizontally(XWPFTableRow row, int fromCol, int toCol) {
        if (fromCol >= toCol || fromCol < 0 || toCol >= row.getTableCells().size()) {
            return;
        }

        try {
            // 获取要合并的单元格
            XWPFTableCell firstCell = row.getTableCells().get(fromCol);

            // 设置第一个单元格的合并属性
            if (firstCell.getCTTc().getTcPr() == null) {
                firstCell.getCTTc().addNewTcPr();
            }

            // 设置水平合并的起始单元格
            firstCell.getCTTc().getTcPr().addNewHMerge().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);

            // 设置后续单元格为合并继续
            for (int i = fromCol + 1; i <= toCol; i++) {
                XWPFTableCell cell = row.getTableCells().get(i);
                if (cell.getCTTc().getTcPr() == null) {
                    cell.getCTTc().addNewTcPr();
                }
                cell.getCTTc().getTcPr().addNewHMerge().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

                // 清空合并继续的单元格内容
                cell.removeParagraph(0);
                cell.addParagraph();
            }

        } catch (Exception e) {
            log.error("合并单元格失败: ", e);
        }
    }

    /**
     * 创建带格式的数据行
     */
    private static void createFormattedDataRows(XWPFTable table, List<Map<String, Object>> tableData,
                                              List<String> columnNames, ExcelDataServiceImpl.TableFormatInfo formatInfo) {
        List<List<ExcelDataServiceImpl.CellFormatInfo>> dataFormats = formatInfo.getDataFormats();

        // 检查是否是重点费用表（第一列是"重点费用"）
        boolean isKeyExpensesTable = !columnNames.isEmpty() && "重点费用".equals(columnNames.get(0));

        for (int dataRowIndex = 0; dataRowIndex < tableData.size(); dataRowIndex++) {
            Map<String, Object> rowData = tableData.get(dataRowIndex);
            XWPFTableRow row = table.createRow();

            // 确保行有足够的单元格
            while (row.getTableCells().size() < columnNames.size()) {
                row.addNewTableCell();
            }

            // 获取该行的格式信息
            List<ExcelDataServiceImpl.CellFormatInfo> rowFormats = dataRowIndex < dataFormats.size() ?
                dataFormats.get(dataRowIndex) : new ArrayList<>();

            // 填充单元格数据并应用格式
            for (int colIndex = 0; colIndex < columnNames.size() && colIndex < row.getTableCells().size(); colIndex++) {
                XWPFTableCell cell = row.getTableCells().get(colIndex);
                Object value = rowData.get(columnNames.get(colIndex));
                ExcelDataServiceImpl.CellFormatInfo cellFormat = colIndex < rowFormats.size() ?
                    rowFormats.get(colIndex) : new ExcelDataServiceImpl.CellFormatInfo();

                // 重点费用表第一列特殊处理
                if (isKeyExpensesTable && colIndex == 0) {
                    // 第一列只在第一行显示"重点费用"，其他行为空但保持合并
                    if (dataRowIndex == 0) {
                        setCellContent(cell, "重点费用", cellFormat, false);
                        // 设置垂直合并开始
                        if (cell.getCTTc().getTcPr() == null) {
                            cell.getCTTc().addNewTcPr();
                        }
                        cell.getCTTc().getTcPr().addNewVMerge().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
                    } else {
                        // 其他行设置为垂直合并继续
                        setCellContent(cell, "", cellFormat, false);
                        if (cell.getCTTc().getTcPr() == null) {
                            cell.getCTTc().addNewTcPr();
                        }
                        cell.getCTTc().getTcPr().addNewVMerge().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
                    }
                } else {
                    setCellContent(cell, value != null ? value.toString() : "", cellFormat, false);
                }
            }
        }
    }

    /**
     * 设置单元格内容和格式
     */
    private static void setCellContent(XWPFTableCell cell, String content,
                                     ExcelDataServiceImpl.CellFormatInfo formatInfo, boolean isHeader) {
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        XWPFRun run = paragraph.createRun();
        run.setText(content);

        // 应用字体格式
        if (formatInfo.isBold() || isHeader) {
            run.setBold(true);
        }
        if (formatInfo.isItalic()) {
            run.setItalic(true);
        }
        if (formatInfo.getFontSize() > 0) {
            run.setFontSize(formatInfo.getFontSize());
        } else {
            run.setFontSize(isHeader ? 11 : 10); // 默认字体大小
        }
        if (formatInfo.getFontName() != null && !formatInfo.getFontName().isEmpty()) {
            run.setFontFamily(formatInfo.getFontName());
        }
        if (formatInfo.getFontColor() != null && !formatInfo.getFontColor().isEmpty()) {
            // 移除#号并设置颜色
            String color = formatInfo.getFontColor().replace("#", "");
            run.setColor(color);
        }

        // 设置段落对齐方式
        String alignment = formatInfo.getAlignment();
        if ("项目".equals(content) || "预算项目".equals(content)) {
            paragraph.setAlignment(ParagraphAlignment.LEFT);
        } else if ("行次".equals(content)) {
            paragraph.setAlignment(ParagraphAlignment.CENTER);
        } else if ("center".equals(alignment)) {
            paragraph.setAlignment(ParagraphAlignment.CENTER);
        } else if ("left".equals(alignment)) {
            paragraph.setAlignment(ParagraphAlignment.LEFT);
        } else {
            paragraph.setAlignment(ParagraphAlignment.RIGHT);
        }

        // 设置单元格垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        // 设置背景色
        if (formatInfo.getBackgroundColor() != null && !formatInfo.getBackgroundColor().isEmpty()) {
            setCellBackgroundColor(cell, formatInfo.getBackgroundColor());
        }

        // 设置边框
        setCellBorders(cell);
    }

    /**
     * 设置单元格背景色
     */
    private static void setCellBackgroundColor(XWPFTableCell cell, String backgroundColor) {
        try {
            // 移除#号
            String color = backgroundColor.replace("#", "");

            // 获取或创建单元格属性
            if (cell.getCTTc().getTcPr() == null) {
                cell.getCTTc().addNewTcPr();
            }

            // 设置背景色
            if (cell.getCTTc().getTcPr().getShd() == null) {
                cell.getCTTc().getTcPr().addNewShd();
            }

            cell.getCTTc().getTcPr().getShd().setFill(color);
            cell.getCTTc().getTcPr().getShd().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STShd.CLEAR);

        } catch (Exception e) {
            log.warn("设置单元格背景色失败: {}", e.getMessage());
        }
    }

    /**
     * 设置单元格边框
     */
    private static void setCellBorders(XWPFTableCell cell) {
        try {
            // 获取或创建单元格属性
            if (cell.getCTTc().getTcPr() == null) {
                cell.getCTTc().addNewTcPr();
            }

            // 获取或创建边框属性
            if (cell.getCTTc().getTcPr().getTcBorders() == null) {
                cell.getCTTc().getTcPr().addNewTcBorders();
            }

            // 设置所有边框为细线
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border =
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            border.setSz(java.math.BigInteger.valueOf(4)); // 边框粗细
            border.setColor("000000"); // 黑色边框

            // 应用到所有边框
            cell.getCTTc().getTcPr().getTcBorders().setTop(border);
            cell.getCTTc().getTcPr().getTcBorders().setBottom(border);
            cell.getCTTc().getTcPr().getTcBorders().setLeft(border);
            cell.getCTTc().getTcPr().getTcBorders().setRight(border);

        } catch (Exception e) {
            log.warn("设置单元格边框失败: {}", e.getMessage());
        }
    }

    /**
     * 将文档写入输出流
     *
     * @param document       Word文档对象
     * @param outputStream 输出流
     * @throws IOException 如果写入失败
     */
    public static void writeDocument(XWPFDocument document, OutputStream outputStream) throws IOException {
        document.write(outputStream);
    }
}
