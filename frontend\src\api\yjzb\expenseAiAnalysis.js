import request from '@/axios';

/**
 * 办公费用AI解读分析API
 */

// 获取分析提示词预览（含业务规则替换）
export const getExpensePromptPreview = (indicatorId, analysisType) => {
  return request({
    url: '/yjzb/expense-ai-analysis/prompt-preview',
    method: 'get',
    params: { indicatorId, analysisType }
  });
};

// 获取指标关联的业务知识列表
export const getBusinessRulesList = (indicatorId) => {
  return request({
    url: '/yjzb/expense-ai-analysis/business-rules',
    method: 'get',
    params: { indicatorId }
  });
};


// 执行AI分析
export const executeExpenseAnalysis = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/execute',
    method: 'post',
    params: params
  });
};

// 获取AI分析结果
export const getExpenseAnalysisResult = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/result',
    method: 'get',
    params: params
  });
};

// 检查是否有缓存结果
export const hasCachedExpenseAnalysis = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/has-cache',
    method: 'get',
    params: params
  });
};

// 执行费用预测
export const executeExpenseForecast = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/forecast',
    method: 'post',
    params: params
  });
};

// 获取预测结果
export const getExpenseForecastResult = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/forecast-result',
    method: 'get',
    params: params
  });
};

// 只预测下一个月的办公费用
export const forecastNextMonth = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/forecast-next-month',
    method: 'post',
    params: params
  });
};

// 获取最新的总体预测结果
export const getLatestTotalForecast = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/latest-total-forecast',
    method: 'get',
    params: params
  });
};

// 获取最新的分类预测结果
export const getLatestCategoryForecasts = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/latest-category-forecasts',
    method: 'get',
    params: params
  });
};

// 获取指定期间的所有分类预测结果
export const getCategoryForecastsByPeriod = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/category-forecasts-by-period',
    method: 'get',
    params: params
  });
};

// 获取年度预算数据
export const getAnnualBudget = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/annual-budget',
    method: 'get',
    params: params
  });
};

// 获取当前累计值（真实数据）
export const getCurrentCumulative = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/current-cumulative',
    method: 'get',
    params: params
  });
};

// 获取预测累计值
export const getForecastCumulative = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/forecast-cumulative',
    method: 'get',
    params: params
  });
};

// 获取指定期间的总预测值
export const getTotalForecastByPeriod = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/total-forecast-by-period',
    method: 'get',
    params: params
  });
};

// 获取指定期间的所有分类预测值
export const getCategoryForecastsByPeriodFromMapper = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/category-forecasts-by-period-mapper',
    method: 'get',
    params: params
  });
};

// 获取指定月份的预测值（不累计）
export const getMonthlyForecastValue = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/monthly-forecast-value',
    method: 'get',
    params: params
  });
};

// 获取指定月份、分类的预测值（不累计）
export const getMonthlyForecastValueByCategory = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/monthly-forecast-value-by-category',
    method: 'get',
    params: params
  });
};

// 获取分类月度实际累计（到指定月为止）
export const getCategoryMonthlyActuals = (params) => {
  return request({
    url: '/yjzb/expense-ai-analysis/category-monthly-actuals',
    method: 'get',
    params: params
  });
};

// 获取指标数据明细
export const getIndicatorValuesDetail = (params) => {
  return request({
    url: '/yjzb/indicatorValuesDetail/by-indicator-period',
    method: 'get',
    params: params
  });
};