/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.modules.yjzb.service.IExcelDataService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Excel数据解析服务实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class ExcelDataServiceImpl implements IExcelDataService {

    // 内存缓存，实际项目中可以使用Redis
    private final Map<String, Map<String, Object>> dataCache = new ConcurrentHashMap<>();

    /**
     * 单元格格式信息
     */
    public static class CellFormatInfo {
        private String backgroundColor;  // 背景色 (RGB hex)
        private String fontColor;       // 字体颜色 (RGB hex)
        private boolean bold;           // 是否粗体
        private boolean italic;         // 是否斜体
        private int fontSize;           // 字体大小
        private String fontName;        // 字体名称
        private String borderStyle;     // 边框样式
        private String alignment;       // 对齐方式
        private boolean merged;         // 是否为合并单元格
        private int mergedRowSpan;      // 合并行数
        private int mergedColSpan;      // 合并列数

        // Getters and Setters
        public String getBackgroundColor() { return backgroundColor; }
        public void setBackgroundColor(String backgroundColor) { this.backgroundColor = backgroundColor; }
        public String getFontColor() { return fontColor; }
        public void setFontColor(String fontColor) { this.fontColor = fontColor; }
        public boolean isBold() { return bold; }
        public void setBold(boolean bold) { this.bold = bold; }
        public boolean isItalic() { return italic; }
        public void setItalic(boolean italic) { this.italic = italic; }
        public int getFontSize() { return fontSize; }
        public void setFontSize(int fontSize) { this.fontSize = fontSize; }
        public String getFontName() { return fontName; }
        public void setFontName(String fontName) { this.fontName = fontName; }
        public String getBorderStyle() { return borderStyle; }
        public void setBorderStyle(String borderStyle) { this.borderStyle = borderStyle; }
        public String getAlignment() { return alignment; }
        public void setAlignment(String alignment) { this.alignment = alignment; }
        public boolean isMerged() { return merged; }
        public void setMerged(boolean merged) { this.merged = merged; }
        public int getMergedRowSpan() { return mergedRowSpan; }
        public void setMergedRowSpan(int mergedRowSpan) { this.mergedRowSpan = mergedRowSpan; }
        public int getMergedColSpan() { return mergedColSpan; }
        public void setMergedColSpan(int mergedColSpan) { this.mergedColSpan = mergedColSpan; }
    }

    /**
     * 表格格式信息
     */
    public static class TableFormatInfo {
        private List<List<CellFormatInfo>> headerFormats;  // 表头格式信息（支持多级表头）
        private List<List<CellFormatInfo>> dataFormats;    // 数据格式信息
        private List<String> columnNames;                  // 列名
        private int headerRowCount;                        // 表头行数
        private List<String> originalFirstHeaderTexts;    // 原始第一级表头文本
        private List<String> originalSecondHeaderTexts;   // 原始第二级表头文本

        public TableFormatInfo() {
            this.headerFormats = new ArrayList<>();
            this.dataFormats = new ArrayList<>();
            this.columnNames = new ArrayList<>();
            this.originalFirstHeaderTexts = new ArrayList<>();
            this.originalSecondHeaderTexts = new ArrayList<>();
        }

        // Getters and Setters
        public List<List<CellFormatInfo>> getHeaderFormats() { return headerFormats; }
        public void setHeaderFormats(List<List<CellFormatInfo>> headerFormats) { this.headerFormats = headerFormats; }
        public List<List<CellFormatInfo>> getDataFormats() { return dataFormats; }
        public void setDataFormats(List<List<CellFormatInfo>> dataFormats) { this.dataFormats = dataFormats; }
        public List<String> getColumnNames() { return columnNames; }
        public void setColumnNames(List<String> columnNames) { this.columnNames = columnNames; }
        public int getHeaderRowCount() { return headerRowCount; }
        public void setHeaderRowCount(int headerRowCount) { this.headerRowCount = headerRowCount; }
        public List<String> getOriginalFirstHeaderTexts() { return originalFirstHeaderTexts; }
        public void setOriginalFirstHeaderTexts(List<String> originalFirstHeaderTexts) { this.originalFirstHeaderTexts = originalFirstHeaderTexts; }
        public List<String> getOriginalSecondHeaderTexts() { return originalSecondHeaderTexts; }
        public void setOriginalSecondHeaderTexts(List<String> originalSecondHeaderTexts) { this.originalSecondHeaderTexts = originalSecondHeaderTexts; }
    }

    @Override
    public Map<String, Object> parseExcelData(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            // 解析各个工作表
            result.put("taxProfitData", parseTaxProfitSheet(workbook));
            result.put("threeExpensesData", parseThreeExpensesSheet(workbook));
            result.put("keyExpensesData", parseKeyExpensesSheet(workbook));
            result.put("capitalData", parseCapitalSheet(workbook));

            // 提取格式信息
            result.put("mainEconomicIndicatorsFormat", parseSheetDataWithFormat(workbook.getSheetAt(0), 0, 0, 1));
            if (workbook.getNumberOfSheets() > 1) {
                result.put("threeExpensesFormat", parseSheetDataWithFormat(workbook.getSheetAt(1), 0, 1, 2));
            }
            if (workbook.getNumberOfSheets() > 2) {
                result.put("keyExpensesFormat", parseSheetDataWithFormat(workbook.getSheetAt(2), 0, 1, 2));
            }
            if (workbook.getNumberOfSheets() > 3) {
                result.put("capitalSheetFormat", parseSheetDataWithFormat(workbook.getSheetAt(3), 0, 1, 2));
            }

            log.info("Excel数据解析完成，共解析{}个工作表，包含格式信息", result.size());

        } catch (IOException e) {
            log.error("解析Excel文件失败", e);
            throw new RuntimeException("解析Excel文件失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 解析税利表数据
     */
    private List<Map<String, Object>> parseTaxProfitSheet(Workbook workbook) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 假设税利表在第一个工作表
        Sheet sheet = workbook.getSheetAt(0);

        return parseSheetData(sheet, 0, 1);
    }

    /**
     * 解析三项费用表数据
     */
    private List<Map<String, Object>> parseThreeExpensesSheet(Workbook workbook) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 假设三项费用表在第二个工作表
        if (workbook.getNumberOfSheets() < 2) {
            return data;
        }

        Sheet sheet = workbook.getSheetAt(1);
        return parseSheetDataWithTwoLevelHeaders(sheet, 0, 1, 2);
    }

    /**
     * 解析重点费用表数据
     */
    private List<Map<String, Object>> parseKeyExpensesSheet(Workbook workbook) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 假设重点费用表在第三个工作表
        if (workbook.getNumberOfSheets() < 3) {
            return data;
        }

        Sheet sheet = workbook.getSheetAt(2);
        return parseSheetDataWithTwoLevelHeaders(sheet, 0, 1, 2);
    }

    /**
     * 解析资本表数据
     */
    private List<Map<String, Object>> parseCapitalSheet(Workbook workbook) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 假设资本表在第四个工作表
        if (workbook.getNumberOfSheets() < 4) {
            return data;
        }

        Sheet sheet = workbook.getSheetAt(3);
        return parseSheetDataWithTwoLevelHeaders(sheet, 0, 1, 2);
    }

    /**
     * 通用的工作表数据解析方法
     */
    private List<Map<String, Object>> parseSheetData(Sheet sheet, int headerIndex, int dataStartIndex) {
        List<Map<String, Object>> data = new ArrayList<>();

        if (sheet == null) {
            return data;
        }

        // 解析表头，处理合并单元格
        Row headerRow = sheet.getRow(headerIndex);
        if (headerRow == null) {
            return data;
        }

        int maxCells = headerRow.getLastCellNum();
        String[] headerValues = getMergedCellValues(sheet, headerIndex, maxCells);

        List<String> headers = new ArrayList<>();
        for (String headerValue : headerValues) {
            headers.add(headerValue);
        }

        // 解析数据行
        for (int i = dataStartIndex; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            Map<String, Object> rowData = new HashMap<>();
            for (int j = 0; j < headers.size() && j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j);
                String header = headers.get(j);
                Object value = getCellValue(cell);
                rowData.put(header, value);
            }

            if (!rowData.isEmpty() && rowData.values().stream().anyMatch(Objects::nonNull)) {
                data.add(rowData);
            }
        }

        return data;
    }

    /**
     * 处理二级表头的工作表数据解析方法
     * @param sheet 工作表
     * @param firstHeaderIndex 第一级表头行索引
     * @param secondHeaderIndex 第二级表头行索引
     * @param dataStartIndex 数据开始行索引
     */
    private List<Map<String, Object>> parseSheetDataWithTwoLevelHeaders(Sheet sheet, int firstHeaderIndex, int secondHeaderIndex, int dataStartIndex) {
        List<Map<String, Object>> data = new ArrayList<>();

        if (sheet == null) {
            return data;
        }

        // 解析第一级表头
        Row firstHeaderRow = sheet.getRow(firstHeaderIndex);
        Row secondHeaderRow = sheet.getRow(secondHeaderIndex);

        if (firstHeaderRow == null || secondHeaderRow == null) {
            return data;
        }

        // 构建合并的表头，处理合并单元格
        List<String> headers = new ArrayList<>();
        int maxCells = Math.max(firstHeaderRow.getLastCellNum(), secondHeaderRow.getLastCellNum());

        // 处理合并单元格后的表头值
        String[] firstHeaderValues = getMergedCellValues(sheet, firstHeaderIndex, maxCells);
        String[] secondHeaderValues = getMergedCellValues(sheet, secondHeaderIndex, maxCells);

        for (int i = 0; i < maxCells; i++) {
            String firstHeader = firstHeaderValues[i];
            String secondHeader = secondHeaderValues[i];

            // 拼接第一行和第二行的文本作为表头
            String combinedHeader = combineHeaders(firstHeader, secondHeader);
            headers.add(combinedHeader);
        }

        // 解析数据行
        for (int i = dataStartIndex; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            Map<String, Object> rowData = new HashMap<>();
            for (int j = 0; j < headers.size() && j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j);
                String header = headers.get(j);
                Object value = getCellValue(cell);
                rowData.put(header, value);
            }

            if (!rowData.isEmpty() && rowData.values().stream().anyMatch(Objects::nonNull)) {
                data.add(rowData);
            }
        }

        return data;
    }

    /**
     * 合并两级表头文本
     */
    private String combineHeaders(String firstHeader, String secondHeader) {
        // 清理空白字符
        firstHeader = firstHeader != null ? firstHeader.trim() : "";
        secondHeader = secondHeader != null ? secondHeader.trim() : "";

        // 如果第一级表头为空，只使用第二级表头
        if (firstHeader.isEmpty()) {
            return secondHeader;
        }

        // 如果第二级表头为空，只使用第一级表头
        if (secondHeader.isEmpty()) {
            return firstHeader;
        }

        // 如果两个表头相同，只返回一个
        if (firstHeader.equals(secondHeader) && ("预算项目".equals(secondHeader) || "项目".equals(secondHeader) || "行次".equals(secondHeader))) {
            return firstHeader;
        }

        // 拼接两级表头，用连接符分隔
        return firstHeader + "-" + secondHeader;
    }

    /**
     * 获取指定行的合并单元格值数组
     * @param sheet 工作表
     * @param rowIndex 行索引
     * @param maxCells 最大列数
     * @return 处理合并单元格后的值数组
     */
    private String[] getMergedCellValues(Sheet sheet, int rowIndex, int maxCells) {
        String[] values = new String[maxCells];
        Row row = sheet.getRow(rowIndex);

        if (row == null) {
            // 如果行不存在，返回空字符串数组
            for (int i = 0; i < maxCells; i++) {
                values[i] = "";
            }
            return values;
        }

        // 首先获取所有单元格的原始值
        for (int i = 0; i < maxCells; i++) {
            Cell cell = row.getCell(i);
            values[i] = getCellValueAsString(cell);
        }

        // 处理合并单元格
        for (int mergedRegion = 0; mergedRegion < sheet.getNumMergedRegions(); mergedRegion++) {
            org.apache.poi.ss.util.CellRangeAddress range = sheet.getMergedRegion(mergedRegion);

            // 检查合并区域是否包含当前行
            if (range.getFirstRow() <= rowIndex && rowIndex <= range.getLastRow()) {
                // 获取合并区域的第一个单元格的值
                Cell firstCell = sheet.getRow(range.getFirstRow()).getCell(range.getFirstColumn());
                String mergedValue = getCellValueAsString(firstCell);

                // 将合并单元格的值应用到该行的所有相关列
                for (int col = range.getFirstColumn(); col <= range.getLastColumn() && col < maxCells; col++) {
                    values[col] = mergedValue;
                }
            }
        }

        return values;
    }

    /**
     * 获取单元格值（获取可见值，而不是原始值）
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        // 使用DataFormatter获取格式化后的可见值
        DataFormatter dataFormatter = new DataFormatter();
        FormulaEvaluator formulaEvaluator = cell.getSheet().getWorkbook().getCreationHelper().createFormulaEvaluator();

        try {
            // 对于公式单元格，先计算公式结果
            if (cell.getCellType() == CellType.FORMULA) {
                CellValue cellValue = formulaEvaluator.evaluate(cell);

                switch (cellValue.getCellType()) {
                    case STRING:
                        return cellValue.getStringValue();
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            return cell.getDateCellValue();
                        } else {
                            // 使用DataFormatter获取格式化后的数值显示
                            String formattedValue = dataFormatter.formatCellValue(cell, formulaEvaluator);
                            return formattedValue;
                        }
                    case BOOLEAN:
                        return cellValue.getBooleanValue();
                    default:
                        return dataFormatter.formatCellValue(cell, formulaEvaluator);
                }
            } else {
                // 非公式单元格的处理
                switch (cell.getCellType()) {
                    case STRING:
                        return cell.getStringCellValue();
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            return cell.getDateCellValue();
                        } else {
                            // 使用DataFormatter获取格式化后的数值显示
                            String formattedValue = dataFormatter.formatCellValue(cell);
                            return formattedValue;
                        }
                    case BOOLEAN:
                        return cell.getBooleanCellValue();
                    default:
                        return dataFormatter.formatCellValue(cell);
                }
            }
        } catch (Exception e) {
            log.warn("获取单元格可见值失败，回退到原始值: {}", e.getMessage());
            // 回退到原始实现
            return getCellValueFallback(cell);
        }
    }

    /**
     * 回退方法：获取单元格原始值
     */
    private Object getCellValueFallback(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    // 数值保留2位小数
                    double numericValue = cell.getNumericCellValue();
                    return BigDecimal.valueOf(numericValue).setScale(2, RoundingMode.HALF_UP);
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                try {
                    // 公式结果如果是数值，也保留2位小数
                    double numericValue = cell.getNumericCellValue();
                    return BigDecimal.valueOf(numericValue).setScale(2, RoundingMode.HALF_UP);
                } catch (Exception e) {
                    return cell.getStringCellValue();
                }
            default:
                return null;
        }
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        Object value = getCellValue(cell);
        return value != null ? value.toString() : "";
    }

    /**
     * 提取单元格格式信息
     */
    private CellFormatInfo extractCellFormatInfo(Cell cell, Sheet sheet) {
        CellFormatInfo formatInfo = new CellFormatInfo();

        if (cell == null) {
            return formatInfo;
        }

        CellStyle cellStyle = cell.getCellStyle();
        Font font = cell.getSheet().getWorkbook().getFontAt(cellStyle.getFontIndex());

        // 提取字体信息
        formatInfo.setBold(font.getBold());
        formatInfo.setItalic(font.getItalic());
        formatInfo.setFontSize(font.getFontHeightInPoints());
        formatInfo.setFontName(font.getFontName());

        // 提取对齐方式
        HorizontalAlignment alignment = cellStyle.getAlignment();
        switch (alignment) {
            case CENTER:
                formatInfo.setAlignment("center");
                break;
            case RIGHT:
                formatInfo.setAlignment("right");
                break;
            case LEFT:
            default:
                formatInfo.setAlignment("left");
                break;
        }

        // 提取颜色信息（仅适用于XSSF格式）
        if (cell instanceof org.apache.poi.xssf.usermodel.XSSFCell) {
            org.apache.poi.xssf.usermodel.XSSFCell xssfCell = (org.apache.poi.xssf.usermodel.XSSFCell) cell;
            org.apache.poi.xssf.usermodel.XSSFCellStyle xssfCellStyle = xssfCell.getCellStyle();

            // 背景色
            if (xssfCellStyle.getFillForegroundXSSFColor() != null) {
                org.apache.poi.xssf.usermodel.XSSFColor bgColor = xssfCellStyle.getFillForegroundXSSFColor();
                if (bgColor.getRGB() != null) {
                    formatInfo.setBackgroundColor(rgbToHex(bgColor.getRGB()));
                }
            }

            // 字体颜色
            if (font instanceof org.apache.poi.xssf.usermodel.XSSFFont) {
                org.apache.poi.xssf.usermodel.XSSFFont xssfFont = (org.apache.poi.xssf.usermodel.XSSFFont) font;
                if (xssfFont.getXSSFColor() != null && xssfFont.getXSSFColor().getRGB() != null) {
                    formatInfo.setFontColor(rgbToHex(xssfFont.getXSSFColor().getRGB()));
                }
            }
        }

        // 检查是否为合并单元格
        int rowIndex = cell.getRowIndex();
        int colIndex = cell.getColumnIndex();

        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            org.apache.poi.ss.util.CellRangeAddress range = sheet.getMergedRegion(i);
            if (range.isInRange(rowIndex, colIndex)) {
                formatInfo.setMerged(true);
                formatInfo.setMergedRowSpan(range.getLastRow() - range.getFirstRow() + 1);
                formatInfo.setMergedColSpan(range.getLastColumn() - range.getFirstColumn() + 1);
                break;
            }
        }

        return formatInfo;
    }

    /**
     * 将RGB字节数组转换为十六进制字符串
     */
    private String rgbToHex(byte[] rgb) {
        if (rgb == null || rgb.length < 3) {
            return null;
        }
        return String.format("#%02X%02X%02X",
            rgb[0] & 0xFF,
            rgb[1] & 0xFF,
            rgb[2] & 0xFF);
    }

    /**
     * 解析工作表数据并提取格式信息（支持单级和二级表头）
     */
    public TableFormatInfo parseSheetDataWithFormat(Sheet sheet, int firstHeaderIndex, int secondHeaderIndex, int dataStartIndex) {
        TableFormatInfo tableFormatInfo = new TableFormatInfo();

        if (sheet == null) {
            return tableFormatInfo;
        }

        // 解析表头
        Row firstHeaderRow = sheet.getRow(firstHeaderIndex);
        Row secondHeaderRow = null;
        boolean isTwoLevelHeader = false;

        if (firstHeaderRow == null) {
            return tableFormatInfo;
        }

        // 检查是否有第二级表头
        if (secondHeaderIndex >= 0 && secondHeaderIndex != firstHeaderIndex) {
            secondHeaderRow = sheet.getRow(secondHeaderIndex);
            if (secondHeaderRow != null) {
                isTwoLevelHeader = true;
            }
        }

        int maxCells = firstHeaderRow.getLastCellNum();
        if (isTwoLevelHeader && secondHeaderRow != null) {
            maxCells = Math.max(maxCells, secondHeaderRow.getLastCellNum());
        }

        // 提取表头格式信息和原始文本
        List<CellFormatInfo> firstHeaderFormats = new ArrayList<>();
        List<CellFormatInfo> secondHeaderFormats = new ArrayList<>();
        List<String> firstHeaderTexts = new ArrayList<>();
        List<String> secondHeaderTexts = new ArrayList<>();

        for (int i = 0; i < maxCells; i++) {
            Cell firstCell = firstHeaderRow.getCell(i);
            Cell secondCell = isTwoLevelHeader ? secondHeaderRow.getCell(i) : null;

            // 提取格式信息
            firstHeaderFormats.add(extractCellFormatInfo(firstCell, sheet));
            if (isTwoLevelHeader) {
                secondHeaderFormats.add(extractCellFormatInfo(secondCell, sheet));
            }

            // 提取原始文本（不使用合并单元格处理）
            firstHeaderTexts.add(getCellValueAsString(firstCell));
            if (isTwoLevelHeader) {
                secondHeaderTexts.add(getCellValueAsString(secondCell));
            }
        }

        tableFormatInfo.getHeaderFormats().add(firstHeaderFormats);
        if (isTwoLevelHeader) {
            tableFormatInfo.getHeaderFormats().add(secondHeaderFormats);
            tableFormatInfo.setHeaderRowCount(2);
        } else {
            tableFormatInfo.setHeaderRowCount(1);
        }

        // 保存原始表头文本信息
        tableFormatInfo.setOriginalFirstHeaderTexts(firstHeaderTexts);
        tableFormatInfo.setOriginalSecondHeaderTexts(secondHeaderTexts);

        // 合并表头文本用于数据映射
        String[] firstHeaderValues = getMergedCellValues(sheet, firstHeaderIndex, maxCells);
        String[] secondHeaderValues = isTwoLevelHeader ? getMergedCellValues(sheet, secondHeaderIndex, maxCells) : new String[maxCells];

        List<String> headers = new ArrayList<>();
        for (int i = 0; i < maxCells; i++) {
            String firstHeader = i < firstHeaderValues.length ? firstHeaderValues[i] : "";
            String secondHeader = isTwoLevelHeader && i < secondHeaderValues.length ? secondHeaderValues[i] : "";

            if (isTwoLevelHeader) {
                headers.add(combineHeaders(firstHeader, secondHeader));
            } else {
                headers.add(firstHeader); // 单级表头直接使用第一级表头
            }
        }

        tableFormatInfo.setColumnNames(headers);

        // 解析数据行并提取格式信息
        for (int i = dataStartIndex; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            List<CellFormatInfo> rowFormats = new ArrayList<>();
            boolean hasData = false;

            for (int j = 0; j < headers.size() && j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j);
                rowFormats.add(extractCellFormatInfo(cell, sheet));

                if (cell != null && getCellValue(cell) != null) {
                    hasData = true;
                }
            }

            if (hasData) {
                tableFormatInfo.getDataFormats().add(rowFormats);
            }
        }

        return tableFormatInfo;
    }



    @Override
    public List<Map<String, Object>> getMainEconomicIndicatorsFromExcel(Map<String, Object> excelData, Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> taxProfitData = (List<Map<String, Object>>) excelData.get("taxProfitData");

        if (taxProfitData == null) {
            return new ArrayList<>();
        }

        // 转换为符合现有接口格式的数据
        return convertToMainEconomicIndicators(taxProfitData, params);
    }

    @Override
    public List<Map<String, Object>> getThreeExpensesFromExcel(Map<String, Object> excelData, Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> threeExpensesData = (List<Map<String, Object>>) excelData.get("threeExpensesData");

        if (threeExpensesData == null) {
            return new ArrayList<>();
        }

        return convertToThreeExpenses(threeExpensesData, params);
    }

    @Override
    public List<Map<String, Object>> getKeyExpensesFromExcel(Map<String, Object> excelData, Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> keyExpensesData = (List<Map<String, Object>>) excelData.get("keyExpensesData");

        if (keyExpensesData == null) {
            return new ArrayList<>();
        }

        return convertToKeyExpenses(keyExpensesData, params);
    }

    @Override
    public List<Map<String, Object>> getOnlyThreeExpensesFromExcel(Map<String, Object> excelData, Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> threeExpensesData = (List<Map<String, Object>>) excelData.get("threeExpensesData");

        if (threeExpensesData == null) {
            return new ArrayList<>();
        }

        return convertToOnlyThreeExpenses(threeExpensesData, params);
    }

    @Override
    public List<Map<String, Object>> getCapitalFromExcel(Map<String, Object> excelData, Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> capitalData = (List<Map<String, Object>>) excelData.get("capitalData");

        if (capitalData == null) {
            return new ArrayList<>();
        }

        return convertToCapital(capitalData, params);
    }

    @Override
    public void cacheExcelData(String key, Map<String, Object> data) {
        dataCache.put(key, data);
    }

    @Override
    public Map<String, Object> getCachedExcelData(String key) {
        return dataCache.get(key);
    }

    @Override
    public void clearCachedExcelData(String key) {
        dataCache.remove(key);
    }

    /**
     * 转换为主要经济指标格式
     */
    private List<Map<String, Object>> convertToMainEconomicIndicators(List<Map<String, Object>> rawData, Map<String, Object> params) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (rawData == null || rawData.isEmpty()) {
            log.warn("convertToMainEconomicIndicators: 原始数据为空");
            return result;
        }

        // 添加调试日志
        log.debug("convertToMainEconomicIndicators: 原始数据行数: {}", rawData.size());
        if (!rawData.isEmpty()) {
            log.debug("convertToMainEconomicIndicators: 第一行数据的键: {}", rawData.get(0).keySet());
        }

        Integer queryYear = (Integer) params.get("queryYear");
        Integer compareYear = (Integer) params.get("compareYear");
        Integer startMonth = (Integer) params.get("startMonth");
        Integer endMonth = (Integer) params.get("endMonth");

        // 构建期间标识
        String currentPeriod = buildPeriodString(queryYear, startMonth, endMonth);
        String comparePeriod = buildPeriodString(compareYear, startMonth, endMonth);
        String budgetPeriod = queryYear + "年预算数";

        for (Map<String, Object> row : rawData) {
            Map<String, Object> convertedRow = new HashMap<>();

            // 尝试多种可能的项目列名
            String itemName = getStringValue(row, "项目");
            convertedRow.put("项目", itemName);
            convertedRow.put(currentPeriod, getNumericValue(row, currentPeriod));
            convertedRow.put(comparePeriod, getNumericValue(row, comparePeriod));
            convertedRow.put(budgetPeriod, getNumericValue(row, budgetPeriod));
            convertedRow.put("同比增减（%）", getNumericValue(row, "同比增减（%）"));
            convertedRow.put("预算执行进度（%）", getNumericValue(row, "预算执行进度（%）"));
            result.add(convertedRow);
        }

        return result;
    }

    /**
     * 转换为三项费用格式
     */
    private List<Map<String, Object>> convertToThreeExpenses(List<Map<String, Object>> rawData, Map<String, Object> params) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (rawData == null || rawData.isEmpty()) {
            log.warn("convertToThreeExpenses: 原始数据为空");
            return result;
        }

        // 添加调试日志
        log.debug("convertToThreeExpenses: 原始数据行数: {}", rawData.size());
        if (!rawData.isEmpty()) {
            log.debug("convertToThreeExpenses: 第一行数据的键: {}", rawData.get(0).keySet());
        }

        Integer queryYear = (Integer) params.get("queryYear");
        Integer compareYear = (Integer) params.get("compareYear");
        Integer startMonth = (Integer) params.get("startMonth");
        Integer endMonth = (Integer) params.get("endMonth");

        String currentPeriod = buildPeriodString(queryYear, startMonth, endMonth);
        String comparePeriod = buildPeriodString(compareYear, startMonth, endMonth);

        String mergedFirstTitle = "三项费用合计-";
        for (Map<String, Object> row : rawData) {
            Map<String, Object> convertedRow = new HashMap<>();

            // 尝试多种可能的项目列名
            convertedRow.put("预算项目", getStringValue(row, "预算项目"));
            convertedRow.put("行次", getNumericValue(row, "行次"));
            convertedRow.put(currentPeriod, getNumericValue(row, mergedFirstTitle + currentPeriod));
            convertedRow.put(comparePeriod, getNumericValue(row, mergedFirstTitle + comparePeriod));
            convertedRow.put("增减额", getNumericValue(row, mergedFirstTitle + "增减额"));
            convertedRow.put("增减比率%", getNumericValue(row, mergedFirstTitle + "增减比率%"));
            // 预算相关字段
            convertedRow.put(queryYear + "年预算数", getNumericValue(row, mergedFirstTitle +  queryYear + "年预算数"));
            convertedRow.put("预算余额", getNumericValue(row, mergedFirstTitle + "预算余额"));
            convertedRow.put("预算执行率%", getNumericValue(row, mergedFirstTitle + "预算执行率%"));
            result.add(convertedRow);
        }

        return result;
    }

    /**
     * 转换为重点费用格式
     */
    private List<Map<String, Object>> convertToKeyExpenses(List<Map<String, Object>> rawData, Map<String, Object> params) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (rawData == null || rawData.isEmpty()) {
            log.warn("convertToKeyExpenses: 原始数据为空");
            return result;
        }

        // 添加调试日志
        log.debug("convertToKeyExpenses: 原始数据行数: {}", rawData.size());
        if (!rawData.isEmpty()) {
            log.debug("convertToKeyExpenses: 第一行数据的键: {}", rawData.get(0).keySet());
        }

        Integer queryYear = (Integer) params.get("queryYear");
        Integer compareYear = (Integer) params.get("compareYear");
        Integer startMonth = (Integer) params.get("startMonth");
        Integer endMonth = (Integer) params.get("endMonth");

        String currentPeriod = buildPeriodString(queryYear, startMonth, endMonth);
        String comparePeriod = buildPeriodString(compareYear, startMonth, endMonth);

        String mergedFirstTitle1 = "实际发生数-";
        String mergedFirstTitle2 = queryYear + "年预算数-";
        for (Map<String, Object> row : rawData) {
            Map<String, Object> convertedRow = new HashMap<>();

            // 添加固定的"重点费用"列
            convertedRow.put("重点费用", "重点费用");

            // 尝试多种可能的项目列名
            convertedRow.put("项目", getStringValue(row, "项目"));
            convertedRow.put(currentPeriod, getNumericValue(row, mergedFirstTitle1 + currentPeriod));
            convertedRow.put(comparePeriod, getNumericValue(row, mergedFirstTitle1 + comparePeriod));
            convertedRow.put("同比增减（%）", getNumericValue(row, mergedFirstTitle1 + "同比增减（%）"));
            // 预算相关字段
            convertedRow.put(queryYear + "年预算数", getNumericValue(row, mergedFirstTitle2 + queryYear + "年预算数"));
            convertedRow.put("执行进度（%）", getNumericValue(row, mergedFirstTitle2 + "执行进度（%）"));
            result.add(convertedRow);
        }

        return result;
    }

    /**
     * 转换为仅三项费用格式
     */
    private List<Map<String, Object>> convertToOnlyThreeExpenses(List<Map<String, Object>> rawData, Map<String, Object> params) {
        // 仅三项费用格式与三项费用类似，但字段可能有所不同
        return convertToThreeExpenses(rawData, params);
    }

    /**
     * 转换为资本表格式
     */
    private List<Map<String, Object>> convertToCapital(List<Map<String, Object>> rawData, Map<String, Object> params) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (rawData == null || rawData.isEmpty()) {
            log.warn("convertToCapital: 原始数据为空");
            return result;
        }

        // 添加调试日志
        log.debug("convertToCapital: 原始数据行数: {}", rawData.size());
        if (!rawData.isEmpty()) {
            log.debug("convertToCapital: 第一行数据的键: {}", rawData.get(0).keySet());
        }

        for (Map<String, Object> row : rawData) {
            Map<String, Object> convertedRow = new HashMap<>();

            // 尝试多种可能的项目列名
            convertedRow.put("项目", getStringValue(row, "项目"));
            convertedRow.put("行次", getNumericValue(row, "行次"));
            convertedRow.put("项目总投资额", getNumericValue(row, "项目总投资额-项目总投资额"));
            convertedRow.put("本年投资预算数", getNumericValue(row, "本年投资预算数-本年投资预算数"));
            convertedRow.put("本年执行数", getNumericValue(row, "本年执行数-本年执行数"));
            convertedRow.put("执行率（%）", getNumericValue(row, "执行率（%）-执行率（%）"));
            result.add(convertedRow);
        }

        return result;
    }

    /**
     * 构建期间字符串
     */
    private String buildPeriodString(Integer year, Integer startMonth, Integer endMonth) {
        if (startMonth.equals(endMonth)) {
            return year + "年" + endMonth + "月";
        } else {
            return year + "年" + startMonth + "-" + endMonth + "月";
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        return value != null ? value.toString().trim() : null;
    }

    /**
     * 安全获取数值
     */
    private String getNumericValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return "";
        }

        if (value instanceof BigDecimal) {
            return value.toString();
        } else if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue()).toString();
        } else if (value instanceof String) {
            try {
                String str = ((String) value).replaceAll(",", "").trim();
                if (str.isEmpty()) {
                    return "";
                }
                return str;
            } catch (NumberFormatException e) {
                return "";
            }
        }

        return "";
    }
}