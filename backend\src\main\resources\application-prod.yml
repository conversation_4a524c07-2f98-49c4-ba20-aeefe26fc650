#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      ##将docker脚本部署的redis服务映射为宿主机ip
      ##生产环境推荐使用阿里云高可用redis服务并设置密码
      host: redis
      port: 6379
      password:
      database: 0
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    url: *****************************************
    username: root
    password: Ystech@2025

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    address: redis://redis:6379
    password:
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html


#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://minio:9000
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://tcinspect.foshantc.com/webfile
  #访问key
  access-key: admin
  #密钥key
  secret-key: admin123456
  #存储桶
  bucket-name: images

# Dify配置
dify:
  api:
    base-url: http://***********
    key: dataset-yIxMQAPhYKzfioH9LRSjZ0bK
    timeout: 30000
    agentkey:
      analysisagent: app-SUtoQASOT4h7OLDXx5e4cXtQ
      totalanalysisagent: app-fxir0ImUo62yVMC3uUgEvPb7
      finance-analysis-key: app-xbPOEIK76J3JlSBjSIWmazGp
      expenseanalysisagent: app-M2XTK5nI78vUdS9GZlUuaVbf

# MLOps配置
mlops:
  predict:
    # MLOps预测服务地址（生产环境）
    url: http://*************:80/mlops-dispatch/predict
    # 预测任务ID（生产环境）
    jobid: cca93b7bca0445b1a25c84d8000dc22f
    # 预测模型ID（生产环境）
    modelid: 1955543232530817025
    # 请求超时时间（毫秒）
    timeout: 30000

  # 支持的文件类型
  supported-file-types:
    - pdf
    - doc
    - docx
    - xls
    - xlsx
    - txt
    - md
    - csv

  # 文件大小限制（字节）
  max-file-size: 52428800  # 50MB
