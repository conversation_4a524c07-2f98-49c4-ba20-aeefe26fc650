package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO;

import java.util.List;
import java.util.Map;

/**
 * 财务分析Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FinanceAnalysisMapper extends BaseMapper<FinanceAnalysisEntity> {

    /**
     * 获取主要经济指标分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 主要经济指标分析数据
     */
    List<Map<String, Object>> selectMainEconomicIndicators(@Param("params") Map<String, Object> params);

    /**
     * 获取三项费用分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 三项费用分析数据
     */
    List<Map<String, Object>> selectThreeExpenses(@Param("params") Map<String, Object> params);

    /**
     * 获取重点费用支出情况分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 重点费用支出情况分析数据
     */
    List<Map<String, Object>> selectKeyExpenses(@Param("params") Map<String, Object> params);

    /**
     * 获取仅三项费用分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 销售费用汇总数据
     */
    List<Map<String, Object>> selectOnlyThreeExpenses(@Param("params") Map<String, Object> params);

//    /**
//     * 获取资本表数据
//     *
//     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
//     * @return 资本表数据
//     */
//    List<Map<String, Object>> selectCapital(@Param("params") Map<String, Object> params);

    /**
     * 分页查询财务分析数据
     *
     * @param page 分页参数
     * @param financeAnalysis 查询条件
     * @return 分页数据
     */
    IPage<FinanceAnalysisVO> selectFinanceAnalysisPage(IPage<FinanceAnalysisVO> page, @Param("financeAnalysis") FinanceAnalysisVO financeAnalysis);
    
}