package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.yjzb.excel.IndicatorForecastExcel;
import org.springblade.modules.yjzb.mapper.IndicatorForecastMapper;
import org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorForecastVO;
import org.springblade.modules.yjzb.service.IIndicatorForecastService;
import org.springblade.modules.yjzb.pojo.dto.IndicatorPredictFeatureDTO;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.util.List;
import java.math.BigDecimal;

@Service
public class IndicatorForecastServiceImpl extends BaseServiceImpl<IndicatorForecastMapper, IndicatorForecastEntity>
        implements IIndicatorForecastService {

    @Autowired
    private org.springblade.modules.yjzb.service.IIndicatorValuesService indicatorValuesService;

    @Autowired
    private org.springblade.modules.yjzb.service.IIndicatorAnnualBudgetService indicatorAnnualBudgetService;

    @Value("${mlops.predict.url:http://*************:80/mlops-dispatch/predict}")
    private String mlopsPredictUrl;

    @Value("${mlops.predict.jobid:cca93b7bca0445b1a25c84d8000dc22f}")
    private String mlopsJobId;

    @Value("${mlops.predict.modelid:1955543232530817025}")
    private String mlopsModelId;

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public IPage<IndicatorForecastVO> selectIndicatorForecastPage(IPage<IndicatorForecastVO> page,
            IndicatorForecastVO query) {
        return page.setRecords(baseMapper.selectIndicatorForecastPage(page, query));
    }

    @Override
    public List<IndicatorForecastExcel> exportIndicatorForecast(Wrapper<IndicatorForecastEntity> queryWrapper) {
        return baseMapper.exportIndicatorForecast(queryWrapper);
    }

    @Override
    public IndicatorForecastEntity findByIndicatorAndYear(Long indicatorId, Integer year) {
        return baseMapper.findByIndicatorAndYear(indicatorId, year);
    }

    /**
     * 一个简单且稳健的基线预测：
     * - 优先使用 ma3（三月均值）；
     * - 若 ma3 为空，回退到 lag1；
     * - 若 lag1 为空，回退到 same_period_last_year；
     * - 再不行则回退到 current_value；
     * - 最终对结果做非负约束。
     */
    @Override
    public BigDecimal predictMonthlyValue(IndicatorPredictFeatureDTO feature) {
        BigDecimal fallback = firstNonNull(
                feature.getMa3Value(),
                feature.getLag1Value(),
                feature.getSamePeriodLastYearValue(),
                feature.getCurrentValue());

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            java.util.Map<String, String> body = new java.util.HashMap<>();
            body.put("jobid", mlopsJobId);
            body.put("modelid", mlopsModelId);
            body.put("id", safeStr(feature.getIndicatorId() + "-" + feature.getPeriod()));
            body.put("indicator_id", safeStr(feature.getIndicatorId()));
            body.put("period", safeStr(feature.getPeriod()));
            body.put("lag1_value", numStr(feature.getLag1Value()));
            body.put("lag2_value", numStr(feature.getLag2Value()));
            body.put("lag3_value", numStr(feature.getLag3Value()));
            body.put("ma3_value", numStr(feature.getMa3Value()));
            body.put("same_period_last_year_value", numStr(feature.getSamePeriodLastYearValue()));
            body.put("current_year_cumulative_value", numStr(feature.getCurrentYearCumulativeValue()));

            HttpEntity<java.util.Map<String, String>> req = new HttpEntity<>(body, headers);
            ResponseEntity<java.util.Map> resp = restTemplate.postForEntity(mlopsPredictUrl, req, java.util.Map.class);

            if (resp.getStatusCode().is2xxSuccessful() && resp.getBody() != null) {
                Object success = resp.getBody().get("success");
                Object data = resp.getBody().get("data");
                if ((success instanceof Boolean && (Boolean) success) && data != null) {
                    BigDecimal pred = parseDecimal(data.toString());
                    if (pred != null) {
                        return pred;
                    }
                }
            }
        } catch (Exception ignore) {
        }

        if (fallback == null || fallback.signum() < 0) {
            return BigDecimal.ZERO;
        }
        return fallback;
    }

    private BigDecimal firstNonNull(BigDecimal... arr) {
        if (arr == null)
            return null;
        for (BigDecimal b : arr) {
            if (b != null)
                return b;
        }
        return null;
    }

    @Override
    public BigDecimal predictByIndicatorAndPeriod(Long indicatorId, String period) {
        // 当前值
        org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity q = new org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity();
        q.setIndicatorId(indicatorId);
        q.setPeriod(period);
        org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity cur = indicatorValuesService
                .getOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>(q));
        java.math.BigDecimal current = cur == null ? null : cur.getValue();

        // 年月解析
        int y = Integer.parseInt(period.substring(0, 4));
        int m = Integer.parseInt(period.substring(5, 7));

        String lag1 = shiftYm(y, m, -1);
        String lag2 = shiftYm(y, m, -2);
        String lag3 = shiftYm(y, m, -3);
        String lastYear = (y - 1) + "-" + String.format("%02d", m);

        java.math.BigDecimal vLag1 = getValue(indicatorId, lag1);
        java.math.BigDecimal vLag2 = getValue(indicatorId, lag2);
        java.math.BigDecimal vLag3 = getValue(indicatorId, lag3);
        java.math.BigDecimal vLastYear = getValue(indicatorId, lastYear);

        java.math.BigDecimal ma3 = null;
        if (current != null && vLag1 != null && vLag2 != null) {
            ma3 = current.add(vLag1).add(vLag2)
                    .divide(new java.math.BigDecimal("3"), java.math.RoundingMode.HALF_UP);
        }

        // 当年累计
        java.math.BigDecimal cumulative = java.math.BigDecimal.ZERO;
        for (int i = 1; i <= m; i++) {
            String p = y + "-" + String.format("%02d", i);
            java.math.BigDecimal vi = getValue(indicatorId, p);
            if (vi != null)
                cumulative = cumulative.add(vi);
        }
        if (cumulative.compareTo(java.math.BigDecimal.ZERO) == 0) {
            cumulative = null;
        }

        // 年度预算
        org.springblade.modules.yjzb.pojo.entity.IndicatorAnnualBudgetEntity bud = indicatorAnnualBudgetService
                .findByIndicatorAndYear(indicatorId, y);
        java.math.BigDecimal budget = bud == null ? null : bud.getCurrentUsed();

        IndicatorPredictFeatureDTO feature = new IndicatorPredictFeatureDTO();
        feature.setIndicatorId(String.valueOf(indicatorId));
        feature.setPeriod(period);
        feature.setCurrentValue(current);
        feature.setLag1Value(vLag1);
        feature.setLag2Value(vLag2);
        feature.setLag3Value(vLag3);
        feature.setMa3Value(ma3);
        feature.setSamePeriodLastYearValue(vLastYear);
        feature.setCurrentYearCumulativeValue(cumulative);
        feature.setAnnualBudgetValue(budget);

        return predictMonthlyValue(feature);
    }

    private String shiftYm(int y, int m, int delta) {
        int total = y * 12 + (m - 1) + delta;
        int ny = total / 12;
        int nm = total % 12 + 1;
        return ny + "-" + String.format("%02d", nm);
    }

    private java.math.BigDecimal getValue(Long indicatorId, String period) {
        if (period == null)
            return null;
        org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity q = new org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity();
        q.setIndicatorId(indicatorId);
        q.setPeriod(period);
        org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity e = indicatorValuesService
                .getOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>(q));
        return e == null ? null : e.getValue();
    }

    private String numStr(BigDecimal v) {
        return v == null ? "0" : v.stripTrailingZeros().toPlainString();
    }

    private String safeStr(String s) {
        return s == null ? "" : s;
    }

    private BigDecimal parseDecimal(String s) {
        try {
            if (s == null || s.isEmpty())
                return null;
            return new BigDecimal(s.trim());
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity predictCurrentYearForIndicator(
            Long indicatorId) {
        String yearStr = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy"));
        int year = Integer.parseInt(yearStr);

        // 读取或创建当年预测记录
        org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity forecast = baseMapper
                .findByIndicatorAndYear(indicatorId, year);
        if (forecast == null) {
            forecast = new org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity();
            forecast.setIndicatorId(indicatorId);
            forecast.setYear(year);
        }

        // 准备遍历2..12月，逐月递推
        java.math.BigDecimal[] monthValues = new java.math.BigDecimal[13]; // 1..12

        // 预取1..12月真实数据
        for (int m = 1; m <= 12; m++) {
            String period = year + "-" + String.format("%02d", m);
            monthValues[m] = getValue(indicatorId, period);
        }

        // 累计值，优先用真实数据，缺失用已预测值
        java.math.BigDecimal cumulative = java.math.BigDecimal.ZERO;
        for (int m = 1; m <= 12; m++) {
            if (monthValues[m] != null) {
                cumulative = cumulative.add(monthValues[m]);
            }
        }
        // 重新计算：递推预测时需要动态累计
        cumulative = java.math.BigDecimal.ZERO;

        for (int m = 1; m <= 12; m++) {
            String period = year + "-" + String.format("%02d", m);
            java.math.BigDecimal current = monthValues[m];

            // 如果当月有真实值，直接累计并跳过预测（仅当m>=2时我们关心预测）
            if (current != null) {
                cumulative = cumulative.add(current);
            } else if (m >= 2) {
                // 需要预测本月
                java.math.BigDecimal lag1 = (m - 1 >= 1) ? (monthValues[m - 1] != null ? monthValues[m - 1] : null)
                        : null;
                java.math.BigDecimal lag2 = (m - 2 >= 1) ? (monthValues[m - 2] != null ? monthValues[m - 2] : null)
                        : null;
                java.math.BigDecimal lag3 = (m - 3 >= 1) ? (monthValues[m - 3] != null ? monthValues[m - 3] : null)
                        : null;

                // 若滞后使用真实值为空，则用已预测值填充
                if (lag1 == null && m - 1 >= 1)
                    lag1 = monthValues[m - 1];
                if (lag2 == null && m - 2 >= 1)
                    lag2 = monthValues[m - 2];
                if (lag3 == null && m - 3 >= 1)
                    lag3 = monthValues[m - 3];

                // 去年同期
                String lastYear = (year - 1) + "-" + String.format("%02d", m);
                java.math.BigDecimal lastYearValue = getValue(indicatorId, lastYear);

                // 计算ma3（和服务中的一致：当前值缺失，用lag1+lag2+预测的当前? 这里用lag1+lag2+上月真实/预测均可，缺则不算ma3）
                java.math.BigDecimal ma3 = null;
                if (lag1 != null && lag2 != null) {
                    // 这里没有当前真实值，用lag1和lag2加上一个估计：取lag1作为近似
                    ma3 = lag1.add(lag2).add(lag1).divide(new java.math.BigDecimal("3"),
                            java.math.RoundingMode.HALF_UP);
                }

                // 当前年累计（到上月）
                java.math.BigDecimal currentYearCumulative = cumulative;

                IndicatorPredictFeatureDTO f = new IndicatorPredictFeatureDTO();
                f.setIndicatorId(String.valueOf(indicatorId));
                f.setPeriod(period);
                f.setCurrentValue(null);
                f.setLag1Value(lag1);
                f.setLag2Value(lag2);
                f.setLag3Value(lag3);
                f.setMa3Value(ma3);
                f.setSamePeriodLastYearValue(lastYearValue);
                f.setCurrentYearCumulativeValue(currentYearCumulative);
                // 年度预算：已在 predictMonthlyValue 的HTTP侧不需要，但可传0占位
                f.setAnnualBudgetValue(null);

                java.math.BigDecimal pred = predictMonthlyValue(f);
                if (pred == null)
                    pred = java.math.BigDecimal.ZERO;

                // 写回作为“本月值”供后续依赖
                monthValues[m] = pred;
                cumulative = cumulative.add(pred);

                // 写入到forecast实体中：仅当值既非0也非-1时才更新
                if (shouldUpdate(pred)) {
                    setForecastMonthValue(forecast, m, pred);
                }
            } else {
                // m==1且无真实值，不预测1月（按需求预测2-12月）
            }
        }

        // upsert到数据库
        // 先检查是否已有记录（有id则update，否则insert）
        org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity exist = baseMapper
                .findByIndicatorAndYear(indicatorId, year);
        if (exist == null || exist.getId() == null) {
            this.save(forecast);
        } else {
            forecast.setId(exist.getId());
            this.updateById(forecast);
        }

        return forecast;
    }

    @Override
    public void upsertMonthlyForecast(Long indicatorId, String period, BigDecimal value) {
        if (indicatorId == null || period == null || period.length() < 7)
            return;
        int year = Integer.parseInt(period.substring(0, 4));
        int month = Integer.parseInt(period.substring(5, 7));
        IndicatorForecastEntity exist = baseMapper.findByIndicatorAndYear(indicatorId, year);
        IndicatorForecastEntity entity = exist == null ? new IndicatorForecastEntity() : exist;
        entity.setIndicatorId(indicatorId);
        entity.setYear(year);
        if (value == null)
            value = BigDecimal.ZERO;
        switch (month) {
            case 2 -> entity.setForecastM02(value);
            case 3 -> entity.setForecastM03(value);
            case 4 -> entity.setForecastM04(value);
            case 5 -> entity.setForecastM05(value);
            case 6 -> entity.setForecastM06(value);
            case 7 -> entity.setForecastM07(value);
            case 8 -> entity.setForecastM08(value);
            case 9 -> entity.setForecastM09(value);
            case 10 -> entity.setForecastM10(value);
            case 11 -> entity.setForecastM11(value);
            case 12 -> entity.setForecastM12(value);
            default -> {
                /* 忽略1月 */ }
        }
        if (exist == null || exist.getId() == null) {
            this.save(entity);
        } else {
            entity.setId(exist.getId());
            this.updateById(entity);
        }
    }

    private void setForecastMonthValue(org.springblade.modules.yjzb.pojo.entity.IndicatorForecastEntity forecast,
            int month, java.math.BigDecimal value) {
        switch (month) {
            case 2 -> forecast.setForecastM02(value);
            case 3 -> forecast.setForecastM03(value);
            case 4 -> forecast.setForecastM04(value);
            case 5 -> forecast.setForecastM05(value);
            case 6 -> forecast.setForecastM06(value);
            case 7 -> forecast.setForecastM07(value);
            case 8 -> forecast.setForecastM08(value);
            case 9 -> forecast.setForecastM09(value);
            case 10 -> forecast.setForecastM10(value);
            case 11 -> forecast.setForecastM11(value);
            case 12 -> forecast.setForecastM12(value);
            default -> {
            }
        }
    }

    private boolean shouldUpdate(java.math.BigDecimal v) {
        if (v == null)
            return false;
        if (v.compareTo(java.math.BigDecimal.ZERO) == 0)
            return false;
        if (v.compareTo(new java.math.BigDecimal("-1")) == 0)
            return false;
        return true;
    }
}
