/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO;
import org.springblade.modules.yjzb.excel.IndicatorValuesExcel;
import org.springblade.modules.yjzb.wrapper.IndicatorValuesWrapper;
import org.springblade.modules.yjzb.service.IIndicatorValuesService;
import org.springblade.modules.yjzb.service.IIndicatorService;
import org.springblade.modules.yjzb.service.IIndicatorAiAnalysisService;
import org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.vo.BusinessKnowledgeVO;
import org.springblade.modules.yjzb.service.IIndicatorImportService;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;

import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 指标数据 控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("yjzb/indicatorValues")
@Tag(name = "指标数据", description = "指标数据接口")
public class IndicatorValuesController extends BladeController {

    private final IIndicatorValuesService indicatorValuesService;
    private final IIndicatorService indicatorService;
    private final IIndicatorAiAnalysisService indicatorAiAnalysisService;
    private final IIndicatorImportService indicatorImportService;
    private final org.springblade.modules.yjzb.service.IIndicatorForecastService indicatorForecastService;

    /**
     * 指标数据 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入indicatorValues")
    public R<IndicatorValuesVO> detail(IndicatorValuesEntity indicatorValues) {
        IndicatorValuesEntity detail = indicatorValuesService.getOne(Condition.getQueryWrapper(indicatorValues));
        return R.data(IndicatorValuesWrapper.build().entityVO(detail));
    }

    /**
     * 指标数据 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入indicatorValues")
    public R<IPage<IndicatorValuesVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorValues,
            Query query) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorValuesWrapper.build().indicatorValuesQuery(indicatorValues);
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<IndicatorValuesEntity> qw = Condition
                .getQueryWrapper(indicatorValues, IndicatorValuesEntity.class);
        // 过滤空值：值为NULL或0时剔除
        Object filterEmptyObj = indicatorValues.get("filterEmpty");
        boolean filterEmpty = false;
        if (filterEmptyObj != null) {
            String s = String.valueOf(filterEmptyObj);
            filterEmpty = "true".equalsIgnoreCase(s) || "1".equals(s);
        }
        if (filterEmpty) {
            qw.lambda().isNotNull(IndicatorValuesEntity::getValue)
                    .ne(IndicatorValuesEntity::getValue, java.math.BigDecimal.ZERO);
        }
        IPage<IndicatorValuesEntity> pages = indicatorValuesService.page(Condition.getPage(query), qw);
        return R.data(IndicatorValuesWrapper.build().pageVO(pages));
    }

    /**
     * 指标数据 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入indicatorValues")
    public R<IPage<IndicatorValuesVO>> page(IndicatorValuesVO indicatorValues, Query query) {
        IPage<IndicatorValuesVO> pages = indicatorValuesService.selectIndicatorValuesPage(Condition.getPage(query),
                indicatorValues);
        return R.data(pages);
    }

    /**
     * 获取所有指标的简要列表（仅返回id与name），供大模型Function调用
     */
    @GetMapping("/indicators/all-simple")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "获取全部指标id与名称", description = "返回所有指标的{id, name}列表", responses = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "OK", content = @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json", array = @io.swagger.v3.oas.annotations.media.ArraySchema(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = org.springblade.modules.yjzb.pojo.vo.SimpleIndicatorVO.class))))
    })
    public R<java.util.List<org.springblade.modules.yjzb.pojo.vo.SimpleIndicatorVO>> allIndicatorsSimple() {
        var list = indicatorService.list(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<org.springblade.modules.yjzb.pojo.entity.IndicatorEntity>()
                        .select("id", "name"));
        java.util.List<org.springblade.modules.yjzb.pojo.vo.SimpleIndicatorVO> result = list.stream()
                .map(e -> new org.springblade.modules.yjzb.pojo.vo.SimpleIndicatorVO(e.getId(), e.getName()))
                .collect(Collectors.toList());
        return R.data(result);
    }

    /**
     * 指标数据 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入indicatorValues")
    public R<Boolean> save(@Valid @RequestBody IndicatorValuesEntity indicatorValues) {
        return R.status(indicatorValuesService.save(indicatorValues));
    }

    /**
     * 指标数据 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入indicatorValues")
    public R<Boolean> update(@Valid @RequestBody IndicatorValuesEntity indicatorValues) {
        return R.status(indicatorValuesService.updateById(indicatorValues));
    }

    /**
     * 指标数据 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入indicatorValues")
    public R<Boolean> submit(@Valid @RequestBody IndicatorValuesEntity indicatorValues) {
        return R.status(indicatorValuesService.saveOrUpdate(indicatorValues));
    }

    /**
     * 指标数据 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R<Boolean> remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(indicatorValuesService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 根据指标类型分页查询指标数据
     */
    @GetMapping("/listByType")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "根据指标类型分页查询", description = "传入指标类型ID等参数")
    public R<IPage<IndicatorValuesVO>> listByType(
            @Parameter(description = "指标类型ID", required = true) @RequestParam Long indicatorTypeId,
            @Parameter(description = "数据期间，格式：YYYY-MM", required = false) @RequestParam(required = false) String period,
            @Parameter(description = "数据来源", required = false) @RequestParam(required = false) String dataSource,
            @Parameter(description = "指标名称模糊匹配", required = false) @RequestParam(name = "indicatorName_like", required = false) String indicatorNameLike,
            @Parameter(description = "过滤空值(值为0或空)", required = false) @RequestParam(required = false) Boolean filterEmpty,
            Query query) {

        IPage<IndicatorValuesVO> pages = indicatorValuesService.selectIndicatorValuesByType(
                Condition.getPage(query), indicatorTypeId, period, dataSource, indicatorNameLike, filterEmpty);
        return R.data(pages);
    }

    /**
     * 统计指定月份和指标类型的指标数值
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 19)
    @Operation(summary = "统计指标数值", description = "统计指定月份和指标类型的指标数值")
    public R<Map<String, Object>> getStatistics(
            @Parameter(description = "数据期间，格式：YYYY-MM", required = true) @RequestParam String period,
            @Parameter(description = "指标类型ID", required = true) @RequestParam Long indicatorTypeId,
            @Parameter(description = "过滤空值(值为0或空)", required = false) @RequestParam(required = false) Boolean filterEmpty) {

        Map<String, Object> statistics = indicatorValuesService.getIndicatorStatistics(period, indicatorTypeId,
                filterEmpty);
        return R.data(statistics);
    }

    /**
     * 按指标与期间区间(含端点)汇总合计值。
     * - 若仅查询某月：传入startPeriod=YYYY-MM且不传endPeriod，或endPeriod与startPeriod相同
     * - 返回：indicatorId, indicatorName, startPeriod, endPeriod, total
     */
    @GetMapping("/sum-by-indicator")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "按指标与期间区间合计", description = "示例：2025-04至2025-07，或仅2025-04某月", parameters = {
            @io.swagger.v3.oas.annotations.Parameter(name = "indicatorId", in = io.swagger.v3.oas.annotations.enums.ParameterIn.QUERY, required = true, description = "指标ID", schema = @io.swagger.v3.oas.annotations.media.Schema(type = "integer", format = "int64")),
            @io.swagger.v3.oas.annotations.Parameter(name = "startPeriod", in = io.swagger.v3.oas.annotations.enums.ParameterIn.QUERY, required = true, description = "起始期间(YYYY-MM)", schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string", pattern = "^\\\\d{4}-\\\\d{2}$")),
            @io.swagger.v3.oas.annotations.Parameter(name = "endPeriod", in = io.swagger.v3.oas.annotations.enums.ParameterIn.QUERY, required = false, description = "结束期间(YYYY-MM)，不传则按单月(startPeriod)", schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string", pattern = "^\\\\d{4}-\\\\d{2}$"))
    }, responses = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "OK", content = @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = org.springblade.modules.yjzb.pojo.vo.SumByIndicatorResponse.class)))
    })
    public R<java.util.Map<String, Object>> sumByIndicator(
            @Parameter(description = "指标ID", required = true) @RequestParam Long indicatorId,
            @Parameter(description = "起始期间，格式：YYYY-MM", required = true) @RequestParam String startPeriod,
            @Parameter(description = "结束期间，格式：YYYY-MM", required = false) @RequestParam(required = false) String endPeriod) {

        String normalizedEnd = (endPeriod == null || endPeriod.isBlank()) ? startPeriod : endPeriod;
        java.math.BigDecimal total = indicatorValuesService.sumByIndicatorAndPeriodRange(indicatorId, startPeriod,
                normalizedEnd);
        String indicatorName = null;
        var indicator = indicatorService.getById(indicatorId);
        if (indicator != null) {
            indicatorName = indicator.getName();
        }
        java.util.Map<String, Object> resp = new java.util.HashMap<>();
        resp.put("indicatorId", indicatorId);
        resp.put("indicatorName", indicatorName);
        resp.put("startPeriod", startPeriod);
        resp.put("endPeriod", normalizedEnd);
        resp.put("total", total);
        return R.data(resp);
    }

    /**
     * 导出数据
     */
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    @GetMapping("/export-indicatorValues")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "导出数据", description = "传入indicatorValues")
    public void exportIndicatorValues(@Parameter(hidden = true) @RequestParam Map<String, Object> indicatorValues,
            BladeUser bladeUser, HttpServletResponse response) {
        // 针对PostgreSQL数据库处理查询条件，避免bigint字段LIKE查询类型不匹配错误
        IndicatorValuesWrapper.build().indicatorValuesQuery(indicatorValues);
        QueryWrapper<IndicatorValuesEntity> queryWrapper = Condition.getQueryWrapper(indicatorValues,
                IndicatorValuesEntity.class);
        // if (!AuthUtil.isAdministrator()) {
        // queryWrapper.lambda().eq(IndicatorValues::getTenantId,
        // bladeUser.getTenantId());
        // }
        // queryWrapper.lambda().eq(IndicatorValuesEntity::getIsDeleted,
        // BladeConstant.DB_NOT_DELETED);
        List<IndicatorValuesExcel> list = indicatorValuesService.exportIndicatorValues(queryWrapper);
        ExcelUtil.export(response, "指标数据数据" + DateUtil.time(), "指标数据数据表", list, IndicatorValuesExcel.class);
    }

    /**
     * 重新开始AI分析：
     * - 使用该条指标数据的数值作为输入启动Dify工作流
     * - 将workflowRunId等信息写入AI解读分析表
     * - 开启后台轮询每3分钟查询一次，完成后回写结果
     */
    @PostMapping("/restart-ai-analysis")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "重新开始AI分析", description = "参数：indicatorValueId")
    public R<Long> restartAiAnalysis(@RequestParam Long indicatorValueId) {
        Long aiRecordId = indicatorAiAnalysisService.restartAnalysisForIndicatorValue(indicatorValueId);
        if (aiRecordId == null) {
            return R.fail("启动AI分析失败：请检查Dify配置(dify.api.base-url, dify.api.agentkey.analysisagent)是否正确且服务可访问");
        }
        return R.data(aiRecordId);
    }

    /**
     * 当月某类型AI指标解读（总体分析）
     * 传入：indicatorTypeId, period(YYYY-MM)
     * 聚合：同类型该月所有指标数据、同类型年度预算之和、当年累计支出和完成进度
     */
    @PostMapping("/monthlyTypeAnalysis/restart")
    @ApiOperationSupport(order = 13)
    @Operation(summary = "当月某类型AI指标解读(重启并记录)", description = "参数：indicatorTypeId, period(YYYY-MM)")
    public R<Long> restartMonthlyTypeAnalysis(@RequestParam Long indicatorTypeId, @RequestParam String period) {
        Long id = indicatorAiAnalysisService.restartTypeMonthlyAnalysis(indicatorTypeId, period);
        return id == null ? R.fail("AI总体解读失败") : R.data(id);
    }

    /** 费用管理总览AI分析 */
    @PostMapping("/overview/expense/restart")
    @ApiOperationSupport(order = 16)
    @Operation(summary = "费用管理总览AI分析(重启并记录)", description = "参数：indicatorTypeId, period(YYYY-MM)")
    public R<Long> restartExpenseOverview(@RequestParam Long indicatorTypeId, @RequestParam String period) {
        Long id = indicatorAiAnalysisService.restartExpenseOverviewAnalysis(indicatorTypeId, period);
        return id == null ? R.fail("费用总览AI解读失败") : R.data(id);
    }

    /** 资产负债总览AI分析 */
    @PostMapping("/overview/balance/restart")
    @ApiOperationSupport(order = 17)
    @Operation(summary = "资产负债总览AI分析(重启并记录)", description = "参数：indicatorTypeId, period(YYYY-MM)")
    public R<Long> restartBalanceOverview(@RequestParam Long indicatorTypeId, @RequestParam String period) {
        Long id = indicatorAiAnalysisService.restartBalanceOverviewAnalysis(indicatorTypeId, period);
        return id == null ? R.fail("资产负债总览AI解读失败") : R.data(id);
    }

    /** 利润总览AI分析 */
    @PostMapping("/overview/profit/restart")
    @ApiOperationSupport(order = 18)
    @Operation(summary = "利润总览AI分析(重启并记录)", description = "参数：indicatorTypeId, period(YYYY-MM)")
    public R<Long> restartProfitOverview(@RequestParam Long indicatorTypeId, @RequestParam String period) {
        Long id = indicatorAiAnalysisService.restartProfitOverviewAnalysis(indicatorTypeId, period);
        return id == null ? R.fail("利润总览AI解读失败") : R.data(id);
    }

    @GetMapping("/monthlyTypeAnalysis/latest")
    @ApiOperationSupport(order = 14)
    @Operation(summary = "查询当月某类型AI指标解读结果", description = "参数：indicatorTypeId, period(YYYY-MM)")
    public R<org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity> latestMonthlyTypeAnalysis(
            @RequestParam Long indicatorTypeId, @RequestParam String period) {
        var entity = indicatorAiAnalysisService.getLatestByIndicatorAndPeriod(indicatorTypeId, period);
        return R.data(entity);
    }

    /**
     * 查询最新AI分析结果（按指标数据ID映射 indicatorId+period）
     */
    @GetMapping("/latest-ai-analysis")
    @ApiOperationSupport(order = 12)
    @Operation(summary = "查询最新AI分析结果", description = "参数：indicatorValueId")
    public R<org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity> latestAiAnalysis(
            @RequestParam Long indicatorValueId) {
        var entity = indicatorAiAnalysisService.getLatestByIndicatorValueId(indicatorValueId);
        return R.data(entity);
    }

    /**
     * 新增：按指标ID+期间(YYYY-MM)查询最新AI解读结果
     */
    @GetMapping("/ai/latest-by-indicator")
    @ApiOperationSupport(order = 12)
    @Operation(summary = "查询最新AI解读(按指标+期间)", description = "参数：indicatorId, period(YYYY-MM)")
    public R<org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity> latestAiByIndicatorAndPeriod(
            @RequestParam Long indicatorId,
            @RequestParam String period) {
        var entity = indicatorAiAnalysisService.getLatestByIndicatorAndPeriod(indicatorId, period);
        return R.data(entity);
    }

    /**
     * 新增：按指标ID+期间 预测并落库 + 启动AI解读（异步）。
     */
    @PostMapping("/ai/predict-and-analyze")
    @ApiOperationSupport(order = 20)
    @Operation(summary = "按指标+期间预测并启动AI解读", description = "参数：indicatorId, period(YYYY-MM)")
    public R<String> predictAndAnalyzeByIndicator(@RequestParam Long indicatorId, @RequestParam String period) {
        try {
            // 1) 预测当月并写入预测表
            java.math.BigDecimal v = indicatorForecastService.predictByIndicatorAndPeriod(indicatorId, period);
            indicatorForecastService.upsertMonthlyForecast(indicatorId, period, v);

            // 2) 查询该指标+期间的指标值ID
            org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity q = new org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity();
            q.setIndicatorId(indicatorId);
            q.setPeriod(period);
            org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity one = indicatorValuesService
                    .getOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>(q));
            if (one == null || one.getId() == null) {
                return R.fail("未找到该指标在指定期间的指标值，无法启动AI解读");
            }

            // 3) 启动AI解读（异步轮询在服务内部处理）
            Long aiId = indicatorAiAnalysisService.restartAnalysisForIndicatorValue(one.getId());
            if (aiId == null) {
                return R.fail("AI解读启动失败");
            }
            return R.data("OK");
        } catch (Exception e) {
            return R.fail("处理失败: " + e.getMessage());
        }
    }

    /**
     * 新增：按指标ID+期间 预测并落库 + 启动AI解读（自定义提示词）。
     */
    @PostMapping("/ai/predict-and-analyze-with-prompt")
    @ApiOperationSupport(order = 21)
    @Operation(summary = "按指标+期间预测并启动AI解读(自定义提示词)", description = "参数：indicatorId, period(YYYY-MM), prompt")
    public R<String> predictAndAnalyzeByIndicatorWithPrompt(@RequestParam Long indicatorId,
            @RequestParam String period,
            @RequestParam String prompt) {
        try {
            // 1) 预测当月并写入预测表
            java.math.BigDecimal v = indicatorForecastService.predictByIndicatorAndPeriod(indicatorId, period);
            indicatorForecastService.upsertMonthlyForecast(indicatorId, period, v);

            // 2) 定位该指标+期间对应的指标值ID
            org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity q = new org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity();
            q.setIndicatorId(indicatorId);
            q.setPeriod(period);
            org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity one = indicatorValuesService
                    .getOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>(q));
            if (one == null || one.getId() == null) {
                return R.fail("未找到该指标在指定期间的指标值，无法启动AI解读");
            }

            // 3) 启动AI解读（写入自定义提示词）
            Long aiId = indicatorAiAnalysisService.restartAnalysisForIndicatorValueWithPrompt(one.getId(), prompt);
            if (aiId == null) {
                return R.fail("AI解读启动失败");
            }
            return R.data("OK");
        } catch (Exception e) {
            return R.fail("处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取指标预测分析的提示词预览
     */
    @GetMapping("/ai/prompt-preview")
    @ApiOperationSupport(order = 22)
    @Operation(summary = "获取提示词预览", description = "获取指标预测分析的提示词模板（含业务规则替换）")
    public R<String> getPromptPreview(@RequestParam Long indicatorId, @RequestParam String period) {
        try {
            String prompt = indicatorAiAnalysisService.buildPromptPreview(indicatorId, period);
            return R.data(prompt);
        } catch (Exception e) {
            log.error("获取提示词预览失败", e);
            return R.fail("获取提示词预览失败: " + e.getMessage());
        }
    }

    /**
     * 获取指标关联的预测类业务知识列表
     */
    @GetMapping("/ai/business-rules")
    @ApiOperationSupport(order = 23)
    @Operation(summary = "获取业务知识列表", description = "获取指定指标关联的预测类业务知识条目列表")
    public R<java.util.List<String>> getBusinessRules(@RequestParam Long indicatorId) {
        try {
            java.util.List<String> rules = indicatorAiAnalysisService.getBusinessRulesList(indicatorId);
            return R.data(rules);
        } catch (Exception e) {
            log.error("获取业务知识失败", e);
            return R.fail("获取业务知识失败: " + e.getMessage());
        }
    }

    /**
     * Excel数据导入
     */
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperationSupport(order = 15)
    @Operation(summary = "Excel数据导入", description = "上传Excel文件导入指标数据")
    public R<Map<String, Object>> importExcelData(
            @Parameter(description = "Excel文件", required = true) @RequestPart("file") MultipartFile file,
            @Parameter(description = "模板类型：profit(利润表), balance(资产负债表), expense(三项费用), tax(税利指标明细表)", required = true) @RequestParam("templateType") String templateType,
            @Parameter(description = "数据期间，格式YYYY-MM", required = true) @RequestParam("period") String period) {

        try {
            Map<String, Object> result = indicatorImportService.importExcelData(file, templateType, period);
            if ((Boolean) result.get("success")) {
                return R.data(result, "导入成功");
            } else {
                return R.fail("导入失败").data(result);
            }
        } catch (Exception e) {
            return R.fail("导入异常: " + e.getMessage());
        }
    }

    /**
     * 获取指标关联的业务知识详细信息（按分类分组）
     */
    @GetMapping("/ai/business-knowledge-by-category")
    @ApiOperationSupport(order = 24)
    @Operation(summary = "获取业务知识分类详情", description = "获取指定指标关联的预测类业务知识，按分类分组返回")
    public R<java.util.Map<String, java.util.List<BusinessKnowledgeVO>>> getBusinessKnowledgeByCategory(
            @RequestParam Long indicatorId) {
        try {
            java.util.Map<String, java.util.List<BusinessKnowledgeVO>> result = indicatorAiAnalysisService
                    .getBusinessKnowledgeByCategory(indicatorId);
            return R.data(result);
        } catch (Exception e) {
            log.error("获取业务知识分类详情失败", e);
            return R.fail("获取业务知识分类详情失败: " + e.getMessage());
        }
    }

    /**
     * 保存或更新业务知识
     */
    @PostMapping("/ai/business-knowledge/save")
    @ApiOperationSupport(order = 25)
    @Operation(summary = "保存业务知识", description = "新增或更新业务知识条目")
    public R<Boolean> saveBusinessKnowledge(@Valid @RequestBody BusinessKnowledgeEntity businessKnowledge) {
        try {
            boolean result = indicatorAiAnalysisService.saveOrUpdateBusinessKnowledge(businessKnowledge);
            return R.status(result);
        } catch (Exception e) {
            log.error("保存业务知识失败", e);
            return R.fail("保存业务知识失败: " + e.getMessage());
        }
    }

    /**
     * 删除业务知识
     */
    @PostMapping("/ai/business-knowledge/delete")
    @ApiOperationSupport(order = 26)
    @Operation(summary = "删除业务知识", description = "删除指定的业务知识条目")
    public R<Boolean> deleteBusinessKnowledge(@RequestParam Long id) {
        try {
            boolean result = indicatorAiAnalysisService.deleteBusinessKnowledge(id);
            return R.status(result);
        } catch (Exception e) {
            log.error("删除业务知识失败", e);
            return R.fail("删除业务知识失败: " + e.getMessage());
        }
    }

    /**
     * 获取业务知识分类列表
     */
    @GetMapping("/ai/business-knowledge-categories")
    @ApiOperationSupport(order = 27)
    @Operation(summary = "获取业务知识分类", description = "获取指定指标关联的业务知识分类列表")
    public R<java.util.List<String>> getBusinessKnowledgeCategories(@RequestParam Long indicatorId) {
        try {
            java.util.List<String> categories = indicatorAiAnalysisService.getBusinessKnowledgeCategories(indicatorId);
            return R.data(categories);
        } catch (Exception e) {
            log.error("获取业务知识分类失败", e);
            return R.fail("获取业务知识分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有财务分类列表
     */
    @GetMapping("/ai/finance-categories")
    @ApiOperationSupport(order = 28)
    @Operation(summary = "获取财务分类列表", description = "获取可用的财务分类")
    public R<java.util.List<java.util.Map<String, Object>>> getFinanceCategories() {
        try {
            log.info("开始获取财务分类列表");

            // 使用SpringUtil获取服务
            org.springblade.modules.yjzb.service.IFinanceCategoryService financeCategoryService = org.springblade.core.tool.utils.SpringUtil
                    .getBean(org.springblade.modules.yjzb.service.IFinanceCategoryService.class);

            // 查询所有启用状态的财务分类
            java.util.List<org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity> entities = financeCategoryService
                    .list();

            log.info("查询到财务分类数量: {}", entities != null ? entities.size() : 0);

            // 转换为简单的Map结构，避免复杂的VO转换
            java.util.List<java.util.Map<String, Object>> result = new java.util.ArrayList<>();
            if (entities != null && !entities.isEmpty()) {
                for (org.springblade.modules.yjzb.pojo.entity.FinanceCategoryEntity entity : entities) {
                    // 只返回启用状态的分类
                    if (entity.getStatus() != null && entity.getStatus() == 1) {
                        java.util.Map<String, Object> item = new java.util.HashMap<>();
                        item.put("id", entity.getId());
                        item.put("categoryName", entity.getCategoryName());
                        item.put("categoryCode", entity.getCategoryCode());
                        item.put("fullPath", entity.getFullPath());
                        item.put("parentId", entity.getParentId());
                        item.put("sort", entity.getSort());
                        result.add(item);
                    }
                }

                // 按sort字段排序
                result.sort((a, b) -> {
                    Integer sortA = (Integer) a.get("sort");
                    Integer sortB = (Integer) b.get("sort");
                    if (sortA == null)
                        sortA = 0;
                    if (sortB == null)
                        sortB = 0;
                    return sortA.compareTo(sortB);
                });
            }

            log.info("返回财务分类数量: {}", result.size());
            return R.data(result);
        } catch (Exception e) {
            log.error("获取财务分类失败", e);
            return R.fail("获取财务分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新业务知识的分类
     */
    @PostMapping("/ai/business-knowledge/update-category")
    @ApiOperationSupport(order = 29)
    @Operation(summary = "更新业务知识分类", description = "拖拽更新业务知识的财务分类")
    public R<Boolean> updateBusinessKnowledgeCategory(@RequestParam Long knowledgeId, @RequestParam Long categoryId) {
        try {
            log.info("更新业务知识分类 knowledgeId={}, categoryId={}", knowledgeId, categoryId);

            // 获取业务知识服务
            org.springblade.modules.yjzb.service.IBusinessKnowledgeService businessKnowledgeService = org.springblade.core.tool.utils.SpringUtil
                    .getBean(org.springblade.modules.yjzb.service.IBusinessKnowledgeService.class);

            // 获取业务知识实体
            org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity entity = businessKnowledgeService
                    .getById(knowledgeId);
            if (entity == null) {
                return R.fail("业务知识不存在");
            }

            // 更新分类
            entity.setKnowledgeCategory(categoryId);
            boolean success = businessKnowledgeService.updateById(entity);

            if (success) {
                // 清除缓存
                org.springblade.modules.yjzb.service.cache.BusinessKnowledgeRuleCache businessKnowledgeRuleCache = org.springblade.core.tool.utils.SpringUtil
                        .getBean(org.springblade.modules.yjzb.service.cache.BusinessKnowledgeRuleCache.class);
                businessKnowledgeRuleCache.rebuildWith(businessKnowledgeService.list());
                log.info("业务知识分类更新成功");
                return R.data(true);
            } else {
                return R.fail("更新失败");
            }
        } catch (Exception e) {
            log.error("更新业务知识分类失败", e);
            return R.fail("更新业务知识分类失败: " + e.getMessage());
        }
    }
}
