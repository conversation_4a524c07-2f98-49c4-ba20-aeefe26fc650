package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.entity.ExpenseForecast;

import java.util.List;
import java.util.Map;

/**
 * 办公费用预测服务接口
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
public interface ExpenseForecastService extends IService<ExpenseForecast> {

    /**
     * 执行费用预测
     *
     * @param indicatorId    指标ID
     * @param forecastPeriod 预测期间
     * @param forecastType   预测类型
     * @param inputData      输入数据
     * @return 预测结果
     */
    ExpenseForecast executeForecast(Long indicatorId, String forecastPeriod, String forecastType, String inputData);

    /**
     * 获取预测结果
     *
     * @param indicatorId    指标ID
     * @param forecastPeriod 预测期间
     * @param forecastType   预测类型
     * @return 预测结果
     */
    ExpenseForecast getForecastResult(Long indicatorId, String forecastPeriod, String forecastType);

    /**
     * 检查是否有缓存的预测结果
     *
     * @param indicatorId    指标ID
     * @param forecastPeriod 预测期间
     * @param forecastType   预测类型
     * @return 是否有缓存
     */
    boolean hasCachedForecast(Long indicatorId, String forecastPeriod, String forecastType);

    /**
     * 按类别预测剩余月份的办公费用
     *
     * @param indicatorId 指标ID
     * @param period      当前期间（格式：YYYY-MM）
     * @return 预测结果列表，每个元素包含月份和预测值
     */
    List<Map<String, Object>> forecastRemainingMonthsByCategory(Long indicatorId, String period);

    /**
     * 只预测下一个月的办公费用
     *
     * @param indicatorId 指标ID
     * @param period      当前期间（格式：YYYY-MM）
     * @return 下一个月预测结果，包含月份和预测值
     */
    Map<String, Object> forecastNextMonthByCategory(Long indicatorId, String period);

    /**
     * 获取最新的总体预测结果
     *
     * @param indicatorId 指标ID
     * @return 最新的总体预测结果
     */
    ExpenseForecast getLatestTotalForecast(Long indicatorId);

    /**
     * 获取指定期间的所有分类预测结果
     *
     * @param indicatorId 指标ID
     * @param period      预测期间（格式：YYYY-MM）
     * @return 分类预测结果列表
     */
    List<ExpenseForecast> getCategoryForecastsByPeriod(Long indicatorId, String period);

    /**
     * 获取最新的分类预测结果
     *
     * @param indicatorId 指标ID
     * @return 最新的分类预测结果列表
     */
    List<ExpenseForecast> getLatestCategoryForecasts(Long indicatorId);

    /**
     * 获取年度预算数据
     *
     * @param indicatorId 指标ID
     * @param year        年份
     * @return 年度预算金额
     */
    java.math.BigDecimal getAnnualBudget(Long indicatorId, Integer year);

    /**
     * 获取当前累计值（真实数据）
     *
     * @param indicatorId 指标ID
     * @param year        年份
     * @param month       当前月份
     * @return 当前累计值
     */
    java.math.BigDecimal getCurrentCumulative(Long indicatorId, Integer year, Integer month);

    /**
     * 获取预测累计值
     *
     * @param indicatorId 指标ID
     * @param year        年份
     * @param month       当前月份
     * @return 预测累计值
     */
    java.math.BigDecimal getForecastCumulative(Long indicatorId, Integer year, Integer month);

    /**
     * 获取指定期间的总预测值
     *
     * @param indicatorId 指标ID
     * @param period      预测期间
     * @return 总预测值
     */
    ExpenseForecast getTotalForecastByPeriod(Long indicatorId, String period);

    /**
     * 获取指定期间的所有分类预测值
     *
     * @param indicatorId 指标ID
     * @param period      预测期间
     * @return 分类预测值列表
     */
    List<ExpenseForecast> getCategoryForecastsByPeriodFromMapper(Long indicatorId, String period);

    /**
     * 获取指定月份的预测值（不累计）
     *
     * @param indicatorId 指标ID
     * @param year        年份
     * @param month       月份
     * @return 该月的预测值
     */
    java.math.BigDecimal getMonthlyForecastValue(Long indicatorId, Integer year, Integer month);

    /**
     * 获取指定月份、分类的预测值（不累计）
     */
    java.math.BigDecimal getMonthlyForecastValueByCategory(Long indicatorId, Integer year, Integer month,
            String category);

    /**
     * 获取指定年份、到指定月份的分类实际累计（每类返回12个月数组，超过month部分为null）
     */
    java.util.Map<String, Object> getCategoryMonthlyActuals(Long indicatorId, Integer year, Integer month,
            java.util.List<String> categories);
}
