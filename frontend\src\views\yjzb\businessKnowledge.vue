<template>
  <basic-container>
    <avue-crud :option="option"
               v-model:search="search"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.businessKnowledge_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="warning"
                   plain
                   icon="el-icon-download"
                   @click="handleExport">导 出
        </el-button>
      </template>
      <!-- 适用指标：表单插槽 -->
      <template #applicableIndicators-form>
        <div>
          <el-space wrap>
            <template v-if="(form.applicableIndicators || []).length <= 5">
              <el-tag
                v-for="id in (form.applicableIndicators || [])"
                :key="id"
                closable
                @close="removeIndicatorTag(id)"
              >{{ indicatorNameMap[id] || id }}</el-tag>
            </template>
            <template v-else>
              <span>已选择{{ (form.applicableIndicators || []).length }}个</span>
            </template>
            <IndicatorMultiSelector
              v-model="form.applicableIndicators"
              @select-items="onIndicatorItems"
            />
          </el-space>
        </div>
      </template>
      <!-- 适用指标：表格单元格插槽 -->
      <template #applicableIndicators="{ row }">
        <span v-if="Array.isArray(row.applicableIndicators) && row.applicableIndicators.length">
          {{ row.applicableIndicators.map(id => indicatorNameMap[id] || id).join('，') }}
        </span>
        <span v-else>-</span>
      </template>


    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/yjzb/businessKnowledge";
  import option from "@/option/yjzb/businessKnowledge";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/utils/auth';
  import {downloadXls} from "@/utils/util";
  import {dateNow} from "@/utils/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
  import { getDetail as getIndicatorDetail } from "@/api/yjzb/indicator";
  import IndicatorMultiSelector from "@/components/indicator-multi-selector/IndicatorMultiSelector.vue";

  export default {
    components: { IndicatorMultiSelector },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        // 名称映射（ID->名称）
        indicatorNameMap: {},
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.businessKnowledge_add, false),
          viewBtn: this.validData(this.permission.businessKnowledge_view, false),
          delBtn: this.validData(this.permission.businessKnowledge_delete, false),
          editBtn: this.validData(this.permission.businessKnowledge_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        const payload = { ...row };
        // 后端期望字符串，前端存数组：统一序列化为 JSON 字符串提交
        if (Array.isArray(payload.applicableIndicators)) {
          payload.applicableIndicators = JSON.stringify(payload.applicableIndicators);
        } else if (payload.applicableIndicators == null || payload.applicableIndicators === '') {
          payload.applicableIndicators = '[]';
        }
        add(payload).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        const payload = { ...row };
        if (Array.isArray(payload.applicableIndicators)) {
          payload.applicableIndicators = JSON.stringify(payload.applicableIndicators);
        } else if (payload.applicableIndicators == null || payload.applicableIndicators === '') {
          payload.applicableIndicators = '[]';
        }
        update(payload).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        let downloadUrl = `/yjzb/businessKnowledge/export-businessKnowledge?${this.website.tokenHeader}=${getToken()}`;
        const {
        } = this.query;
        let values = {
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `业务知识${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      onIndicatorItems(items = []) {
        (items || []).forEach(it => {
          this.indicatorNameMap[it.id] = it.name || it.code || it.id;
        });
      },
      removeIndicatorTag(id) {
        const arr = Array.isArray(this.form.applicableIndicators) ? this.form.applicableIndicators : [];
        this.form.applicableIndicators = arr.filter(x => x !== id);
      },
      normalizeIndicators(val) {
        if (Array.isArray(val)) return val;
        if (val === null || val === undefined || val === '') return [];
        if (typeof val === 'string') {
          try {
            const parsed = JSON.parse(val);
            return Array.isArray(parsed) ? parsed : String(val).split(',').filter(Boolean);
          } catch (e) {
            return String(val).split(',').filter(Boolean);
          }
        }
        return [];
      },
      async populateIndicatorNames(records = []) {
        const missing = new Set();
        records.forEach(r => {
          const arr = Array.isArray(r.applicableIndicators) ? r.applicableIndicators : [];
          arr.forEach(id => { if (!this.indicatorNameMap[id]) missing.add(id); });
        });
        if (missing.size === 0) return;
        const tasks = Array.from(missing).map(async (id) => {
          try {
            const resp = await getIndicatorDetail(id);
            const d = resp?.data?.data || {};
            this.indicatorNameMap[id] = d.name || d.code || id;
          } catch (e) {
            this.indicatorNameMap[id] = id;
          }
        });
        await Promise.allSettled(tasks);
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
            //  applicableIndicators h => []
            const v = this.form.applicableIndicators;
            if (v && !Array.isArray(v)) {
              try {
                const parsed = JSON.parse(v);
                this.form.applicableIndicators = Array.isArray(parsed)
                  ? parsed
                  : String(v).split(',').filter(Boolean);
              } catch (e) {
                this.form.applicableIndicators = String(v).split(',').filter(Boolean);
              }
            } else if (!v) {
              this.form.applicableIndicators = [];
            }
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;

        const {
          knowledgeTitle,
          knowledgeType,
          knowledgeContent,
        } = this.query;

        let values = {
          knowledgeTitle_like: knowledgeTitle,
          knowledgeContent_like: knowledgeContent,
          knowledgeType_equal: knowledgeType,
        };

        getList(page.currentPage, page.pageSize, values).then(async res => {
          const data = res.data.data;
          this.page.total = data.total;
          const records = (data.records || []).map(r => ({
            ...r,
            applicableIndicators: this.normalizeIndicators(r.applicableIndicators)
          }));
          this.data = records;
          await this.populateIndicatorNames(records);
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
