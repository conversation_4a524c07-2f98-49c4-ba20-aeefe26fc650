package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.modules.yjzb.entity.ExpenseAiAnalysis;
import org.springblade.modules.yjzb.entity.ExpenseForecast;
import org.springblade.modules.yjzb.service.ExpenseAiAnalysisService;
import org.springblade.modules.yjzb.service.ExpenseForecastService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 办公费用AI解读分析控制器
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/yjzb/expense-ai-analysis")
@Tag(name = "办公费用AI解读分析", description = "办公费用AI解读分析相关接口")
public class ExpenseAiAnalysisController {

    private final ExpenseAiAnalysisService expenseAiAnalysisService;
    private final ExpenseForecastService expenseForecastService;

    /**
     * 提示词预览：返回构建后的完整提示词（含业务规则替换）
     */
    @GetMapping("/prompt-preview")
    @Operation(summary = "提示词预览", description = "获取指定指标与分析类型的提示词模板（包含业务规则替换后的内容）")
    public R<String> promptPreview(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "分析类型，如 trend/volatility/budget") @RequestParam String analysisType) {
        try {
            String prompt = expenseAiAnalysisService.buildPromptPreview(indicatorId, analysisType);
            return R.data(prompt);
        } catch (Exception e) {
            return R.fail("获取提示词失败: " + e.getMessage());
        }
    }

    /**
     * 获取指标关联的业务知识列表（用于提示词编辑器右侧显示）
     */
    @GetMapping("/business-rules")
    @Operation(summary = "获取业务知识列表", description = "获取指定指标关联的归因类业务知识条目列表")
    public R<java.util.List<String>> getBusinessRules(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId) {
        try {
            java.util.List<String> rules = expenseAiAnalysisService.getBusinessRulesList(indicatorId);
            return R.data(rules);
        } catch (Exception e) {
            return R.fail("获取业务知识失败: " + e.getMessage());
        }
    }

    /**
     * 调试接口：检查业务知识缓存状态
     */
    @GetMapping("/debug/cache-status")
    @Operation(summary = "调试缓存状态", description = "检查业务知识缓存中的数据")
    public R<Map<String, Object>> debugCacheStatus(@RequestParam(required = false) Long indicatorId) {
        try {
            Map<String, Object> result = new java.util.HashMap<>();

            // 如果指定了indicatorId，检查该指标的缓存
            if (indicatorId != null) {
                String rules2 = expenseAiAnalysisService.getBusinessRulesList(indicatorId).toString();
                result.put("indicatorId", indicatorId);
                result.put("knowledgeType2_rules", rules2);
            }

            // 检查几个常见的办公费指标
            Long[] testIds = { 1952675507458174978L, 1952675507458175077L }; // 办公费（销售费用）和办公费（管理费用）
            for (Long id : testIds) {
                String rules = expenseAiAnalysisService.getBusinessRulesList(id).toString();
                result.put("indicator_" + id + "_rules", rules);
            }

            return R.data(result);
        } catch (Exception e) {
            return R.fail("调试失败: " + e.getMessage());
        }
    }

    /**
     * 执行AI分析
     */
    @PostMapping("/execute")
    @Operation(summary = "执行AI分析", description = "执行办公费用AI解读分析")
    public R<String> executeAnalysis(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period,
            @Parameter(description = "分析类型") @RequestParam String analysisType,
            @Parameter(description = "输入参数") @RequestParam String inputParams,
            @Parameter(description = "是否强制重新计算，跳过缓存") @RequestParam(required = false, defaultValue = "false") boolean force) {

        try {
            String result = expenseAiAnalysisService.executeAnalysis(indicatorId, period, analysisType, inputParams,
                    force);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("AI分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI分析结果
     */
    @GetMapping("/result")
    @Operation(summary = "获取AI分析结果", description = "获取办公费用AI解读分析结果")
    public R<ExpenseAiAnalysis> getAnalysisResult(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period,
            @Parameter(description = "分析类型") @RequestParam String analysisType) {

        ExpenseAiAnalysis result = expenseAiAnalysisService.getAnalysisResult(indicatorId, period, analysisType);
        return R.data(result);
    }

    /**
     * 检查是否有缓存结果
     */
    @GetMapping("/has-cache")
    @Operation(summary = "检查缓存", description = "检查是否有缓存的AI分析结果")
    public R<Boolean> hasCachedResult(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "期间") @RequestParam String period,
            @Parameter(description = "分析类型") @RequestParam String analysisType) {

        boolean hasCache = expenseAiAnalysisService.hasCachedResult(indicatorId, period, analysisType);
        return R.data(hasCache);
    }

    /**
     * 执行费用预测
     */
    @PostMapping("/forecast")
    @Operation(summary = "执行费用预测", description = "执行办公费用预测")
    public R<ExpenseForecast> executeForecast(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "预测期间") @RequestParam String forecastPeriod,
            @Parameter(description = "预测类型") @RequestParam String forecastType,
            @Parameter(description = "输入数据") @RequestParam String inputData) {

        try {
            ExpenseForecast result = expenseForecastService.executeForecast(indicatorId, forecastPeriod, forecastType,
                    inputData);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("费用预测失败: " + e.getMessage());
        }
    }

    /**
     * 获取预测结果
     */
    @GetMapping("/forecast-result")
    @Operation(summary = "获取预测结果", description = "获取办公费用预测结果")
    public R<ExpenseForecast> getForecastResult(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "预测期间") @RequestParam String forecastPeriod,
            @Parameter(description = "预测类型") @RequestParam String forecastType) {

        ExpenseForecast result = expenseForecastService.getForecastResult(indicatorId, forecastPeriod, forecastType);
        return R.data(result);
    }

    /**
     * 只预测下一个月的办公费用
     */
    @PostMapping("/forecast-next-month")
    @Operation(summary = "预测下一个月", description = "只预测下一个月的办公费用")
    public R<Map<String, Object>> forecastNextMonth(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "当前期间") @RequestParam String period) {

        try {
            Map<String, Object> result = expenseForecastService.forecastNextMonthByCategory(indicatorId, period);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("预测下一个月失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新的总体预测结果
     */
    @GetMapping("/latest-total-forecast")
    @Operation(summary = "获取最新总体预测", description = "获取最新的总体预测结果")
    public R<ExpenseForecast> getLatestTotalForecast(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId) {

        try {
            ExpenseForecast result = expenseForecastService.getLatestTotalForecast(indicatorId);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取最新总体预测失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新的分类预测结果
     */
    @GetMapping("/latest-category-forecasts")
    @Operation(summary = "获取最新分类预测", description = "获取最新的分类预测结果")
    public R<List<ExpenseForecast>> getLatestCategoryForecasts(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId) {

        try {
            List<ExpenseForecast> result = expenseForecastService.getLatestCategoryForecasts(indicatorId);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取最新分类预测失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定期间的所有分类预测结果
     */
    @GetMapping("/category-forecasts-by-period")
    @Operation(summary = "按期间获取分类预测", description = "获取指定期间的所有分类预测结果")
    public R<List<ExpenseForecast>> getCategoryForecastsByPeriod(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "预测期间") @RequestParam String period) {

        try {
            List<ExpenseForecast> result = expenseForecastService.getCategoryForecastsByPeriod(indicatorId, period);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取分类预测失败: " + e.getMessage());
        }
    }

    /**
     * 获取年度预算数据
     */
    @GetMapping("/annual-budget")
    @Operation(summary = "获取年度预算", description = "获取指定指标和年份的年度预算")
    public R<java.math.BigDecimal> getAnnualBudget(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "年份") @RequestParam Integer year) {
        try {
            java.math.BigDecimal result = expenseForecastService.getAnnualBudget(indicatorId, year);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取年度预算失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前累计值（真实数据）
     */
    @GetMapping("/current-cumulative")
    @Operation(summary = "获取当前累计值", description = "获取指定指标、年份和月份的当前累计值")
    public R<java.math.BigDecimal> getCurrentCumulative(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "年份") @RequestParam Integer year,
            @Parameter(description = "月份") @RequestParam Integer month) {
        try {
            java.math.BigDecimal result = expenseForecastService.getCurrentCumulative(indicatorId, year, month);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取当前累计值失败: " + e.getMessage());
        }
    }

    /**
     * 获取预测累计值
     */
    @GetMapping("/forecast-cumulative")
    @Operation(summary = "获取预测累计值", description = "获取指定指标、年份和月份的预测累计值")
    public R<java.math.BigDecimal> getForecastCumulative(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "年份") @RequestParam Integer year,
            @Parameter(description = "月份") @RequestParam Integer month) {
        try {
            java.math.BigDecimal result = expenseForecastService.getForecastCumulative(indicatorId, year, month);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取预测累计值失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定期间的总预测值
     */
    @GetMapping("/total-forecast-by-period")
    @Operation(summary = "获取指定期间的总预测值", description = "获取指定指标和期间的总预测值")
    public R<ExpenseForecast> getTotalForecastByPeriod(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "预测期间") @RequestParam String period) {
        try {
            ExpenseForecast result = expenseForecastService.getTotalForecastByPeriod(indicatorId, period);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取指定期间的总预测值失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定期间的所有分类预测值
     */
    @GetMapping("/category-forecasts-by-period-mapper")
    @Operation(summary = "获取指定期间的分类预测值", description = "获取指定指标和期间的所有分类预测值")
    public R<List<ExpenseForecast>> getCategoryForecastsByPeriodFromMapper(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "预测期间") @RequestParam String period) {
        try {
            List<ExpenseForecast> result = expenseForecastService.getCategoryForecastsByPeriodFromMapper(indicatorId,
                    period);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取指定期间的分类预测值失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定月份的预测值（不累计）
     */
    @GetMapping("/monthly-forecast-value")
    @Operation(summary = "获取指定月份的预测值", description = "获取指定指标、年份和月份的预测值（不累计）")
    public R<java.math.BigDecimal> getMonthlyForecastValue(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "年份") @RequestParam Integer year,
            @Parameter(description = "月份") @RequestParam Integer month) {
        try {
            java.math.BigDecimal result = expenseForecastService.getMonthlyForecastValue(indicatorId, year, month);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取指定月份的预测值失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定月份的分类预测值（不累计）
     */
    @GetMapping("/monthly-forecast-value-by-category")
    @Operation(summary = "获取指定月份的分类预测值", description = "获取指定指标、年份、月份、分类的预测值（不累计）")
    public R<java.math.BigDecimal> getMonthlyForecastValueByCategory(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "年份") @RequestParam Integer year,
            @Parameter(description = "月份") @RequestParam Integer month,
            @Parameter(description = "分类") @RequestParam String category) {
        try {
            java.math.BigDecimal result = expenseForecastService.getMonthlyForecastValueByCategory(indicatorId, year,
                    month, category);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取指定月份的分类预测值失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类月度实际累计（到指定月为止）
     */
    @GetMapping("/category-monthly-actuals")
    @Operation(summary = "获取分类月度实际累计", description = "获取指定指标、年份、月份（截止）以及分类列表的月度实际累计曲线")
    public R<java.util.Map<String, Object>> getCategoryMonthlyActuals(
            @Parameter(description = "指标ID") @RequestParam Long indicatorId,
            @Parameter(description = "年份") @RequestParam Integer year,
            @Parameter(description = "月份") @RequestParam Integer month,
            @Parameter(description = "分类，逗号分隔") @RequestParam String categories) {
        try {
            java.util.List<String> list = java.util.Arrays.asList(categories.split(","));
            java.util.Map<String, Object> result = expenseForecastService.getCategoryMonthlyActuals(indicatorId, year,
                    month, list);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取分类月度实际累计失败: " + e.getMessage());
        }
    }
}
