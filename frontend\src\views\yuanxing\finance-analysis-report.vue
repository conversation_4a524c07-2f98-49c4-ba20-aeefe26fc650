<template>
  <!-- 财务税费管控 - 深度分析报告页面 -->
  <basic-container>
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <h3>深度分析报告</h3>
      <div class="header-actions">
        <el-button type="primary" @click="generateReport">
          <el-icon><plus /></el-icon> 生成新报告
        </el-button>
        <el-button @click="refreshReports">
          <el-icon><refresh /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
<!--    <div class="filter-panel">-->
<!--      <el-form :model="filterForm" inline label-width="80px">-->
<!--        <el-form-item label="选择月份">-->
<!--          <el-date-picker-->
<!--            v-model="filterForm.selectedMonth"-->
<!--            type="month"-->
<!--            placeholder="选择月份"-->
<!--            format="YYYY-MM"-->
<!--            value-format="YYYY-MM"-->
<!--          />-->
<!--        </el-form-item>-->
<!--        <el-form-item>-->
<!--          <el-button type="primary" @click="handleFilter">查询</el-button>-->
<!--          <el-button @click="resetFilter">重置</el-button>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--    </div>-->

    <!-- 报告统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon finance">
            <el-icon><document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">总报告数</div>
            <div class="stat-value">{{ reportStats.total }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <el-icon><circle-check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">本年完成</div>
            <div class="stat-value">{{ reportStats.thisyear }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <el-icon><clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">生成中</div>
            <div class="stat-value">{{ reportStats.generating }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon danger">
            <el-icon><warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">失败重试</div>
            <div class="stat-value">{{ reportStats.failed }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 报告列表 -->
    <div class="report-list-panel">
      <div class="panel-header">
        <h4>分析报告列表</h4>
        <div class="panel-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索报告标题"
            size="small"
            prefix-icon="el-icon-search"
            style="width: 200px; margin-right: 10px;"
          />
<!--          <el-select v-model="listSortType" size="small" style="width: 120px;">-->
<!--            <el-option label="最新生成" value="latest" />-->
<!--            <el-option label="最多下载" value="downloadCount" />-->
<!--            <el-option label="按标题" value="title" />-->
<!--          </el-select>-->
        </div>
      </div>
      
      <el-table :data="filteredReportList" stripe border v-loading="loading">
        <el-table-column prop="title" label="报告标题" min-width="200">
          <template #default="{ row }">
            <div class="report-title">
              <el-icon><document /></el-icon>
              <span @click="viewReport(row)" class="title-link">{{ row.title }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="报告类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getReportTypeTag(row.type)" size="small">
              {{ row.typeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="period" label="分析周期" width="140" />
        <el-table-column prop="generateTime" label="生成时间" width="160" />
        <el-table-column prop="fileSizeFormatted" label="文件大小" width="130" />
        <el-table-column prop="downloadCount" label="下载次数" width="100" align="right" />
        <el-table-column prop="dataSource" label="数据来源" width="100">
          <template #default="{ row }">
            <el-tag :type="getDataSourceTag(row.dataSource)" size="small">
              {{ getDataSourceName(row.dataSource) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reportStatus" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.reportStatus)" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="mini" type="text" @click="viewReport(row)" v-if="row.reportStatus === 'completed'">
              <el-icon><view /></el-icon> 查看
            </el-button>
            <el-button size="mini" type="text" @click="downloadReport(row)" v-if="row.reportStatus === 'completed'">
              <el-icon><download /></el-icon> 下载报告
            </el-button>
            <el-button size="mini" type="text" @click="downloadExcel(row)" v-if="row.reportStatus === 'completed' && row.dataSource === 'excel'">
              <el-icon><files /></el-icon> 下载Excel
            </el-button>
            <el-button size="mini" type="text" @click="retryGenerate(row)" v-if="row.reportStatus === 'failed'">
              <el-icon><refresh /></el-icon> 重试
            </el-button>
            <el-button size="mini" type="text" @click="deleteReport(row)">
              <el-icon><delete /></el-icon> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div style="margin-top: 15px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 报告详情对话框 -->
    <el-dialog
      v-model="reportDialogVisible"
      :title="currentReport?.title"
      width="80%"
      :modal-append-to-body="false"
      top="5vh"
      destroy-on-close
      @close="handleDialogClose"
    >
      <div class="report-detail-template" v-if="currentReport">
        <div class="template-header">
<!--          <h2>广东烟草阳江市有限责任公司</h2>-->
          <div class="header-title-row">
            <h2>{{ currentReport.title }}</h2>
            <div class="template-actions">
              <el-button @click="downloadReport(currentReport)">
                <el-icon><download /></el-icon> 下载报告
              </el-button>
              <el-button @click="exportReport('pdf')">
                <el-icon><document /></el-icon> 导出PDF
              </el-button>
              <!-- 移除导出Excel按钮 -->
            </div>
          </div>
<!--          <div class="template-meta">-->
<!--            <span>分析周期：{{ currentReport.period }}</span>-->
<!--            <span>生成时间：{{ currentReport.generateTime }}</span>-->
<!--            <span>下载次数：{{ currentReport.downloadCount }}</span>-->
<!--          </div>-->
        </div>
        <div class="template-content">
          <div v-html="currentReport.templateContent" class="template-html"></div>
        </div>
      </div>
    </el-dialog>

    <!-- 生成报告对话框 -->
    <el-dialog
      v-model="generateDialogVisible"
      title="生成分析报告"
      width="700px"
    >
      <el-form :model="generateForm" :rules="generateRules" ref="generateFormRef" label-width="120px">
        <el-form-item label="报告标题" prop="title">
          <el-input v-model="generateForm.title" placeholder="请输入报告标题" />
        </el-form-item>

        <!-- 数据来源选择 -->
        <el-form-item label="数据来源" prop="dataSource">
          <el-radio-group v-model="generateForm.dataSource" @change="handleDataSourceChange">
<!--            <el-radio label="database">数据库数据</el-radio>-->
            <el-radio label="excel">Excel导入数据</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- Excel文件上传 -->
        <el-form-item v-if="generateForm.dataSource === 'excel'" label="Excel文件" prop="excelFile">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".xlsx,.xls"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
          >
            <el-button type="primary">
              <el-icon><upload /></el-icon>
              选择表样.xlsx文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传xlsx/xls文件，且不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="生成年份" prop="queryYear">
          <el-select v-model="generateForm.queryYear" placeholder="选择查询年份">
            <el-option v-for="year in yearOptions" :key="year" :label="year + '年'" :value="year" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始月份" prop="startMonth" v-show="false">
          <el-select v-model="generateForm.startMonth" placeholder="选择开始月份">
            <el-option v-for="month in monthOptions" :key="month" :label="month + '月'" :value="month" />
          </el-select>
        </el-form-item>
        <el-form-item label="生成月份" prop="endMonth">
          <el-select v-model="generateForm.endMonth" placeholder="选择结束月份">
            <el-option v-for="month in monthOptions" :key="month" :label="month + '月'" :value="month" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitGenerate" :loading="generating">
          {{ generating ? '生成中...' : '开始生成' }}
        </el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Plus, Refresh, Document, CircleCheck, Clock, Warning, View, Download, Share, Delete, Calendar, Files, Upload } from '@element-plus/icons-vue'
import {
  getReportList,
  getReportStatistics,
  getReportDetail,
  generateReport,
  removeReports,
  downloadReport,
  retryGenerateReport,
  previewReport,
  downloadPdfReport,
  uploadExcelData,
  downloadExcelFile
} from '@/api/yjzb/financeAnalysisReport'

export default {
  name: 'FinanceAnalysisReport',
  components: {
    Plus, Refresh, Document, CircleCheck, Clock, Warning, View, Download, Share, Delete, Calendar, Files, Upload
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      listSortType: 'latest',
      pageSize: 10,
      currentPage: 1,
      total: 0,
      reportDialogVisible: false,
      generateDialogVisible: false,
      currentReport: null,
      activeTab: 'summary',
      generating: false,

      // 筛选表单
      filterForm: {
        selectedMonth: '',
        type: '',
        reportStatus: ''
      },

      // 报告统计
      reportStats: {
        total: 0,
        thisYear: 0,
        generating: 0,
        failed: 0
      },

      // 报告列表数据
      reportList: [],

      // 年份选项
      yearOptions: [],

      // 月份选项
      monthOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      
      // 生成报告表单
      generateForm: {
        title: '',
        type: 'finance',
        dataSource: 'excel', // 数据来源：database 或 excel
        excelFile: null, // Excel文件
        queryYear: new Date().getFullYear(),
        compareYear: new Date().getFullYear() - 1,
        startMonth: 1,
        endMonth: new Date().getMonth() + 1
      },
      
      // 表单验证规则
      generateRules: {
        title: [
          { required: true, message: '请输入报告标题', trigger: 'blur' }
        ],
        dataSource: [
          { required: true, message: '请选择数据来源', trigger: 'change' }
        ],
        excelFile: [
          {
            validator: (rule, value, callback) => {
              if (this.generateForm.dataSource === 'excel' && !value) {
                callback(new Error('请上传Excel文件'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        queryYear: [
          { required: true, message: '请选择查询年份', trigger: 'change' }
        ],
        startMonth: [
          { required: true, message: '请选择开始月份', trigger: 'change' }
        ],
        endMonth: [
          { required: true, message: '请选择结束月份', trigger: 'change' }
        ]
      }
    }
  },

  mounted() {
    this.initYearOptions()
    this.loadReportList()
    this.loadStatistics()
  },

  beforeUnmount() {
    // 清理文件URL对象
    this.cleanupFileUrl()
    // 清理全局下载函数
    if (window.downloadCurrentFile) {
      delete window.downloadCurrentFile
    }
  },

  watch: {
    // 监听搜索关键词变化
    searchKeyword: {
      handler() {
        this.currentPage = 1
        this.loadReportList()
      },
      deep: true
    },

    // 监听筛选条件变化
    filterForm: {
      handler() {
        this.currentPage = 1
        this.loadReportList()
      },
      deep: true
    }
  },
  computed: {
    filteredReportList() {
      return this.reportList
    }
  },
  methods: {
    // 清理文件URL对象
    cleanupFileUrl() {
      if (this.currentReport && this.currentReport.fileUrl) {
        window.URL.revokeObjectURL(this.currentReport.fileUrl)
        this.currentReport.fileUrl = null
      }
    },

    // 对话框关闭处理
    handleDialogClose() {
      this.cleanupFileUrl()
      // 清理全局下载函数
      if (window.downloadCurrentFile) {
        delete window.downloadCurrentFile
      }
    },

    // 初始化年份选项
    initYearOptions() {
      const currentYear = new Date().getFullYear()
      this.yearOptions = []
      for (let i = currentYear; i >= currentYear - 10; i--) {
        this.yearOptions.push(i)
      }
    },

    // 加载报告列表
    async loadReportList() {
      try {
        this.loading = true
        const params = {
          current: this.currentPage,
          size: this.pageSize,
          title: this.searchKeyword,
          type: this.filterForm.type,
          reportStatus: this.filterForm.reportStatus
        }

        const response = await getReportList(params)
        if (response.data.success) {
          this.reportList = response.data.data.records || []
          this.total = response.data.data.total || 0
        }
      } catch (error) {
        console.error('加载报告列表失败:', error)
        this.$message.error('加载报告列表失败')
      } finally {
        this.loading = false
      }
    },

    // 加载统计信息
    async loadStatistics() {
      try {
        const response = await getReportStatistics()
        if (response.data.success) {
          this.reportStats = response.data.data || {}
        }
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },

    // 生成新报告
    generateReport() {
      this.generateDialogVisible = true
      this.generateForm = {
        title: '',
        type: 'finance',
        dataSource: 'excel',
        excelFile: null,
        queryYear: new Date().getFullYear(),
        compareYear: new Date().getFullYear() - 1,
        startMonth: 1,
        endMonth: new Date().getMonth() + 1
      }
    },
    
    // 提交生成报告
    async submitGenerate() {
      try {
        const valid = await this.$refs.generateFormRef.validate()
        if (valid) {
          this.generating = true

          // 同期年份
          this.generateForm['compareYear'] = this.generateForm['queryYear'] - 1

          let response
          if (this.generateForm.dataSource === 'excel') {
            // 使用Excel数据生成报告
            const formData = new FormData()
            formData.append('file', this.generateForm.excelFile)
            formData.append('title', this.generateForm.title)
            formData.append('queryYear', this.generateForm.queryYear)
            formData.append('compareYear', this.generateForm.compareYear)
            formData.append('startMonth', this.generateForm.startMonth)
            formData.append('endMonth', this.generateForm.endMonth)

            response = await uploadExcelData(formData)
          } else {
            // 使用数据库数据生成报告
            response = await generateReport(this.generateForm)
          }

          if (response.data.success) {
            this.generating = false
            this.generateDialogVisible = false
            this.$message.success('报告生成任务已提交，请稍后查看结果')
            this.loadReportList()
            this.loadStatistics()
          } else {
            this.$message.error(response.data.msg || '生成报告失败')
          }
        }
      } catch (error) {
        console.error('生成报告失败:', error)
        this.$message.error('生成报告失败')
      } finally {
        this.generating = false
      }
    },
    
    // 查看报告详情 - 改为下载文件预览
    async viewReport(row) {
      try {
        if (row.reportStatus !== 'completed') {
          this.$message.warning('报告尚未生成完成，无法预览')
          return
        }

        // 显示加载状态
        this.$message.info('正在加载预览...')

        // 尝试获取PDF预览
        let previewUrl = null
        let fileName = row.fileName || `${row.title}.docx`
        let fileExtension = fileName.split('.').pop().toLowerCase()

        try {
          // 首先尝试获取PDF预览（后端将Word转换为PDF）
          const previewResponse = await previewReport(row.id)
          const pdfBlob = new Blob([previewResponse.data], { type: 'application/pdf' })
          previewUrl = window.URL.createObjectURL(pdfBlob)
          fileExtension = 'pdf' // 标记为PDF用于预览
        } catch (previewError) {
          console.warn('PDF预览失败，尝试原文件预览:', previewError)
          // 如果PDF预览失败，回退到原文件
          const response = await downloadReport(row.id)
          const blob = new Blob([response.data])
          previewUrl = window.URL.createObjectURL(blob)
        }

        // 设置当前报告信息
        this.currentReport = {
          ...row,
          fileName: fileName,
          fileUrl: previewUrl,
          fileExtension: fileExtension
        }

        // 根据文件类型生成预览内容
        if (fileExtension === 'pdf') {
          // PDF文件使用iframe预览（包括转换后的PDF）
          this.currentReport.templateContent = `
            <div style="width: 100%; height: 700px; border: 1px solid #ddd; border-radius: 4px; background: #f5f5f5;">
              <iframe src="${previewUrl}#zoom=130" width="100%" height="100%" frameborder="0" style="border-radius: 4px; background: white;">
                <div style="text-align: center; padding: 40px;">
                  <p>您的浏览器不支持PDF预览</p>
                  <button onclick="window.open('${previewUrl}', '_blank')" style="
                    background: #409EFF;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                  ">在新窗口打开</button>
                </div>
              </iframe>
            </div>
          `
          // 设置下载原文件函数
          window.downloadOriginalFile = async () => {
            try {
              const response = await downloadReport(row.id)
              const blob = new Blob([response.data])
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              link.download = fileName
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              window.URL.revokeObjectURL(url)
              this.$message.success('下载成功')
            } catch (error) {
              this.$message.error('下载失败')
            }
          }
        } else if (['doc', 'docx'].includes(fileExtension)) {
          // Word文档显示提示信息和下载链接
          this.currentReport.templateContent = `
            <div style="text-align: center; padding: 40px;">
              <div style="font-size: 48px; color: #409EFF; margin-bottom: 20px;">
                <i class="el-icon-document"></i>
              </div>
              <h3 style="color: #303133; margin-bottom: 16px;">Word文档预览</h3>
              <p style="color: #606266; margin-bottom: 24px;">
                文件名：${fileName}<br/>
                PDF预览服务暂时不可用，请下载原文件查看
              </p>
              <button onclick="window.downloadCurrentFile()" style="
                background: #409EFF;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
              ">
                <i class="el-icon-download"></i> 下载文件
              </button>
            </div>
          `
          // 设置全局下载函数
          window.downloadCurrentFile = () => {
            const link = document.createElement('a')
            link.href = previewUrl
            link.download = fileName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.$message.success('下载成功')
          }
        } else {
          // 其他文件类型显示通用预览
          this.currentReport.templateContent = `
            <div style="text-align: center; padding: 40px;">
              <div style="font-size: 48px; color: #909399; margin-bottom: 20px;">
                <i class="el-icon-files"></i>
              </div>
              <h3 style="color: #303133; margin-bottom: 16px;">文件预览</h3>
              <p style="color: #606266; margin-bottom: 24px;">
                文件名：${fileName}<br/>
                文件类型：${fileExtension.toUpperCase()}
              </p>
              <button onclick="window.open('${previewUrl}', '_blank')" style="
                background: #409EFF;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                margin-right: 12px;
              ">
                <i class="el-icon-view"></i> 在新窗口打开
              </button>
              <button onclick="window.downloadCurrentFile()" style="
                background: #67C23A;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
              ">
                <i class="el-icon-download"></i> 下载文件
              </button>
            </div>
          `
          // 设置全局下载函数
          window.downloadCurrentFile = () => {
            const link = document.createElement('a')
            link.href = previewUrl
            link.download = fileName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.$message.success('下载成功')
          }
        }

        // 显示对话框
        this.reportDialogVisible = true
        this.activeTab = 'summary'

        // 刷新列表以更新下载次数
        this.loadReportList()
      } catch (error) {
        console.error('预览报告失败:', error)
        this.$message.error('预览报告失败')
      }
    },

    // 下载报告
    async downloadReport(row) {
      try {
        if (row.reportStatus !== 'completed') {
          this.$message.warning('报告尚未生成完成，无法下载')
          return
        }

        const response = await downloadReport(row.id)

        // 创建下载链接
        const blob = new Blob([response.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = row.fileName || `${row.title}.docx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('下载成功')

        // 刷新列表以更新下载次数
        this.loadReportList()
      } catch (error) {
        console.error('下载报告失败:', error)
        this.$message.error('下载报告失败')
      }
    },
    
    // 分享报告
    shareReport(row) {
      this.$message.success('分享链接已复制到剪贴板')
    },
    
    // 删除报告
    deleteReport(row) {
      this.$confirm('确认删除此报告？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await removeReports(row.id.toString())
          if (response.data.success) {
            this.$message.success('删除成功')
            this.loadReportList()
            this.loadStatistics()
          } else {
            this.$message.error(response.data.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除报告失败:', error)
          this.$message.error('删除报告失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    // 重试生成
    async retryGenerate(row) {
      try {
        const response = await retryGenerateReport(row.id)
        if (response.data.success) {
          this.$message.success('重新生成任务已提交')
          this.loadReportList()
          this.loadStatistics()
        } else {
          this.$message.error(response.data.msg || '重试失败')
        }
      } catch (error) {
        console.error('重试生成报告失败:', error)
        this.$message.error('重试生成报告失败')
      }
    },
    
    // 导出报告
    async exportReport(format) {
      if (!this.currentReport) {
        this.$message.error('请先选择要导出的报告')
        return
      }

      try {
        if (format === 'pdf') {
          this.$message.info('正在准备PDF文件...')

          // 下载PDF文件
          const response = await downloadPdfReport(this.currentReport.id)

          // 创建下载链接
          const blob = new Blob([response.data], { type: 'application/pdf' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = this.currentReport.pdfFileName || `${this.currentReport.title}.pdf`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          this.$message.success('PDF导出成功')
        } else {
          this.$message.success(`正在导出${format.toUpperCase()}格式报告...`)
        }
      } catch (error) {
        console.error('导出报告失败:', error)
        this.$message.error('导出报告失败')
      }
    },
    
    // 筛选和刷新
    handleFilter() {
      // 查询逻辑
    },
    
    resetFilter() {
      this.filterForm.selectedMonth = '';
    },
    
    refreshReports() {
      this.loadReportList()
      this.loadStatistics()
      this.$message.success('刷新成功')
    },
    
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadReportList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadReportList()
    },
    
    // 工具方法
    getReportTypeTag(type) {
      const typeMap = {
        finance: 'primary',
        tax: 'success',
        expense: 'warning',
        budget: 'info',
        risk: 'danger'
      }
      return typeMap[type] || 'info'
    },
    
    getStatusTag(status) {
      const statusMap = {
        completed: 'success',
        generating: 'warning',
        failed: 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    getPriorityTag(priority) {
      const priorityMap = {
        high: 'danger',
        medium: 'warning',
        low: 'info'
      }
      return priorityMap[priority] || 'info'
    },

    // 获取数据来源标签类型
    getDataSourceTag(dataSource) {
      const dataSourceMap = {
        database: 'primary',
        excel: 'success'
      }
      return dataSourceMap[dataSource] || 'info'
    },

    // 获取数据来源名称
    getDataSourceName(dataSource) {
      const dataSourceMap = {
        database: '数据库',
        excel: 'Excel'
      }
      return dataSourceMap[dataSource] || '未知'
    },

    // 数据来源变化处理
    handleDataSourceChange(value) {
      if (value === 'database') {
        this.generateForm.excelFile = null
        this.$refs.uploadRef && this.$refs.uploadRef.clearFiles()
      }
    },

    // 文件选择处理
    handleFileChange(file) {
      this.generateForm.excelFile = file.raw
    },

    // 文件移除处理
    handleFileRemove() {
      this.generateForm.excelFile = null
    },

    // 文件上传前验证
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }
      return false // 阻止自动上传，手动处理
    },

    // 下载Excel文件
    async downloadExcel(row) {
      try {
        this.$message.info('正在下载Excel文件...')
        const response = await downloadExcelFile(row.id)

        // 创建下载链接
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 设置文件名
        const fileName = row.excelFileName || '表样.xlsx'
        link.download = fileName

        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('Excel文件下载成功')
      } catch (error) {
        console.error('下载Excel文件失败:', error)
        this.$message.error('下载Excel文件失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.filter-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  display: flex;
  align-items: center;
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    
    &.finance {
      background: #e8f4fd;
      color: #409eff;
    }
    
    &.success {
      background: #e8f8f1;
      color: #67c23a;
    }
    
    &.warning {
      background: #fdf6ec;
      color: #e6a23c;
    }
    
    &.danger {
      background: #fef0f0;
      color: #f56c6c;
    }
  }
  
  .stat-content {
    flex: 1;
    
    .stat-title {
      color: #909399;
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .stat-value {
      color: #303133;
      font-size: 24px;
      font-weight: bold;
    }
  }
}

.report-list-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  
  .panel-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h4 {
      margin: 0;
      color: #303133;
    }
    
    .panel-actions {
      display: flex;
      align-items: center;
    }
  }
}

.report-title {
  display: flex;
  align-items: center;
  
  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
  
  .title-link {
    color: #409eff;
    cursor: pointer;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.report-detail-template {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .template-header {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #ebeef5;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    text-align: center;

    .header-title-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      h2 {
        margin: 0;
        flex: 1;
        text-align: center;
      }

      .template-actions {
        display: flex;
        gap: 10px;
        flex-shrink: 0;
      }
    }
  }

  .template-content {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .template-html {
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    padding: 32px 32px 32px 32px;
    box-sizing: border-box;
    text-align: left;
    font-size: 16px;
    color: #303133;
    max-height: 70vh;
    overflow: hidden;
    display: block;
  }
}

.report-recommendations {
  .recommendation-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    
    .rec-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      h5 {
        margin: 0;
        color: #303133;
      }
    }
    
    .rec-description {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }
    
    .rec-actions {
      margin-bottom: 15px;
      
      h6 {
        margin: 0 0 10px 0;
        color: #303133;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          color: #606266;
          margin-bottom: 5px;
        }
      }
    }
    
    .rec-impact {
      .impact-label {
        color: #909399;
      }
      
      .impact-value {
        color: #303133;
        font-weight: bold;
      }
    }
  }
}

.el-dialog {
  max-width: 820px !important;
}
</style>

