/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service;

import org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity;
import org.springblade.modules.yjzb.pojo.entity.BusinessKnowledgeEntity;
import org.springblade.modules.yjzb.pojo.vo.BusinessKnowledgeVO;
import org.springblade.core.mp.base.BaseService;

/**
 * 指标AI解读分析 服务类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-10
 */
public interface IIndicatorAiAnalysisService extends BaseService<IndicatorAiAnalysisEntity> {

    /**
     * 重新开始指定指标数据的AI分析：
     * - 使用指标数据的数值作为输入启动Dify工作流
     * - 写入一条AI解读分析记录（状态为RUNNING，保存workflowRunId与输入参数）
     * - 启动后台轮询任务每3分钟查询一次执行结果，完成后回写结果与状态
     *
     * @param indicatorValueId 指标数据ID（yjzb_indicator_values.id）
     * @return 新增的AI解读分析记录ID，失败返回null
     */
    Long restartAnalysisForIndicatorValue(Long indicatorValueId);

    /**
     *
     *
     * @param indicatorValueId 指标数据ID
     * @param systemPrompt     自定义提示词（写入 inputs.systemPrompt），为空则使用默认
     * @return 新增AI解读记录ID
     */
    Long restartAnalysisForIndicatorValueWithPrompt(Long indicatorValueId, String systemPrompt);

    /**
     * 获取指标预测分析的提示词预览（含业务规则替换）
     * 
     * @param indicatorId 指标ID
     * @param period      期间
     * @return 完整提示词内容
     */
    String buildPromptPreview(Long indicatorId, String period);

    /**
     * 获取指标关联的预测类业务知识列表
     *
     * @param indicatorId 指标ID
     * @return 业务知识条目列表
     */
    java.util.List<String> getBusinessRulesList(Long indicatorId);

    /**
     * 获取指标关联的业务知识详细信息（按分类分组）
     *
     * @param indicatorId 指标ID
     * @return 按分类分组的业务知识
     */
    java.util.Map<String, java.util.List<BusinessKnowledgeVO>> getBusinessKnowledgeByCategory(Long indicatorId);

    /**
     * 保存或更新业务知识
     *
     * @param businessKnowledge 业务知识实体
     * @return 是否成功
     */
    boolean saveOrUpdateBusinessKnowledge(BusinessKnowledgeEntity businessKnowledge);

    /**
     * 删除业务知识
     *
     * @param id 业务知识ID
     * @return 是否成功
     */
    boolean deleteBusinessKnowledge(Long id);

    /**
     * 获取业务知识分类列表
     *
     * @param indicatorId 指标ID
     * @return 分类列表
     */
    java.util.List<String> getBusinessKnowledgeCategories(Long indicatorId);

    /**
     * 查询指定指标数据（通过 indicatorValueId 定位 indicatorId+period）下最新一条AI解读分析记录
     *
     * @param indicatorValueId 指标数据ID
     * @return 最新的AI解读分析记录，可能为null
     */
    IndicatorAiAnalysisEntity getLatestByIndicatorValueId(Long indicatorValueId);

    /**
     * 启动当月某类型的总体AI解读（以 indicatorTypeId 作为存储的 indicatorId 记录）
     * - 聚合同类型当月所有指标数据、当年累计与年度预算等，调用Dify（blocking）
     * - 将outputs.text写入结果，状态置为COMPLETED
     *
     * @param indicatorTypeId 指标类型ID
     * @param period          月份 YYYY-MM
     * @return 新建AI解读记录ID；失败返回null
     */
    Long restartTypeMonthlyAnalysis(Long indicatorTypeId, String period);

    /**
     * 按 indicatorId + period 查询最新AI解读记录（用于类型级别查询）
     */
    IndicatorAiAnalysisEntity getLatestByIndicatorAndPeriod(Long indicatorId, String period);

    /**
     * 费用管理总览AI分析（类型级别）：传入 indicatorTypeId 与 period，聚合当月与当年累计数据并触发AI分析
     */
    Long restartExpenseOverviewAnalysis(Long indicatorTypeId, String period);

    /**
     * 资产负债总览AI分析（类型级别）：传入 indicatorTypeId 与 period，聚合当月与当年累计数据并触发AI分析
     */
    Long restartBalanceOverviewAnalysis(Long indicatorTypeId, String period);

    /**
     * 利润总览AI分析（类型级别）：传入 indicatorTypeId 与 period，聚合当月与当年累计数据并触发AI分析
     */
    Long restartProfitOverviewAnalysis(Long indicatorTypeId, String period);

    /**
     * 新：按指标ID与期间(YYYY-MM)存储“单月AI解读结果”（无think，仅正文），用于月度卡片展示。
     */
    void saveMonthlyAiInterpretation(Long indicatorId, String period, String resultText);
}
