package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springblade.modules.yjzb.entity.ExpenseForecast;

import java.util.List;

/**
 * 办公费用预测Mapper接口
 *
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */
@Mapper
public interface ExpenseForecastMapper extends BaseMapper<ExpenseForecast> {

    /**
     * 获取指定指标和期间的总预测值
     */
    @Select("SELECT * FROM yjzb_expense_forecast WHERE indicator_id = #{indicatorId} AND forecast_period = #{period} AND forecast_type = 'TOTAL' ORDER BY create_time DESC LIMIT 1")
    ExpenseForecast getTotalForecastByPeriod(@Param("indicatorId") Long indicatorId, @Param("period") String period);

    /**
     * 获取指定指标和期间的所有分类预测值
     */
    @Select("SELECT * FROM yjzb_expense_forecast WHERE indicator_id = #{indicatorId} AND forecast_period = #{period} AND forecast_type != 'TOTAL' ORDER BY create_time DESC")
    List<ExpenseForecast> getCategoryForecastsByPeriod(@Param("indicatorId") Long indicatorId,
            @Param("period") String period);

    /**
     * 获取指定指标、期间、分类的预测值
     */
    @Select("SELECT * FROM yjzb_expense_forecast WHERE indicator_id = #{indicatorId} AND forecast_period = #{period} AND forecast_type = #{category} ORDER BY create_time DESC LIMIT 1")
    ExpenseForecast getCategoryForecastByPeriod(@Param("indicatorId") Long indicatorId,
            @Param("period") String period,
            @Param("category") String category);

    /**
     * 获取指定指标的最新总预测值
     */
    @Select("SELECT * FROM yjzb_expense_forecast WHERE indicator_id = #{indicatorId} AND forecast_type = 'TOTAL' ORDER BY create_time DESC LIMIT 1")
    ExpenseForecast getLatestTotalForecast(@Param("indicatorId") Long indicatorId);

    /**
     * 获取指定指标的最新分类预测值
     */
    @Select("SELECT * FROM yjzb_expense_forecast WHERE indicator_id = #{indicatorId} AND forecast_type != 'TOTAL' ORDER BY create_time DESC")
    List<ExpenseForecast> getLatestCategoryForecasts(@Param("indicatorId") Long indicatorId);

}
